{"name": "super-app-showcase", "version": "0.0.1", "private": true, "license": "MIT", "scripts": {"adbreverse": "adb reverse tcp:3000 tcp:3000 && adb reverse tcp:8081 tcp:8081 && adb reverse tcp:9000 tcp:9000 && adb reverse tcp:9001 tcp:9001 && adb reverse tcp:9002 tcp:9002 && adb reverse tcp:9003 tcp:9003 && adb reverse tcp:9004 tcp:9004", "align-deps": "pnpm -r align-deps", "build:android:prod": "cd packages/host/android && ./gradlew bundle", "bundle:auth:ios": "pnpm --filter auth bundle:ios", "bundle:dashboard:android": "pnpm --filter dashboard bundle:android", "bundle:dashboard:ios": "pnpm --filter dashboard bundle:ios", "bundle:hn:host:android": "pnpm --filter host bundle:HN:android", "bundle:qn:host:android": "pnpm --filter host bundle:QN:android", "bundle:qnc:host:android": "pnpm --filter host bundle:QNC:android", "bundle:ios": "pnpm --filter host run bundle:ios", "check-deps": "pnpm -r check-deps", "copy:config:dev": "node scripts/copy.js config dev", "copy:config:uat": "node scripts/copy.js config uat", "copy:config:prod": "node scripts/copy.js config prod", "copy:dev": "rm -fr ./packages/host/android/gradle.properties && rm -fr packages/host/index.js && cp packages/host/index.dev.js packages/host/index.js && cp ./packages/host/android/gradle.properties.dev ./packages/host/android/gradle.properties", "copy:prod": "rm -fr ./packages/host/android/gradle.properties && rm -fr ./packages/host/index.js && cp ./packages/host/index.prod.js ./packages/host/index.js && cp ./packages/host/android/gradle.properties.prod ./packages/host/android/gradle.properties", "lint": "pnpm -r lint", "open:xcode": "open packages/host/ios/host.xcworkspace", "open:ios": "open -a Simulator --args -CurrentDeviceUDID \"20A7CE34-E96E-4E54-9200-F6ECB60AB86F\"", "pods": "pnpm -r pods", "pods:update": "pnpm -r pods:update", "restart:hn:android": "adb shell am force-stop online.dean06.superapp && adb shell monkey -p online.dean06.superapp -c android.intent.category.LAUNCHER 1", "restart:qn:android": "adb shell am force-stop vn.gov.quangninh.dichvucong.congchuc && adb shell monkey -p vn.gov.quangninh.dichvucong.congchuc -c android.intent.category.LAUNCHER 1", "restart:qnc:android": "adb shell am force-stop vn.gov.quangninh.dichvucong && adb shell monkey -p vn.gov.quangninh.dichvucong -c android.intent.category.LAUNCHER 1", "run:dashboard:android": "pnpm --filter dashboard android", "run:qn:dashboard:ios": "pnpm --filter host replace-env QN && pnpm --filter host ios:qn", "run:hn:dashboard:ios": "pnpm --filter host replace-env HN && pnpm --filter host ios:hn", "run:hn:host:android": "pnpm --filter host clear-main && pnpm --filter host replace-env HN && pnpm --filter host android", "run:qn:host:android": "pnpm --filter host clear-main && pnpm --filter host replace-env QN && pnpm --filter host android", "run:qnc:host:android": "pnpm --filter host clear-main && pnpm --filter host replace-env QNC && pnpm --filter host android", "run:hn:host:android:release": "pnpm --filter host copy:android-client HN && pnpm --filter host android:release", "run:qn:host:android:release": "pnpm --filter host copy:android-client QN && pnpm --filter host android:release", "run:qnc:host:android:release": "pnpm --filter host copy:android-client QNC && pnpm --filter host android:release", "run:hn:host:ios": "pnpm copy:dev && pnpm --filter host run ios:hn", "run:qn:host:ios": "pnpm copy:dev && pnpm --filter host run ios:qn", "run:qnc:host:ios": "pnpm copy:dev && pnpm --filter host run ios:qnc", "start:m": "pnpm copy:dev && mprocs", "start": "node scripts/start.js", "start:hn:auth:dev": "node scripts/start.js HN auth dev", "start:hn:auth:uat": "node scripts/start.js HN auth uat", "start:hn:auth:prod": "node scripts/start.js HN auth prod", "start:hn:citizen:dev": "node scripts/start.js HN citizen dev", "start:hn:citizen:uat": "node scripts/start.js HN citizen uat", "start:hn:citizen:prod": "node scripts/start.js HN citizen prod", "start:hn:dashboard": "node scripts/start.js HN dashboard", "start:hn:dashboard:dev": "node scripts/start.js HN dashboard dev", "start:hn:dashboard:uat": "node scripts/start.js HN dashboard uat", "start:hn:dashboard:prod": "node scripts/start.js HN dashboard prod", "start:hn:host": "node scripts/start.js HN host", "start:qn:auth:dev": "node scripts/start.js QN auth dev", "start:qn:auth:uat": "node scripts/start.js QN auth uat", "start:qn:auth:prod": "node scripts/start.js QN auth prod", "start:qn:citizen:dev": "node scripts/start.js QN citizen dev", "start:qn:citizen:uat": "node scripts/start.js QN citizen uat", "start:qn:citizen:prod": "node scripts/start.js QN citizen prod", "start:qn:dashboard:dev": "node scripts/start.js QN dashboard dev", "start:qn:dashboard:uat": "node scripts/start.js QN dashboard uat", "start:qn:dashboard:prod": "node scripts/start.js QN dashboard prod", "start:qn:host": "node scripts/start.js QN host", "start:qnc:host": "node scripts/start.js QNC host", "start:catalog-server": "node scripts/start.js HN catalog-server", "start:standalone:dashboard": "node scripts/start.js dashboard --standalone", "test": "pnpm -r test", "typecheck": "pnpm -r typecheck", "install:android": "cd packages/host/android/app/build/outputs/apk/debug && adb install app-debug.apk", "install:android:prod": "./scripts/abb-to-apks", "run-app": "node scripts/run-app.js", "run-app-example": "pnpm run-app QNC host android local"}, "dependencies": {"@changesets/cli": "^2.27.8", "minio": "8.0.2", "mprocs": "^0.7.1", "nodemon": "3.1.7", "react-native-permissions": "5.2.1", "semver": "7.6.3"}, "packageManager": "pnpm@9.7.0", "engines": {"node": ">=18"}, "engineStrict": true, "devDependencies": {"@ac-mobile/app-store-cli": "^0.1.26"}}