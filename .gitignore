.yarn/*

node_modules

/*.tgz
build/

.idea/
ac-store.json

# Code Signing
code-signing.pem
code-signing.pem.pub
tmp
tmp/index.html
# mprocs
mprocs.log
# packages/host/index.js
packages/host/android/gradle.properties.prod
packages/host/android/gradle.properties
packages/host/ios/fastlane/builds
packages/host/ios/fastlane/archives.xcarchive
packages/host/ios/fastlane/dist
packages/host/miniapp_config_param.js
packages/host/android/app/src/main/res/**

packages/host/android/app/google-services.json
packages/host/android/app/src/main/AndroidManifest.xml
packages/host/ios/host/Images.xcassets
packages/host/miniapp_config_param.js
# packages/host/miniapp_env.js
packages/host/android/app/google-services.json
packages/host/android/app/build.gradle
packages/host/android/app/src/main/java/**
# packages/*/src/api/api-config.ts

# Ignore all files in packages/auth/src/api except HN and QN folders
packages/auth/src/api/*
!packages/auth/src/api/
!packages/auth/src/api/HN/
!packages/auth/src/api/QN/

# Ignore all files in packages/auth/src/assets/inlineAssets except HN and QN folders
packages/auth/src/assets/inlineAssets/*
!packages/auth/src/assets/inlineAssets/
!packages/auth/src/assets/inlineAssets/HN/
!packages/auth/src/assets/inlineAssets/QN/

# Ignore all files in packages/dashboard/src/api except HN and QN folders
packages/dashboard/src/api/*
!packages/dashboard/src/api/
!packages/dashboard/src/api/HN/
!packages/dashboard/src/api/QN/

# Ignore all files in packages/dashboard/src/assets/inlineAssets except HN and QN folders
packages/dashboard/src/assets/inlineAssets/*
!packages/dashboard/src/assets/inlineAssets/
!packages/dashboard/src/assets/inlineAssets/HN/
!packages/dashboard/src/assets/inlineAssets/QN/
releases/
*.dSYM.zip

packages/host/ios/vendor
packages/dashboard.diff

