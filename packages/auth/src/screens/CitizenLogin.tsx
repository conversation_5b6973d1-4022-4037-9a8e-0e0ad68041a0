import axios from 'axios';
import {useAuthStore, useConfigStore} from '@ac-mobile/common';
import React, {useRef, useState} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ImageBackground,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
  Alert,
} from 'react-native';
import {useNavigation, NavigationProp} from '@react-navigation/native';
import {AuthStackParamList} from '../navigation/AuthNavigator';
import {Button} from 'react-native-paper';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {CITIZEN_API, DVC_HN_URL} from '../api/api-config';
import {ReloadAppButton} from '../components/ReloadAppButton';
import {useTheme} from 'react-native-paper';
import WebView from 'react-native-webview';

const screenHeight = Dimensions.get('window').height;

const skylineIllustration = require('../assets/inlineAssets/bg-city.png');

const CitizenLoginScreen = () => {
  const theme = useTheme();
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const {setAuth} = useAuthStore();
  const {setConfig} = useConfigStore();
  const navigation = useNavigation<NavigationProp<AuthStackParamList>>();

  const webViewRef = useRef<WebView>(null);
  const [canGoBack, setCanGoBack] = useState(false);
  const [urlCreateAccount, setUrlCreateAccount] = useState<string | undefined>(
    undefined,
  );

  const handleLogin = async () => {
    if (!username || !password) {
      Alert.alert('Vui lòng nhập tài khoản và mật khẩu');
      return;
    }
    setLoading(true);
    try {
      const res = await axios.post(
        `${CITIZEN_API}/requester-biz/api/v1/auth/get-token`,
        {
          username,
          password,
        },
        {
          headers: {
            accept: 'application/json',
            'Content-Type': 'application/json',
            'X-Requester-Service-Id': '',
            'X-Requester-Service-Key-Id': '',
            'X-Requester-Service-Key-Secret': '',
          },
          timeout: 10000,
        },
      );

      if (res.data && res.data.data) {
        // Use auth store instead of config store
        setAuth({
          user: undefined,
          accessToken: res.data.data.access_token,
          refreshToken: res.data.data.refresh_token,
        });
        Alert.alert('Đăng nhập thành công');
      } else {
        Alert.alert('Đăng nhập thất bại', 'Vui lòng thử lại');
      }
    } catch (err: any) {
      console.log({err});
      if (
        err.response &&
        err.response.data &&
        err.response.data.error_description
      ) {
        // Alert.alert('Đăng nhập thất bại', err.response.data.error_description);
        Alert.alert('Đăng nhập thất bại', 'Thôn tin đăng nhập không chính xác');
      } else {
        Alert.alert('Đăng nhập thất bại', 'Có lỗi xảy ra, vui lòng thử lại');
      }
    } finally {
      setLoading(false);
    }
  };

  if (urlCreateAccount) {
    return (
      <SafeAreaView style={styles.flexContainer}>
        <View style={styles.webviewHeader}>
          <TouchableOpacity
            onPress={() => {
              if (canGoBack && webViewRef.current) {
                webViewRef.current.goBack();
              } else {
                setUrlCreateAccount(undefined);
              }
            }}
            style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#000" />
          </TouchableOpacity>
          <Text style={styles.webviewTitle}>Tạo mới tài khoản</Text>
          <View style={{width: 24}} />
        </View>

        <WebView
          ref={webViewRef}
          source={{uri: urlCreateAccount}}
          startInLoadingState
          javaScriptEnabled
          domStorageEnabled
          style={styles.flexContainer}
          onNavigationStateChange={navState => {
            setCanGoBack(navState.canGoBack);
            if (navState.url === `${DVC_HN_URL}/dang-nhap-mobile-thanh-cong`) {
              setUrlCreateAccount(undefined);
              Alert.alert('Thành công', 'Tài khoản đã được tạo.');
            }
          }}
        />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      style={[styles.flexContainer, {backgroundColor: theme.colors.surface}]}>
      <KeyboardAvoidingView
        style={styles.flexContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          <ImageBackground
            source={skylineIllustration}
            style={[
              styles.headerIllustration,
              {backgroundColor: theme.colors.surface},
            ]}
            resizeMode="cover"
          />
          <View style={styles.reloadButtonContainer}>
            <ReloadAppButton />
          </View>
          <View
            style={[
              styles.loginFormCard,
              {backgroundColor: theme.colors.background},
            ]}>
            <Text style={[styles.titleText, {color: theme.colors.onSurface}]}>
              Đăng nhập tài khoản
            </Text>

            <View style={styles.inputFieldsContainer}>
              <TextInput
                style={[
                  styles.input,
                  {
                    borderColor: theme.colors.primary,
                    color: theme.colors.onSurface,
                  },
                ]}
                placeholder="Nhập số căn cước công dân"
                placeholderTextColor={theme.colors.onSurfaceVariant}
                keyboardType="phone-pad"
                value={username}
                onChangeText={setUsername}
                editable={!loading}
              />
              <View style={styles.passwordContainer}>
                <TextInput
                  style={[
                    styles.input,
                    styles.passwordInput,
                    {
                      borderColor: theme.colors.primary,
                      color: theme.colors.onSurface,
                    },
                  ]}
                  placeholder="Mật khẩu"
                  placeholderTextColor={theme.colors.onSurfaceVariant}
                  secureTextEntry={!passwordVisible}
                  value={password}
                  onChangeText={setPassword}
                  editable={!loading}
                />
                <TouchableOpacity
                  style={styles.eyeIcon}
                  onPress={() => setPasswordVisible(!passwordVisible)}
                  disabled={loading}>
                  <Ionicons
                    name={passwordVisible ? 'eye-off' : 'eye'}
                    size={24}
                    color={theme.colors.onSurfaceVariant}
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* <TouchableOpacity
              onPress={() => navigation.navigate('CitizenForgetPassword')}>
              <Text style={[styles.forgotPasswordText, {color: theme.colors.error}]}>Quên mật khẩu?</Text>
            </TouchableOpacity> */}

            <Button
              mode="contained"
              style={[styles.loginButton, loading && {opacity: 0.7}]}
              onPress={handleLogin}
              disabled={loading}>
              {loading ? 'Đang đăng nhập...' : 'Đăng nhập'}
            </Button>

            {/* Divider for clarity */}
            <View style={styles.dividerContainer}>
              <View
                style={[
                  styles.dividerLine,
                  {backgroundColor: theme.colors.outlineVariant},
                ]}
              />
              <Text
                style={[
                  styles.dividerText,
                  {color: theme.colors.onSurfaceVariant},
                ]}>
                Hoặc
              </Text>
              <View
                style={[
                  styles.dividerLine,
                  {backgroundColor: theme.colors.outlineVariant},
                ]}
              />
            </View>

            {/* Officer login button, visually separated */}
            <View style={styles.officerLoginContainer}>
              <Button
                mode="text"
                onPress={() => {
                  setUrlCreateAccount(`${DVC_HN_URL}/dang-nhap-mobile`);
                }}>
                <Text style={{color: theme.colors.primary}}>
                  Tạo mới tài khoản
                </Text>
              </Button>
            </View>

            <View style={styles.officerLoginContainer}>
              <Button
                mode="text"
                onPress={() => {
                  setConfig('loginMode', 'officer');
                  Alert.alert(
                    'Chuyển đổi chế độ',
                    'Đang chuyển sang chế độ đăng nhập công chức...',
                    [
                      {
                        text: 'OK',
                        onPress: () => {
                          navigation.navigate('SignIn');
                        },
                      },
                    ],
                    {cancelable: false},
                  );
                }}>
                <Text style={{color: theme.colors.primary}}>
                  Đăng nhập tài khoản công chức
                </Text>
              </Button>
            </View>
          </View>

          {/* Move footer to very bottom for clarity */}
          <View style={styles.footerContainer}>
            {/* <Text
              style={[
                styles.footerText,
                {color: theme.colors.onSurfaceVariant},
              ]}>
              Bản quyền thuộc về Ủy ban nhân dân thành phố Quảng Ninh
            </Text> */}
            <Text
              style={[
                styles.footerText,
                {color: theme.colors.onSurfaceVariant},
              ]}>
              [-{CITIZEN_API}-]
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  flexContainer: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
  },
  headerIllustration: {
    height: screenHeight * 0.28,
  },
  webviewButtonContainer: {
    padding: 24,
    alignItems: 'center',
  },
  webviewButton: {
    borderRadius: 8,
    paddingVertical: 14,
    paddingHorizontal: 20,
    alignItems: 'center',
    width: '100%',
  },
  webviewButtonText: {
    fontWeight: 'bold',
    fontSize: 16,
    textAlign: 'center',
  },
  loginFormCard: {
    flex: 1,
    borderRadius: 20,
    paddingHorizontal: 24,
    paddingTop: 32,
    marginTop: -20,
    elevation: 5,
    shadowOffset: {width: 0, height: -3},
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  titleText: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 24,
  },
  inputFieldsContainer: {
    gap: 16,
  },
  input: {
    borderRadius: 8,
    borderWidth: 1,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 14,
    height: 48,
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  passwordInput: {
    flex: 1,
  },
  eyeIcon: {
    position: 'absolute',
    right: 12,
  },
  forgotPasswordText: {
    fontSize: 14,
    textAlign: 'right',
    marginTop: 8,
    marginBottom: 24,
  },
  loginButton: {
    marginTop: 16,
    borderRadius: 8,
    paddingVertical: 4,
  },
  loginButtonText: {
    fontWeight: 'bold',
    fontSize: 16,
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 20,
    paddingHorizontal: 24,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#ccc', // will be overridden by theme
  },
  dividerText: {
    marginHorizontal: 8,
    fontSize: 13,
  },
  officerLoginContainer: {
    marginTop: 8,
    marginBottom: 8,
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  footerContainer: {
    marginTop: 32,
    marginBottom: 16,
    alignItems: 'center',
    justifyContent: 'flex-end',
    flex: 0,
  },
  registerLink: {
    textAlign: 'center',
    marginTop: 20,
    fontSize: 14,
  },
  registerLinkHighlight: {
    fontWeight: 'bold',
  },
  footerText: {
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 12,
    paddingHorizontal: 24,
  },
  reloadButtonContainer: {
    position: 'absolute',
    top: 40,
    right: 24,
    zIndex: 20,
  },
  buttonContainer: {
    paddingHorizontal: 16,
  },
  webviewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 50,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
  },
  backButton: {padding: 8},
  webviewTitle: {
    flex: 1,
    textAlign: 'center',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

export default CitizenLoginScreen;
