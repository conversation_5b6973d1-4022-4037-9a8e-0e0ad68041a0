import React from 'react';
import {View, Text, ScrollView, StyleSheet} from 'react-native';

const PrivacyScreen = () => {
  return (
    <ScrollView contentContainerStyle={styles.container}>
      <Text style={styles.title}>Chính sách Bảo mật</Text>
      <View style={styles.section}>
        <Text>
          Chào mừng bạn đến với trang web của chúng tôi. Chúng tôi tôn trọng
          quyền riêng tư của bạn và cam kết bảo vệ thông tin cá nhân của bạn.
          Chính sách Bảo mật này giải thích cách chúng tôi thu thập, sử dụng và
          bảo vệ thông tin cá nhân của bạn khi bạn sử dụng trang web này.
        </Text>
      </View>
      <Text style={styles.subTitle}>Thông tin Chúng tôi thu thập</Text>
      <View style={styles.section}>
        <Text>
          - Thông tin cá nhân cung cấp tự nguyện thông qua các biểu mẫu trên
          trang web, chẳng hạn như tên, địa chỉ email, số điện thoại và địa chỉ.
        </Text>
        <Text>
          - Thông tin về hoạt động của bạn trên trang web, bao gồm các thông tin
          về truy cập, trang xem và hành động khác.
        </Text>
        <Text>
          - Thông tin về thiết bị của bạn và phần mềm, chẳng hạn như địa chỉ IP,
          loại trình duyệt, thống kê, v.v.
        </Text>
      </View>
      <Text style={styles.subTitle}>Cách chúng tôi sử dụng thông tin</Text>
      <View style={styles.section}>
        <Text>
          Chúng tôi sử dụng thông tin thu thập để cung cấp và duy trì trang web
          này, để cải thiện trải nghiệm của người dùng, và để bảo vệ trang web
          và người dùng khỏi hoạt động bất hợp pháp.
        </Text>
      </View>
      <Text style={styles.subTitle}>Chia sẻ thông tin với bên thứ ba</Text>
      <View style={styles.section}>
        <Text>
          Chúng tôi có thể chia sẻ thông tin cá nhân của bạn với bên thứ ba khi
          cần thiết để cung cấp dịch vụ hoặc tuân thủ các yêu cầu pháp lý.
        </Text>
      </View>
      <Text style={styles.subTitle}>Bảo mật thông tin</Text>
      <View style={styles.section}>
        <Text>
          Chúng tôi cam kết bảo mật thông tin cá nhân của bạn và đã thực hiện
          các biện pháp bảo mật phù hợp để ngăn chặn truy cập trái phép, sử
          dụng, tiết lộ hoặc hủy hoại thông tin cá nhân của bạn.
        </Text>
      </View>
      <Text style={styles.subTitle}>Liên hệ chúng tôi</Text>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
  },
  subTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 15,
    marginBottom: 5,
  },
  section: {
    marginBottom: 15,
  },
});

export default PrivacyScreen;
