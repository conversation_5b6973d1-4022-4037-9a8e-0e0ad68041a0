/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import {Alert, Dimensions, StyleSheet, View} from 'react-native';
import {
  Button,
  MD3Colors,
  Text,
  TextInput,
  useTheme,
  Modal,
} from 'react-native-paper';
import {useAuthStore, CONFIG} from '@ac-mobile/common';
import {LoginAnimation} from '../lotties/LoginAnimation';
import TermsScreen from './TermsScreen';
import PrivacyScreen from './PrivacyScreen';

type LoginFormValues = {
  username: string;
  password: string;
};

const SignInScreen = () => {
  const {
    setAuth,
    setLoading,
    // loading,
  } = useAuthStore();
  const theme = useTheme();

  const [username, setUsername] = React.useState('');
  const [password, setPassword] = React.useState('');
  const [loadingLogin, setLoadingLogin] = React.useState(false);
  const [visibleTerm, setVisibleTerm] = React.useState(false);
  const [visiblePrivacy, setVisiblePrivacy] = React.useState(false);
  const [secureTextEntry, setSecureTextEntry] = React.useState(true);
  const containerStyle = {backgroundColor: 'white', padding: 20};
  const showModalTerm = () => setVisibleTerm(true);
  const hideModalTerm = () => setVisibleTerm(false);
  const showModalPrivacy = () => setVisiblePrivacy(true);
  const hideModalPrivacy = () => setVisiblePrivacy(false);

  // const processLoginWithToken = async (token: any) => {
  //   try {
  //     console.log(token, '');
  //   } catch (error) {
  //     setLoading('error');
  //     Alert.alert('Có lỗi xảy ra, vui lòng thử đăng nhập lại!');
  //   }
  // };

  // eslint-disable-next-line @typescript-eslint/no-shadow
  const onSubmit = async ({username, password}: LoginFormValues) => {
    setLoadingLogin(true);

    console.log(CONFIG, 'CONFIG');

    try {
      console.log('Login with', {username, password});
      setLoading('loading');
      setAuth({
        user: {
          preferred_username: 'Han Han',
          email: '<EMAIL>',
          name: 'Han Han Demo',
        } as any,
        accessToken: 'accessToken',
        refreshToken: 'refreshToken',
      });
      setLoading('success');
    } catch (error) {
      setLoading('error');
      console.log(error);
      Alert.alert('Thông tin đăng nhập không chính xác, vui lòng thử lại !!');
    }

    setLoadingLogin(false);
  };

  return (
    <View style={styles.container}>
      <LoginAnimation />
      <View style={{gap: 10, justifyContent: 'center', alignItems: 'center'}}>
        <Text
          style={{
            fontWeight: '500',
            fontSize: 28,
            color: theme.colors.primary,
          }}>
          Đăng nhập
        </Text>
        <Text>Mừng bạn trở lại!</Text>
        <View style={{width: Dimensions.get('screen').width - 80}}>
          <TextInput
            style={{marginBottom: 8}}
            label="Tên đăng nhập"
            onChangeText={(text: string) =>
              setUsername(text.trim().toLocaleLowerCase())
            }
            right={<TextInput.Icon icon="account" />}
          />
          <TextInput
            label="Mật khẩu"
            style={{marginBottom: 8}}
            onChangeText={(text: string) => setPassword(text)}
            secureTextEntry={secureTextEntry}
            right={
              <TextInput.Icon
                icon={secureTextEntry ? 'eye' : 'eye-off'}
                onPress={() => {
                  setSecureTextEntry(!secureTextEntry);
                }}
              />
            }
          />
        </View>
        <Button
          loading={loadingLogin}
          mode="contained"
          style={{width: Dimensions.get('screen').width - 80}}
          onPress={() => {
            if (username.length === 0) {
              Alert.alert('Vui lòng nhập tên đăng nhập');
              return;
            } else if (password.length === 0) {
              Alert.alert('Vui lòng nhập mật khẩu');
              return;
            }

            onSubmit({username, password});
          }}>
          Đăng nhập
        </Button>
        {/* <Text style={{marginTop: 8, marginBottom: 0}}>
              Hoặc đăng nhập với
            </Text> */}
        <View style={{display: 'flex', flexDirection: 'row'}}>
          {/* <IconButton
                size={28}
                loading={loading === 'loading' || loading === 'success'}
                mode="contained-tonal"
                icon={({size}: {size: number; color: string}) => {
                  return (
                    <View style={{width: size, height: size}}>
                      <GoogleIcon />
                    </View>
                  );
                }}
                onPress={() => {
                  signIn(ssoConfig);
                }}
              /> */}
          {/* <IconButton
            size={28}
            loading={loading === 'loading' || loading === 'success'}
            mode="contained-tonal"
            icon={({size}: {size: number; color: string}) => {
              return (
                <View style={{width: size, height: size}}>
                  <FacebookIcon />
                </View>
              );
            }}
            onPress={() => {
              signIn(ssoConfig);
            }}
          /> */}
        </View>
      </View>
      <View
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}>
        <Text
          style={{color: 'blue', margin: 20}}
          onPress={() => showModalPrivacy()}>
          Quyền riêng tư
        </Text>
        <Text
          style={{color: 'blue', margin: 20}}
          onPress={() => showModalTerm()}>
          Các điều khoản
        </Text>
      </View>

      <Modal
        visible={visibleTerm}
        onDismiss={hideModalTerm}
        contentContainerStyle={containerStyle}>
        <TermsScreen />
        <Button
          mode="contained"
          style={{marginBottom: 16}}
          onPress={() => hideModalTerm()}>
          Đồng ý
        </Button>
      </Modal>
      <Modal
        visible={visiblePrivacy}
        onDismiss={hideModalPrivacy}
        contentContainerStyle={containerStyle}>
        <PrivacyScreen />
        <Button
          mode="contained"
          style={{marginBottom: 16}}
          onPress={() => hideModalPrivacy()}>
          Đồng ý
        </Button>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  welcomeHeadline: {
    color: MD3Colors.primary20,
  },
  welcomeText: {
    padding: 16,
    paddingBottom: 32,
  },
});

export default SignInScreen;
