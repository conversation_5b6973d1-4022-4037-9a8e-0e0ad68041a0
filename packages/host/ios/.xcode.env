# This `.xcode.env` file is versioned and is used to source the environment
# used when running script phases inside Xcode.
# To customize your local environment, you can create an `.xcode.env.local`
# file that is not versioned.

# NODE_BINARY variable contains the PATH to the node executable.
#
# Customize the NODE_BINARY variable here.
# For example, to use nvm with brew, add the following line
# . "$(brew --prefix nvm)/nvm.sh" --no-use
export NODE_BINARY=$(command -v node)

export NODE_ENV=production
export SAS_CATALOG_SERVER_URL=https://super-app-showcase-catalog.vercel.app
export ST_SSO_AUTHORIZATION_ENDPOINT='https://auth.demo.pro/realms/demo/protocol/openid-connect/auth'
export ST_SSO_CLIENT_ID='demo'
export ST_SSO_CLIENT_SECRET='demo'
export ST_SSO_REALM='demo'
export ST_SSO_SCOPES='openid'
export ST_SSO_TOKEN_ENDPOINT='https://auth.demo.pro/realms/demo/protocol/openid-connect/token'
export ST_API_ENDPOINT='https://api.demo.pro/api'