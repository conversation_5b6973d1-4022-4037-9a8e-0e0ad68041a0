# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:ios)

platform :ios do
  desc "Push a new release build to the App Store"
  lane :release do
    increment_build_number(xcodeproj: "host.xcodeproj")
    build_app(workspace: "host.xcworkspace", scheme: "CongChucQNI")
    # upload_to_app_store
  end

  desc "Push a new beta build to TestFlight"
  lane :beta_qn do
    increment_build_number(xcodeproj: "host.xcodeproj")
    build_app(
      workspace: "host.xcworkspace",
      scheme: "CongChucQNI",
      export_method: "app-store",
      export_options: {
        provisioningProfiles: {
          "vn.gov.quangninh.dichvucong.congchuc" => "QN1G"
        }
      }
    )
    # upload_to_testflight
  end

  lane :beta_hn do
    increment_build_number(xcodeproj: "host.xcodeproj")
    build_app(
      workspace: "host.xcworkspace",
      scheme: "CongChucHNO",
      export_method: "app-store",
      export_options: {
        provisioningProfiles: {
          "vn.gov.hanoi.dichvucong.congchuc" => "distributecongchuchanoi"
        }
      }
    )
    # upload_to_testflight
  end
end
