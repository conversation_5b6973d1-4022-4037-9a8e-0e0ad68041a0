import { params } from './miniapp_config_param';
import { Platform } from 'react-native';
import { name as appName } from './app.json';
import { version as appVersion } from './package.json';
import { getContainers } from '@ac-mobile/common';

export const getAppContainers = async (environment) => {
  if (environment !== 'local') {

    const finalParams = {
      mainAppName: params.mainAppName,
      hostname: params.hostname,
      version: appVersion,
      platform: Platform.OS,
      appName: 'congchucqnh',
      environment,
    };

    const result = await getContainers(finalParams);
    console.log('getAppContainers', result, finalParams);
    return result;
    // return {
    //   "auth": "https://api-dvc.quangninh.gov.vn/dl-app-store-mini-apps/releases/production/auth-ed9f53e1-b4ab-43a4-9193-7930fc8f9fca/auth-ios@0.0.5/[name][ext]",
    //   "citizen": "https://api-dvc.quangninh.gov.vn/dl-app-store-mini-apps/releases/production/citizen-ed9f53e1-b4ab-43a4-9193-7930fc8f9fca/citizen-ios@0.0.25/[name][ext]",
    //   "dashboard": "https://api-dvc.quangninh.gov.vn/dl-app-store-mini-apps/releases/production/dashboard-ed9f53e1-b4ab-43a4-9193-7930fc8f9fca/dashboard-ios@0.0.2/[name][ext]"
    // };
  } else return { "auth": "http://localhost:9003/[name][ext]", "dashboard": "http://localhost:9002/[name][ext]", "citizen": "http://localhost:9004/[name][ext]" }
};
