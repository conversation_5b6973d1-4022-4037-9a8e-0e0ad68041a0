const { spawn } = require('child_process');
const path = require('path');

const CLIENTS = {
  HN: {
    name: '<PERSON><PERSON>ộ<PERSON>',
    id: 'hanoi'
  },
  QN: {
    name: 'Quảng Ninh',
    id: 'quangninh'
  },
  QNC: {
    name: 'Quảng Ninh Citizen',
    id: 'quangninhcitizen'
  }
};

const startApp = async (client) => {
  const clientConfig = CLIENTS[client];
  if (!clientConfig) {
    console.error(`Unknown client: ${client}`);
    console.log('Available clients:', Object.keys(CLIENTS).join(', '));
    process.exit(1);
  }

  // Set client-specific environment variable
  process.env.CLIENT_ID = clientConfig.id;
  process.env.CLIENT_NAME = clientConfig.name;

  // First clear the Android main files
  const clearMainProcess = spawn('node', ['clearMainAndroid.js'], {
    stdio: 'inherit',
    shell: true,
  });

  await new Promise((resolve, reject) => {
    clearMainProcess.on('exit', code => {
      if (code === 0) resolve();
      else reject(new Error(`Clear Android main files failed with code ${code}`));
    });
  });

  // Then replace environment files
  const replaceEnvProcess = spawn('node', ['envReplacer.js', client], {
    stdio: 'inherit',
    shell: true,
  });

  await new Promise((resolve, reject) => {
    replaceEnvProcess.on('exit', code => {
      if (code === 0) resolve();
      else reject(new Error(`Replace environment files failed with code ${code}`));
    });
  });

  // Start the React Native app
  const startProcess = spawn(
    'react-native',
    ['webpack-start'],
    {
      stdio: 'inherit',
      shell: true,
    }
  );

  startProcess.on('error', err => {
    console.error('Failed to start the application:', err);
    process.exit(1);
  });
};

// Parse command line arguments
const args = process.argv.slice(2);
if (args.length < 1) {
  console.error('Usage: start <client>');
  console.log('Available clients:', Object.keys(CLIENTS).join(', '));
  process.exit(1);
}

const [client] = args;
startApp(client);
