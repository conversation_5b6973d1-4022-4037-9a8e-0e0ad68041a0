const fs = require('fs');
const path = require('path');

const envFile = path.join(__dirname, '../miniapp_env.js');
const env = process.argv[2];

if (!['production', 'staging', 'uat', 'development', 'local'].includes(env)) {
  console.error(`Invalid environment: ${env}`);
  process.exit(1);
}

const content = `export const miniAppEnv = '${env}';
`;

fs.writeFileSync(envFile, content);
console.log(`Updated miniapp_env.js to ${env} environment`);
