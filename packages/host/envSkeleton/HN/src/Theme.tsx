// import {MD3Theme, MD3Colors, DefaultTheme} from 'react-native-paper';

// type ACColors = {
//   myOwnColor: string;
// };

// export const appTheme: MD3Theme & {colors: typeof MD3Colors & ACColors} = {
//   ...DefaultTheme,
//   // Specify custom property
//   // myOwnProperty: true,
//   colors: {
//     ...DefaultTheme.colors,
//     primary: '#254ed3',
//     primaryContainer: '#dde0fe',
//     secondary: '#FFAB00',
//     secondaryContainer: '#fcddb2',
//     tertiary: '#388e3c',
//     tertiaryContainer: '#9cf898',
//     error: '#FF5630',
//     onSurface: '#1c1b1d',
//     surface: 'white',
//     myOwnColor: '#BADA55',
//     surfaceVariant: '#F4F6F8',
//   } as any,
// };

import {DefaultTheme, MD3DarkTheme} from 'react-native-paper';

export const lightTheme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: 'rgb(192, 0, 20)',
    onPrimary: 'rgb(255, 255, 255)',
    primaryContainer: 'rgb(255, 218, 214)',
    onPrimaryContainer: 'rgb(65, 0, 2)',
    secondary: 'rgb(109, 94, 0)',
    onSecondary: 'rgb(255, 255, 255)',
    secondaryContainer: 'rgb(252, 227, 101)',
    onSecondaryContainer: 'rgb(33, 27, 0)',
    tertiary: 'rgb(16, 109, 32)',
    onTertiary: 'rgb(255, 255, 255)',
    tertiaryContainer: 'rgb(157, 248, 152)',
    onTertiaryContainer: 'rgb(0, 34, 4)',
    error: '#FF5630',
    onSurface: '#1c1b1d',
    surface: '#FFFBEE',
    myOwnColor: '#BADA55',
    surfaceVariant: '#F4F6F8',
    onSurfaceVariant: '#23272A',
  } as any,
  roundness: 2,
  isV3: false,
} as any;

export const darkTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    primary: 'rgb(255, 180, 171)',
    onPrimary: 'rgb(105, 0, 6)',
    primaryContainer: 'rgb(147, 0, 13)',
    onPrimaryContainer: 'rgb(255, 218, 214)',
    secondary: 'rgb(222, 199, 76)',
    onSecondary: 'rgb(57, 48, 0)',
    secondaryContainer: 'rgb(82, 70, 0)',
    onSecondaryContainer: 'rgb(252, 227, 101)',
    tertiary: 'rgb(130, 219, 126)',
    onTertiary: 'rgb(0, 57, 10)',
    tertiaryContainer: 'rgb(0, 83, 18)',
    error: '#FF5630',
    onSurface: '#f5f5f5',
    surface: '#23272A',
    myOwnColor: '#BADA55',
    surfaceVariant: '#23272A',
    onSurfaceVariant: '#F4F6F8',
  } as any,
  roundness: 2,
  isV3: false,
} as any;
