import {DefaultTheme, MD3DarkTheme} from 'react-native-paper';

export const lightTheme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: '#D71920',
    onPrimary: '#fff',
    primaryContainer: '#fdd9d6',
    secondary: '#FFD666', // Figma Warning/Default/Light
    secondaryContainer: '#FFF1B8', // Figma Secondary/Default/Main
    tertiary: '#388e3c',
    tertiaryContainer: '#9cf898',
    error: '#FF5630',
    onSurface: '#1c1b1d',
    surface: '#FFFBEE',
    myOwnColor: '#BADA55',
    surfaceVariant: '#F4F6F8',
    onSurfaceVariant: '#23272A',
  } as any,
  roundness: 2,
  isV3: false,
} as any;

export const darkTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    primary: '#D71920',
    onPrimary: '#23272A',
    primaryContainer: '#3a0a0d',
    secondary: '#FFD666', // Figma Warning/Default/Light
    secondaryContainer: '#FFF1B8', // Figma Secondary/Default/Main
    tertiary: '#388e3c',
    tertiaryContainer: '#1b5e20',
    error: '#FF5630',
    onSurface: '#f5f5f5',
    surface: '#23272A',
    myOwnColor: '#BADA55',
    surfaceVariant: '#23272A',
    onSurfaceVariant: '#F4F6F8',
  } as any,
  roundness: 2,
  isV3: false,
} as any;
