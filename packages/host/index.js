/**
 * @format
 */
import '@gorhom/bottom-sheet';
import 'color';
import 'react-native-paper-dates';
import 'react-query';
import 'jwt-decode';
import 'react-native-svg';
import 'react-native-webview';
import 'react-native-tab-view';
import 'react-native-pager-view';
import 'lottie-react-native';
import '@react-native-firebase/remote-config';
import '@react-navigation/native-stack';
import '@react-navigation/bottom-tabs';
import 'react-native-paper';
import 'react-native-html-to-pdf';
import '@notifee/react-native';
import 'react-native-vector-icons';
import { ScriptManager, Script, Federated } from '@callstack/repack/client';
import { AppRegistry, Platform } from 'react-native';
import App from './src/App';
import { name as appName } from './app.json';
import { version as appVersion } from './package.json';
import configKeys from '../catalog-server/utils/configKeys';
import { useConfigStore } from '@ac-mobile/common';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { miniAppEnv } from './miniapp_env';
import { getAppContainers } from './containers';

useConfigStore.getState().setConfig(configKeys.CURRENT_APP_VERSION, appVersion);
useConfigStore.getState().setConfig(configKeys.CURRENT_APP_VERSION, appVersion);
ScriptManager.shared.setMaxListeners(50);
ScriptManager.shared.setStorage(AsyncStorage);

ScriptManager.shared.addResolver(async (scriptId, caller) => {

  const containers = await getAppContainers(miniAppEnv);
  print('containersxxxx', containers)
  const resolveURL = Federated.createURLResolver({
    containers,
  });

  let url;
  if (__DEV__ && caller === 'main') {
    url = Script.getDevServerURL(scriptId);
  } else {
    url = resolveURL(scriptId, caller);
  }

  if (!url) {
    return undefined;
  }

  return {
    url,
    cache: !__DEV__,
    query: {
      platform: Platform.OS, // only needed in development
    },
    verifyScriptSignature: 'off',
    // verifyScriptSignature: __DEV__ ? 'off' : 'strict',
  };
});

AppRegistry.registerComponent(appName, () => App);
