const fs = require('fs');
const path = require('path');

function clearFolder(folderPath) {
  // Ensure the folder path is absolute
  const absoluteFolderPath = path.resolve(folderPath);

  // Check if the folder exists
  if (fs.existsSync(absoluteFolderPath)) {
    // Get a list of all files and subdirectories in the folder
    const folderContents = fs.readdirSync(absoluteFolderPath);

    // Iterate through the contents and remove each item
    folderContents.forEach((item) => {
      const itemPath = path.join(absoluteFolderPath, item);

      // Check if the item is a file or a subdirectory
      const isFile = fs.statSync(itemPath).isFile();
      const isDirectory = fs.statSync(itemPath).isDirectory();

      // Remove the item based on its type
      if (isFile) {
        fs.unlinkSync(itemPath); // Remove file
      } else if (isDirectory) {
        clearFolder(itemPath); // Recursively clear subdirectory
        fs.rmdirSync(itemPath); // Remove empty directory
      }
    });

    console.log(`Folder "${absoluteFolderPath}" has been cleared.`);
  } else {
    console.log(`Folder "${absoluteFolderPath}" does not exist.`);
  }
}

// Example usage:
const folderPath = './android/app/src/main/java';
clearFolder(folderPath);
