/**
 * @format
 */
import '@gorhom/bottom-sheet';
import 'color';
import 'react-native-paper-dates';
import 'react-query';
import 'jwt-decode';
import 'react-native-svg';
import 'react-native-webview';
import 'react-native-tab-view';
import 'react-native-pager-view';
import 'lottie-react-native';
import '@react-navigation/native-stack';
import '@react-navigation/bottom-tabs';
import 'react-native-paper';
import 'react-native-vector-icons';
import { ScriptManager, Script, Federated } from '@callstack/repack/client';
import { AppRegistry, Platform } from 'react-native';
import App from './src/App';
import { name as appName } from './app.json';
import { version as appVersion } from './package.json';
import configKeys from '../catalog-server/utils/configKeys';
import { CONFIG, getContainers, useConfigStore } from '@ac-mobile/common';
import AsyncStorage from '@react-native-async-storage/async-storage';

useConfigStore.getState().setConfig(configKeys.CURRENT_APP_VERSION, appVersion);
ScriptManager.shared.setMaxListeners(50);
ScriptManager.shared.setStorage(AsyncStorage);

ScriptManager.shared.addResolver(async (scriptId, caller) => {
  const params = {
    // hostname: 'https://super-app-showcase-catalog.vercel.app',
    // hostname: CONFIG.API.CATALOG,
    hostname: 'http://localhost:3000',
    version: appVersion,
    platform: Platform.OS,
    environment: 'local',
    appName,
  };

  console.log(
    { ...params, scriptId, caller, 'CONFIG.API.CATALOG': CONFIG.API.CATALOG },
    'addResolverXXX',
  );

  const containers = await getContainers(params);

  console.log(containers, 'containers');

  // HN stg
  // const containers = {
  //   auth: `https://dl-storage-dev.dev.cluster02.fis-cloud.xplat.online/dl-mobile-dev/releases/staging/auth-dadb9344-5c3a-414d-9f57-156592128a38/auth-${Platform.OS}@0.0.1/[name][ext]`,
  //   dashboard: `https://dl-storage-dev.dev.cluster02.fis-cloud.xplat.online/dl-mobile-dev/releases/staging/ihanoi-dadb9344-5c3a-414d-9f57-156592128a38/ihanoi-${Platform.OS}@0.0.1/[name][ext]`,
  // }

  // QN dev
  // const containers = {
  //   auth: `https://dl-storage-dev.dev.cluster02.fis-cloud.xplat.online/dl-mobile-dev/releases/development/auth-ef73553a-8b76-48d8-9177-235a2d4032d2/auth-${Platform.OS}@0.0.1/[name][ext]`,
  //   dashboard: `https://dl-storage-dev.dev.cluster02.fis-cloud.xplat.online/dl-mobile-dev/releases/development/Dashboard-ef73553a-8b76-48d8-9177-235a2d4032d2/Dashboard-${Platform.OS}@0.0.1/[name][ext]`,
  // }

  // QN stg
  // const containers = {
  //   auth: `https://dl-storage-dev.dev.cluster02.fis-cloud.xplat.online/dl-mobile-dev/releases/staging/auth-ef73553a-8b76-48d8-9177-235a2d4032d2/auth-${Platform.OS}@0.0.1/[name][ext]`,
  //   dashboard: `https://dl-storage-dev.dev.cluster02.fis-cloud.xplat.online/dl-mobile-dev/releases/staging/Dashboard-ef73553a-8b76-48d8-9177-235a2d4032d2/Dashboard-${Platform.OS}@0.0.1/[name][ext]`,
  // }

  const resolveURL = Federated.createURLResolver({
    containers,
  });

  let url;
  if (__DEV__ && caller === 'main') {
    url = Script.getDevServerURL(scriptId);
  } else {
    url = resolveURL(scriptId, caller);
  }

  if (!url) {
    return undefined;
  }

  return {
    url,
    cache: !__DEV__,
    query: {
      platform: Platform.OS, // only needed in development
    },
    verifyScriptSignature: __DEV__ ? 'off' : 'strict',
  };
});

AppRegistry.registerComponent(appName, () => App);
