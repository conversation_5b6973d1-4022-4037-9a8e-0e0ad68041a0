/* eslint-disable react/no-unstable-nested-components */
import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {useTheme} from 'react-native-paper';
import Icon from 'react-native-vector-icons/AntDesign';
import {useNavStore} from '@ac-mobile/common';
import DashboardScreen from '../screens/DashboardScreen';
import TestScreen from '../screens/TestScreen';

export type TabsParamList = {
  HomeNavigator: undefined;
  ServicesNavigator: undefined;
  AccountNavigator: undefined;
  TestScreen: undefined;
};

const Tabs = createBottomTabNavigator<TabsParamList>();

const TabsNavigator = () => {
  const theme = useTheme();

  const {dashboard} = useNavStore();

  return (
    <Tabs.Navigator
      activeIndicatorStyle={{
        backgroundColor: theme.colors.primaryContainer,
      }}
      activeColor={theme.colors.primary}
      // eslint-disable-next-line react-native/no-inline-styles
      barStyle={{
        display: dashboard ? undefined : 'none',
        backgroundColor: theme.colors.primaryContainer,
      }}>
      <Tabs.Screen
        name="HomeNavigator"
        component={DashboardScreen}
        options={{
          title: 'Trang chủ',
          tabBarIcon: ({color, focused}: {focused: boolean; color: string}) => (
            <Icon
              name="home"
              size={24}
              color={focused ? theme.colors.primary : color}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="TestScreen"
        component={TestScreen}
        options={{
          title: 'Test',
          tabBarIcon: ({color, focused}: {focused: boolean; color: string}) => (
            <Icon
              name="home"
              size={24}
              color={focused ? theme.colors.primary : color}
            />
          ),
        }}
      />
    </Tabs.Navigator>
  );
};

export default TabsNavigator;
