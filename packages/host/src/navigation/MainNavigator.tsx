import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';

import DashboardMiniAppScreen from '../screens/DashboardMiniAppScreen';

export type MainStackParamList = {
  Home: undefined;
};

const Main = createNativeStackNavigator<MainStackParamList>();

const MainNavigator = () => {
  return (
    <Main.Navigator
      screenOptions={{
        headerShown: false,
      }}>
      <Main.Screen name="Home" component={DashboardMiniAppScreen} />
    </Main.Navigator>
  );
};

export default MainNavigator;
