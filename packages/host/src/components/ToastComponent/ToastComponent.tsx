import {Pressable, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import Toast, {
  BaseToast,
  ErrorToast,
  SuccessToast,
} from 'react-native-toast-message';
import {CheckFalse, CheckSuccess, CloseIcon} from '../../icons';

// Toast.show({
//  type: 'ErrorToast',
//  props: {title: 'Cập nhập thất bại'},
//  });
// Toast.show({
// type: 'SuccessToast',
// props: {title: 'Cập nhập thất bại'},
// });
export const toastConfig = {
  success: (props: any) => (
    <View className="w-[90%] h-15 bg-white rounded-xl flex-row justify-between items-center p-1 shadow-md shadow-black/10 mt-8">
      <View className="flex-row justify-center items-center space-x-2.5">
        <View className="flex-row justify-center items-center w-12 h-12 bg-green-500/20 rounded-lg">
          <CheckSuccess width={24} height={24} />
        </View>
        <View className="flex-col gap-1">
          <Text className="font-[600]">
            {props?.props?.title ?? props.text1}
          </Text>
          <Text>{props?.props?.content ?? props.text2}</Text>
        </View>
      </View>
      <Pressable onPress={() => Toast.hide()}>
        <CloseIcon width={24} height={24} />
      </Pressable>
    </View>
  ),
  error: (props: any) => (
    <View className="w-[90%] h-15 bg-white rounded-xl flex-row justify-between items-center p-2 shadow-md shadow-black/10 mt-8">
      <View className="flex-row justify-center items-center space-x-2.5">
        <View className="flex-row justify-center items-center w-12 h-12 bg-red-500/20 rounded-lg">
          <CheckFalse width={24} height={24} />
        </View>
        <View className="flex-col gap-1">
          <Text className="font-[600]">
            {props?.props?.title ?? props.text1}
          </Text>
          <Text>{props?.props?.content ?? props.text2}</Text>
        </View>
      </View>
      <Pressable onPress={() => Toast.hide()}>
        <CloseIcon width={24} height={24} />
      </Pressable>
    </View>
  ),
};
