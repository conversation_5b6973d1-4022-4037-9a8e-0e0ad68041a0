import React from 'react';
import {StyleSheet, Text, SafeAreaView, View} from 'react-native';
import {Button, MD3Colors} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {useNetInfo} from '@react-native-community/netinfo';
import RNRestart from 'react-native-restart';
import AsyncStorage from '@react-native-async-storage/async-storage';

const AppRepositories: {[key: string]: any} = {
  AuthProvider: 'dữ liệu [Lỗi E1]',
  AccountScreen: 'dữ liệu [Lỗi E2]',
  BookingScreen: 'dữ liệu [Lỗi E3]',
  DashboardScreen: 'dữ liệu [Lỗi E4]',
  TestExamWebScreen: 'dữ liệu [Lỗi E5]',
  UpcomingScreen: 'dữ liệu [Lỗi E6]',
};

type Props = {
  children: React.ReactNode;
  name: string;
};

type State = {
  hasError: boolean;
};

const NetInfoView = ({name}: {name: string}) => {
  const {isConnected} = useNetInfo();

  if (isConnected) {
    return (
      <View style={styles.netInfoViewContainer}>
        <Icon size={96} color={MD3Colors.primary20} name="alert-octagon" />
        <Text style={styles.text}>{`Lỗi khi tải ${
          AppRepositories[name] || 'dữ liệu'
        }`}</Text>
        <Text style={styles.subText}>Vui lòng bấm Tải lại!</Text>
      </View>
    );
  }

  return (
    <View style={styles.netInfoViewContainer}>
      <Icon size={96} color={MD3Colors.primary20} name="alert-octagon" />
      <Text style={styles.text}>{`Lỗi khi tải ${
        AppRepositories[name] || 'dữ liệu'
      }`}</Text>
      <Text style={styles.subText}>Vui lòng kiểm tra lại "Kết nối mạng"!</Text>
    </View>
  );
};

class ErrorBoundary extends React.Component<Props, State> {
  name: string;

  constructor(props: Props) {
    super(props);
    this.name = props.name;
    this.state = {hasError: false};
  }

  static getDerivedStateFromError() {
    return {hasError: true};
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.log(error, errorInfo);
    const myHeaders = new Headers();
    myHeaders.append('Content-Type', 'application/json');

    const raw = JSON.stringify({
      foo: 'packages/host/src/components/ErrorBoundary.tsx',
      error,
      errorInfo,
      SAS_CATALOG_SERVER_URL:
        process.env.SAS_CATALOG_SERVER_URL || 'SAS_CATALOG_SERVER_URL is null',
    });

    fetch(
      'https://super-app-showcase-catalog.vercel.app/log?platform=ios&appVersion=0.0.1',
      // `${process.env.SAS_CATALOG_SERVER_URL}/log?platform=ios&appVersion=0.0.1`,
      {
        method: 'POST',
        headers: myHeaders,
        body: raw,
        redirect: 'follow',
      },
    )
      .then(response => response.text())
      .then(result => console.log(result))
      .catch(error1 => console.error(error1));
  }

  render() {
    if (this.state.hasError) {
      return (
        <SafeAreaView style={styles.container}>
          <NetInfoView name={this.name} />
          <Button
            onPress={async () => {
              const keys = await AsyncStorage.getAllKeys();
              for (const key of keys) {
                if (key.includes('Repack')) {
                  await AsyncStorage.removeItem(key);
                }
              }
              this.setState({hasError: false});
              RNRestart.restart();
            }}>
            Tải lại
          </Button>
        </SafeAreaView>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  netInfoViewContainer: {
    alignItems: 'center',
  },
  text: {
    fontSize: 24,
    color: MD3Colors.primary20,
    textAlign: 'center',
  },
  subText: {
    fontSize: 16,
    color: MD3Colors.primary20,
    textAlign: 'center',
  },
});

export default ErrorBoundary;
