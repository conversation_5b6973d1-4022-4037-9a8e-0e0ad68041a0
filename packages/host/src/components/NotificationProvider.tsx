import React, {useEffect, useCallback} from 'react';
import {Platform, PermissionsAndroid} from 'react-native';
import messaging from '@react-native-firebase/messaging';
import notifee, {AndroidImportance} from '@notifee/react-native';
import firebase from '@react-native-firebase/app';
import {params} from '../../miniapp_config_param';
import {usePostDevice} from '../api/api-notification';
import {firebaseConfig} from '../utils/firebase-config';
import DeviceInfo from 'react-native-device-info';
import Toast from 'react-native-toast-message';
import {useConfigStore} from '@ac-mobile/common';

type NotificationProviderProps = {
  children: React.ReactNode;
  onNoti?: (noti: any) => void;
};

const NotificationProvider: React.FC<NotificationProviderProps> = ({
  children,
  onNoti,
}) => {
  const {setConfig, getConfig} = useConfigStore();
  const postDevice = usePostDevice();
  const saveFCMToken = useCallback(
    async (token: string) => {
      const lastToken = getConfig('lastPushToken');
      if (lastToken === token) {
        return;
      } // Prevent duplicate registration
      setConfig('lastPushToken', token);
      try {
        const deviceUUID = DeviceInfo.getUniqueIdSync();
        const deviceType = Platform.OS;
        const pushToken = token;
        const appId = params.appId;
        const rs = await postDevice({deviceType, pushToken, appId, deviceUUID});
        if (rs?.data?.data) {
          setConfig('deviceId', rs.data.data.deviceId);
        }
        console.log('Device token saved successfully:', rs.data);
        Toast.show({
          type: 'success',
          text1: 'Thành công',
          text2: 'Token thiết bị đã được lưu thành công',
          position: 'top',
        });
      } catch (error) {
        console.error('Error saving FCM token:', JSON.stringify(error));
        Toast.show({
          type: 'error',
          text1: 'Lỗi',
          text2: 'Không thể lưu token thiết bị',
          position: 'top',
        });
      }
    },
    [setConfig, getConfig, postDevice],
  );

  // Initialize Firebase first
  useEffect(() => {
    const initializeFirebase = async () => {
      try {
        if (!firebase.apps.length) {
          await firebase.initializeApp(firebaseConfig);
        }
        console.log('Firebase initialized successfully');
      } catch (error) {
        console.error('Firebase initialization error:', error);
      }
    };
    initializeFirebase();
  }, []);

  // Request notifications permission and initialize
  useEffect(() => {
    let unsubscribe: (() => void) | undefined;
    const requestUserPermission = async () => {
      try {
        // First check if Firebase is initialized
        if (!firebase.apps.length) {
          console.error('Firebase not initialized yet');
          return;
        }

        // Then check if physical device permissions are required
        if (Platform.OS === 'android') {
          // For Android 13+ (API level 33 and above)
          if (Platform.Version >= 33) {
            const permissionResult = await PermissionsAndroid.request(
              PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
            );
            console.log('Notification permission result:', permissionResult);
          }
        }

        // Then request FCM permissions
        const authStatus = await messaging().requestPermission();
        console.log('FCM auth status:', authStatus);

        const enabled =
          (authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
            authStatus === messaging.AuthorizationStatus.PROVISIONAL) &&
          false;

        if (enabled) {
          const fcmToken = await messaging().getToken();
          saveFCMToken(fcmToken);

          unsubscribe = messaging().onMessage(async remoteMessage => {
            console.log('Received foreground message:', remoteMessage);
            if (onNoti) {
              onNoti(remoteMessage);
            }
            await notifee.displayNotification({
              title: remoteMessage.notification?.title,
              body: remoteMessage.notification?.body,
              android: {
                channelId: 'default',
                sound: 'default',
                pressAction: {
                  id: 'default',
                },
              },
            });
          });

          // Set background message handler
          messaging().setBackgroundMessageHandler(async remoteMessage => {
            console.log('Received background message:', remoteMessage);
          });

          return unsubscribe;
        }
      } catch (error) {
        console.error('Failed to get push token:', error);
      }
    };

    requestUserPermission();

    // Cleanup: unsubscribe from onMessage when effect re-runs or unmounts
    return () => {
      if (unsubscribe) unsubscribe();
    };
  }, [saveFCMToken, onNoti]);
  useEffect(() => {
    async function createNotificationChannel() {
      await notifee.createChannel({
        id: 'default',
        name: 'Default Channel',
        importance: AndroidImportance.HIGH,
        sound: 'default',
      });
    }
    createNotificationChannel();
  }, []);

  return <>{children}</>;
};

export default NotificationProvider;
