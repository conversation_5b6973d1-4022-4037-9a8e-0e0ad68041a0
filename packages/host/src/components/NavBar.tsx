import React from 'react';
import {NativeStackHeaderProps} from '@react-navigation/native-stack';
import {Appbar, useTheme} from 'react-native-paper';

const NavBar = ({navigation, back, route, options}: NativeStackHeaderProps) => {
  const theme = useTheme();
  if (
    route.name === 'Home' ||
    route.name === 'Services' ||
    route.name === 'Account'
  ) {
    return null;
  }

  return (
    <Appbar.Header elevated style={{backgroundColor: theme.colors.primary}}>
      {back ? <Appbar.BackAction onPress={navigation.goBack} /> : null}
      <Appbar.Content title={options.title ?? route.name} />
    </Appbar.Header>
  );
};

export default NavBar;
