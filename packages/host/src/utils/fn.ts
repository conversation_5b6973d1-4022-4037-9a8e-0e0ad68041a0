import moment from 'moment';

export const formatDateTime = (dateTime: string, defaultValue = '-') => {
  return (
    moment(dateTime)
      .format('HH:mm DD/MM/YYYY')
      .replace(`/${new Date().getFullYear()}`, '') ||
    defaultValue ||
    dateTime ||
    ''
  ).replace(`/${new Date().getFullYear()}`, '');
};
export const convertDate = (date: string) => {
  return `${date.split('-')[2]}-${date.split('-')[1]}-${date.split('-')[0]}`;
};
export const isEqual = (obj1: any, obj2: any): boolean => {
  if (obj1 === obj2) return true;
  if (typeof obj1 !== typeof obj2) return false;
  if (typeof obj1 !== 'object') return false;
  if (Array.isArray(obj1) !== Array.isArray(obj2)) return false;

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) return false;

  return keys1.every(key => isEqual(obj1[key], obj2[key]));
};
