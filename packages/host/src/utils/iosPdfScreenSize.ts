import {Dimensions, Platform} from 'react-native';

interface PDFScreenCalibration {
  screenWidth: number;
  screenHeight: number;
  scaleFactors: {
    x: number;
    y: number;
  };
}

const {width: SCREEN_WIDTH, height: SCREEN_HEIGHT} = Dimensions.get('window');

const IOS_PDF_CALIBRATIONS: PDFScreenCalibration[] = [
  {
    screenWidth: 393,
    screenHeight: 852,
    scaleFactors: {
      x: 1.6,
      y: 1.4,
    },
  },
];

const findNearestCalibration = (
  targetWidth: number,
  targetHeight: number,
  calibrations: PDFScreenCalibration[],
): PDFScreenCalibration | null => {
  console.log(SCREEN_WIDTH, 'SCREEN_WIDTH', SCREEN_HEIGHT, 'SCREEN_HEIGHT');

  if (calibrations.length === 0) return null;

  // First try to find exact match
  const exactMatch = calibrations.find(
    cal => cal.screenWidth === targetWidth && cal.screenHeight === targetHeight,
  );
  console.log(exactMatch, 'exactMatchexactMatchexactMatchexactMatch');

  if (exactMatch) return exactMatch;

  // If no exact match, find nearest using reduce with initial value
  return calibrations.reduce((nearest, current) => {
    const currentDiff =
      Math.abs(current.screenWidth - targetWidth) +
      Math.abs(current.screenHeight - targetHeight);
    const nearestDiff =
      Math.abs(nearest.screenWidth - targetWidth) +
      Math.abs(nearest.screenHeight - targetHeight);

    return currentDiff < nearestDiff ? current : nearest;
  }, calibrations[0]); // Provide initial value
};

export const calculateIOSPDFCoordinates = (
  x: number,
  y: number,
): {x: number; y: number} => {
  console.log('IN calculateIOSPDFCoordinates');

  if (Platform.OS !== 'ios') {
    return {x, y};
  }

  const calibration = findNearestCalibration(
    SCREEN_WIDTH,
    SCREEN_HEIGHT,
    IOS_PDF_CALIBRATIONS,
  );

  if (calibration) {
    return {
      x: x * calibration.scaleFactors.x,
      y: y * calibration.scaleFactors.y,
    };
  }
  console.log(x, y, 'xxx', 'yyy');

  return {x, y};
};

export const addCalibrationData = (xFactor: number, yFactor: number) => {
  const newCalibration: PDFScreenCalibration = {
    screenWidth: SCREEN_WIDTH,
    screenHeight: SCREEN_HEIGHT,
    scaleFactors: {
      x: xFactor,
      y: yFactor,
    },
  };

  console.log(JSON.stringify(newCalibration, null, 2));
};
