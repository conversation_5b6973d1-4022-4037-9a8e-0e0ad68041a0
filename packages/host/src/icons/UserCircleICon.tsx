// import * as React from 'react';
// import Svg, {
//   SvgProps,
//   Path,
//   Rect,
//   ClipPath,
//   Defs,
//   Pattern,
//   Image,
// } from 'react-native-svg';

// export const Setting = (props: SvgProps) => (
//   <Svg width={'1rem'} height={'1rem'} viewBox="-10 -8 42 42" {...props}>
//     <Path
//       opacity="0.32"
//       fill-rule="evenodd"
//       clip-rule="evenodd"
//       d="M14.2793 2.152C13.9093 2 13.4393 2 12.5003 2C11.5613 2 11.0913 2 10.7213 2.152C10.2288 2.35421 9.8367 2.74377 9.63132 3.235C9.53732 3.458 9.50132 3.719 9.48632 4.098C9.47965 4.3726 9.40305 4.64097 9.26376 4.87772C9.12448 5.11447 8.92711 5.31178 8.69032 5.451C8.44906 5.5851 8.17786 5.65615 7.90184 5.65754C7.62582 5.65894 7.35392 5.59065 7.11132 5.459C6.77332 5.281 6.52832 5.183 6.28632 5.151C5.75687 5.08148 5.22139 5.2238 4.79632 5.547C4.47832 5.789 4.24332 6.193 3.77432 7C3.30432 7.807 3.07032 8.21 3.01732 8.605C2.94732 9.131 3.09132 9.663 3.41732 10.084C3.56532 10.276 3.77432 10.437 4.09732 10.639C4.57432 10.936 4.88032 11.442 4.88032 12C4.88032 12.558 4.57432 13.064 4.09832 13.36C3.77432 13.563 3.56532 13.724 3.41632 13.916C3.2554 14.1239 3.13728 14.3617 3.0688 14.6156C3.00031 14.8694 2.98282 15.1343 3.01732 15.395C3.07032 15.789 3.30432 16.193 3.77432 17L3.77993 17.0096C4.24618 17.8102 4.47958 18.211 4.79632 18.453C5.22032 18.776 5.75632 18.918 6.28632 18.849C6.52832 18.817 6.77332 18.719 7.11132 18.541C7.35404 18.4092 7.62613 18.3408 7.90234 18.3422C8.17855 18.3436 8.44994 18.4147 8.69132 18.549C9.17732 18.829 9.46532 19.344 9.48632 19.902C9.50132 20.282 9.53732 20.542 9.63132 20.765C9.83532 21.255 10.2273 21.645 10.7213 21.848C11.0913 22 11.5613 22 12.5003 22C13.4393 22 13.9093 22 14.2793 21.848C14.7719 21.6458 15.1639 21.2562 15.3693 20.765C15.4633 20.542 15.4993 20.282 15.5143 19.902C15.5343 19.344 15.8233 18.828 16.3103 18.549C16.5516 18.4149 16.8228 18.3439 17.0988 18.3425C17.3748 18.3411 17.6467 18.4093 17.8893 18.541C18.2273 18.719 18.4723 18.817 18.7143 18.849C19.2443 18.919 19.7803 18.776 20.2043 18.453C20.5223 18.211 20.7573 17.807 21.2263 17C21.6963 16.193 21.9303 15.79 21.9833 15.395C22.0177 15.1343 22 14.8693 21.9314 14.6155C21.8627 14.3616 21.7444 14.1239 21.5833 13.916C21.4353 13.724 21.2263 13.563 20.9033 13.361C20.4263 13.064 20.1203 12.558 20.1203 12C20.1203 11.442 20.4263 10.936 20.9023 10.64C21.2263 10.437 21.4353 10.276 21.5843 10.084C21.7452 9.87606 21.8633 9.63829 21.9318 9.38443C22.0003 9.13057 22.0178 8.86566 21.9833 8.605C21.9303 8.211 21.6963 7.807 21.2263 7L21.2207 6.99035C20.7545 6.1898 20.521 5.78903 20.2043 5.547C19.7792 5.2238 19.2438 5.08148 18.7143 5.151C18.4723 5.183 18.2273 5.281 17.8893 5.459C17.6466 5.59083 17.3745 5.65922 17.0983 5.65782C16.8221 5.65642 16.5507 5.58528 16.3093 5.451C16.0727 5.31166 15.8755 5.11429 15.7364 4.87755C15.5973 4.64081 15.5209 4.37251 15.5143 4.098C15.4993 3.718 15.4633 3.458 15.3693 3.235C15.2677 2.99174 15.1191 2.77088 14.9321 2.58506C14.745 2.39923 14.5232 2.25208 14.2793 2.152ZM15.5226 12C15.5226 13.657 14.1686 15 12.4996 15C10.8296 15 9.47656 13.657 9.47656 12C9.47656 10.343 10.8296 9 12.4996 9C14.1696 9 15.5226 10.343 15.5226 12Z"
//       fill="#637381"
//     />
//     <Path
//       d="M15.5226 12C15.5226 13.657 14.1686 15 12.4996 15C10.8296 15 9.47656 13.657 9.47656 12C9.47656 10.343 10.8296 9 12.4996 9C14.1696 9 15.5226 10.343 15.5226 12Z"
//       fill="#637381"
//     />
//   </Svg>
// );
