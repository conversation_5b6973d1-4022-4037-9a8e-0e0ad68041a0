import * as React from 'react';
import Svg, {SvgProps, Path} from 'react-native-svg';
export const DoneTaskIcon = (props: SvgProps) => (
  <Svg fill="none" {...props}>
    {props.color ? (
      <>
        <Path
          fill="#637381"
          d="M12.375 18.95c-2.62 0-4.75-2.13-4.75-4.75s2.13-4.75 4.75-4.75 4.75 2.13 4.75 4.75-2.13 4.75-4.75 4.75Zm0-8c-1.79 0-3.25 1.46-3.25 3.25s1.46 3.25 3.25 3.25 3.25-1.46 3.25-3.25-1.46-3.25-3.25-3.25Z"
        />
        <Path
          fill="#637381"
          d="M11.815 15.83c-.32 0-.64-.12-.88-.36l-.65-.65a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l.48.48 1.61-1.48c.3-.28.78-.26 1.06.04s.26.78-.04 1.06l-1.78 1.64c-.25.22-.55.33-.86.33Z"
        />
        <<PERSON>
          fill="#637381"
          d="M16.375 22.75h-8c-4.62 0-5.48-2.15-5.7-4.24l-.75-8.01c-.11-1.05-.14-2.6.9-3.76.9-1 2.39-1.48 4.55-1.48h10c2.17 0 3.66.49 4.55 1.48 1.04 1.16 1.01 2.71.9 3.77l-.75 7.99c-.22 2.1-1.08 4.25-5.7 4.25Zm-9-16c-1.69 0-2.85.33-3.44.99-.49.54-.65 1.37-.52 2.61l.75 8.01c.17 1.58.6 2.89 4.21 2.89h8c3.6 0 4.04-1.31 4.21-2.9l.75-7.99c.13-1.25-.03-2.08-.52-2.62-.59-.66-1.75-.99-3.44-.99h-10Z"
        />
        <Path
          fill="#637381"
          d="M16.375 6.75c-.41 0-.75-.34-.75-.75v-.8c0-1.78 0-2.45-2.45-2.45h-1.6c-2.45 0-2.45.67-2.45 2.45V6c0 .41-.34.75-.75.75s-.75-.34-.75-.75v-.8c0-1.76 0-3.95 3.95-3.95h1.6c3.95 0 3.95 2.19 3.95 3.95V6c0 .41-.34.75-.75.75ZM16.385 14.39c-.34 0-.64-.23-.73-.57-.1-.4.14-.81.54-.91 1.95-.49 3.76-1.34 5.38-2.52.33-.24.8-.17 1.05.17.24.33.17.8-.17 1.05a17.157 17.157 0 0 1-5.9 2.76.88.88 0 0 1-.17.02ZM8.375 14.42c-.06 0-.12-.01-.18-.02-2.01-.49-3.9-1.34-5.63-2.52a.753.753 0 0 1-.2-1.04c.23-.34.7-.43 1.04-.2 1.58 1.08 3.3 1.85 5.14 2.3.4.1.65.5.55.91-.07.34-.38.57-.72.57Z"
        />
      </>
    ) : (
      <>
        <Path
          fill="#366AE2"
          d="M21.465 6.98c-.85-.94-2.26-1.41-4.33-1.41h-.24v-.04c0-1.68 0-3.76-3.76-3.76h-1.52c-3.76 0-3.76 2.08-3.76 3.76v.04h-.24c-2.07 0-3.49.47-4.33 1.41-.99 1.11-.96 2.58-.86 3.59l.01.07.07.806a.5.5 0 0 0 .226.377c.283.184.585.377.874.537.14.09.29.17.44.25 1.13.62 2.34 1.11 3.58 1.45a4.755 4.755 0 0 0 4.75 4.69c2.62 0 4.75-2.13 4.75-4.75v-.04c1.26-.38 2.47-.91 3.6-1.57.06-.03.1-.06.15-.09a12.2 12.2 0 0 0 1.177-.76.498.498 0 0 0 .207-.353l.066-.607c.01-.06.01-.11.02-.18.08-1 .06-2.38-.88-3.42ZM9.285 5.53c0-1.7 0-2.34 2.33-2.34h1.52c2.33 0 2.33.64 2.33 2.34v.04h-6.18v-.04Zm3.09 11.72a3.25 3.25 0 0 1-3.22-2.84c-.02-.13-.03-.27-.03-.41 0-1.79 1.46-3.25 3.25-3.25s3.25 1.46 3.25 3.25c0 .12-.01.23-.02.34v.01a3.258 3.258 0 0 1-3.23 2.9Z"
        />
        <Path
          fill="#366AE2"
          d="M11.805 16.03c-.19 0-.38-.07-.53-.22l-.99-.98a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l.48.48 1.61-1.48c.3-.28.78-.26 1.06.04s.26.78-.04 1.06l-2.13 1.97c-.16.12-.34.19-.52.19Z"
        />
        <Path
          fill="#366AE2"
          d="M21.417 13.731c.248-.138.57.062.545.345l-.355 3.885c-.21 2-1.03 4.04-5.43 4.04h-7.62c-4.4 0-5.22-2.04-5.42-4.03l-.346-3.703c-.027-.288.28-.482.536-.347.78.43 1.59.81 2.42 1.11a1 1 0 0 1 .62.69c.75 2.6 3.17 4.53 6.01 4.53 2.89 0 5.33-1.97 6.04-4.67a1 1 0 0 1 .61-.7c.84-.33 1.64-.72 2.39-1.15Z"
        />
      </>
    )}
  </Svg>
);
