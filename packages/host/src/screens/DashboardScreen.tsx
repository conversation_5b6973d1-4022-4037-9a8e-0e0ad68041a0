import {Federated} from '@callstack/repack/client';
import React from 'react';
import ErrorBoundary from '../components/ErrorBoundary';
import SplashScreen from '../components/SplashScreen';

const Dashboard = React.lazy(() =>
  Federated.importModule('dashboard', './DashboardHomeScreen'),
);

const DashboardScreen = () => {
  return (
    <ErrorBoundary name="DashboardScreen">
      <React.Suspense fallback={<SplashScreen />}>
        <Dashboard />
      </React.Suspense>
    </ErrorBoundary>
  );
};

export default DashboardScreen;
