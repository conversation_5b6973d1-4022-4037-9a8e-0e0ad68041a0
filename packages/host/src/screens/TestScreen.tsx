import React from 'react';
import ErrorBoundary from '../components/ErrorBoundary';
import SplashScreen from '../components/SplashScreen';
import {Text} from 'react-native-paper';
import {View} from 'react-native';

const TestScreen = () => {
  return (
    <ErrorBoundary name="TestScreen">
      <React.Suspense fallback={<SplashScreen />}>
        <View>
          <Text>TestScreen</Text>
        </View>
      </React.Suspense>
    </ErrorBoundary>
  );
};

export default TestScreen;
