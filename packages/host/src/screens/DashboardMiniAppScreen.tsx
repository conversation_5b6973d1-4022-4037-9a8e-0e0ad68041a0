import {Federated} from '@callstack/repack/client';
import React from 'react';
import ErrorBoundary from '../components/ErrorBoundary';
import SplashScreen from '../components/SplashScreen';

const Dashboard = React.lazy(() =>
  Federated.importModule('dashboard', './App'),
);

const DashboardMiniAppScreen = () => {
  return (
    <ErrorBoundary name="DashboardMiniAppScreen">
      <React.Suspense fallback={<SplashScreen />}>
        <Dashboard />
      </React.Suspense>
    </ErrorBoundary>
  );
};

export default DashboardMiniAppScreen;
