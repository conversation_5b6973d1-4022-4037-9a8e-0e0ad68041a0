
const { spawn } = require('child_process');
const path = require('path');

const CLIENTS = {
  HN: {
    name: '<PERSON><PERSON> Nộ<PERSON>',
    id: 'hanoi'
  },
  QN: {
    name: '<PERSON><PERSON><PERSON><PERSON>nh',
    id: 'quangninh'
  }
};

const ENV_MAP = {
  dev: 'dev',
  uat: 'uat',
  prod: 'prod'
};

const startApp = async (client, env, options = {}) => {
  const clientConfig = CLIENTS[client];
  if (!clientConfig) {
    console.error(`Unknown client: ${client}`);
    console.log('Available clients:', Object.keys(CLIENTS).join(', '));
    process.exit(1);
  }

  if (!ENV_MAP[env]) {
    console.error('Invalid environment. Please use: dev, uat, or prod');
    process.exit(1);
  }

  // Set client-specific environment variable
  process.env.CLIENT_ID = clientConfig.id;
  process.env.CLIENT_NAME = clientConfig.name;

  // First copy the client-specific files
  const copyClientProcess = spawn('node', ['scripts/copy-client-files.js', client], {
    stdio: 'inherit',
    shell: true,
  });

  await new Promise((resolve, reject) => {
    copyClientProcess.on('exit', code => {
      if (code === 0) resolve();
      else reject(new Error(`Copy client files failed with code ${code}`));
    });
  });

  // Then copy the environment config
  const copyEnvProcess = spawn('pnpm', ['copy:env', env], {
    stdio: 'inherit',
    shell: true,
  });

  await new Promise((resolve, reject) => {
    copyEnvProcess.on('exit', code => {
      if (code === 0) resolve();
      else reject(new Error(`Copy env failed with code ${code}`));
    });
  });

  // Prepare start command arguments
  const args = ['webpack-start'];

  // Add port based on standalone mode
  args.push('--port', options.standalone ? '8081' : '9002');

  // Start the React Native app
  const startProcess = spawn(
    'react-native',
    args,
    {
      stdio: 'inherit',
      shell: true,
      env: {
        ...process.env,
        ...(options.standalone ? { STANDALONE: '1' } : {}),
      },
    },
  );

  startProcess.on('error', err => {
    console.error('Failed to start the application:', err);
    process.exit(1);
  });
};

// Parse command line arguments
const args = process.argv.slice(2);
if (args.length < 2) {
  console.error('Usage: start <client> <env> [--standalone]');
  console.log('Available clients:', Object.keys(CLIENTS).join(', '));
  console.log('Available environments:', Object.keys(ENV_MAP).join(', '));
  process.exit(1);
}

const [client, env] = args;
const standalone = args.includes('--standalone');

startApp(client, env, { standalone });