<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CFBundleDevelopmentRegion</key>
		<string>en</string>
		<key>CFBundleDisplayName</key>
		<string>IH<PERSON><PERSON><PERSON></string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>$(PRODUCT_NAME)</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(MARKETING_VERSION)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(CURRENT_PROJECT_VERSION)</string>
		<key>ITSAppUsesNonExemptEncryption</key>
		<false />
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>itms-apps</string>
		</array>
		<key>LSRequiresIPhoneOS</key>
		<true />
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSAllowsArbitraryLoads</key>
			<false />
			<key>NSAllowsLocalNetworking</key>
			<true />
		</dict>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string></string>
		<key>UIAppFonts</key>
		<array>
			<string>AntDesign.ttf</string>
			<string>Entypo.ttf</string>
			<string>EvilIcons.ttf</string>
			<string>Feather.ttf</string>
			<string>FontAwesome.ttf</string>
			<string>FontAwesome5_Brands.ttf</string>
			<string>FontAwesome5_Regular.ttf</string>
			<string>FontAwesome5_Solid.ttf</string>
			<string>Foundation.ttf</string>
			<string>Ionicons.ttf</string>
			<string>MaterialIcons.ttf</string>
			<string>MaterialCommunityIcons.ttf</string>
			<string>SimpleLineIcons.ttf</string>
			<string>Octicons.ttf</string>
			<string>Zocial.ttf</string>
			<string>Fontisto.ttf</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIRequiredDeviceCapabilities</key>
		<array>
			<string>arm64</string>
		</array>
		<key>UIRequiresFullScreen</key>
		<true />
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>ITSAppUsesNonExemptEncryption</key>
		<false />
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false />
		<key>NSPhotoLibraryUsageDescription</key>
		<string>$(PRODUCT_NAME) cần truy cập thư viện ảnh để chọn tệp PDF.</string>
		<key>NSDocumentUsageDescription</key>
		<string>$(PRODUCT_NAME) cần truy cập tài liệu để chọn tệp PDF.</string>
		<key>NSPhotoLibraryAddUsageDescription</key>
		<string>$(PRODUCT_NAME) cần quyền để lưu tệp vào thư viện ảnh.</string>
		<key>NSFileProviderUsageDescription</key>
		<string>$(PRODUCT_NAME) cần truy cập tài liệu để chọn tệp.</string>

	</dict>
</plist>