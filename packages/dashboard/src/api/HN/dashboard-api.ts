import {AnyObj} from '@ac-mobile/common';
import {DocumentPickerResponse} from 'react-native-document-picker';
import {AcClient} from './ac-client';
import {IPheDuyetHoSo} from '../screens/dashboard/ProcesseDocument/AcceptDocsType';
import axios from 'axios';

export const getDsLinhVuc = (params: any = {}) => {
  return AcClient.api.get('/Mobile/GetDanhMucLinhVuc-xoa', params);
};
export const GetDanhMucLinhVuc = () => {
  try {
    return AcClient.api.get('/DanhMuc/LinhVuc');
  } catch (error) {
    console.log('error at GetDanhMucLinhVuc', error);
  }
};
//PostChuyenHoSo

// ChuyenHoSoType
const chuyenHoSo = (params: any) => {
  return AcClient.api.post('/HoSo/ChuyenHoSo', params);
};
// PheDuyetHoSo
// IPheDuyetHoSo
const pheDuyetHoSo = (params: IPheDuyetHoSo) => {
  try {
    return AcClient.api.post('/HoSo/PheDuyetHoSo', params);
  } catch (error) {
    console.log(error, 'error at PheDuyetHoSo');
    throw error;
  }
};

//PostTuChoiHoSo
//IRefuseDocs
const TuChoiHoSo = (params: any) => {
  return AcClient.api.post('/HoSo/TuChoiHoSo', params);
};
//GetNoiDungGiayTo
const GetNoiDungGiayTo = (params: {
  HoSoID?: number;
  UserID?: number;
  DonViID?: number;
  PageNum?: number;
  PageSize?: number;
}) => {
  const {DonViID, HoSoID, PageNum, PageSize, UserID} = params;
  try {
    return AcClient.api.get('/HoSo/GetNoiDungGiayTo', {
      params: {
        HoSoID: HoSoID,
        UserID: UserID,
        DonViID: DonViID,
        PageNum: PageNum,
        PageSize: PageSize,
      },
    });
  } catch (error) {
    console.log('INGetNoiDungGiayTo Error', error);
    throw error;
  }
};
//GetNguoiNhanChuyenHoSo
const GetNguoiNhanChuyenHoSo = (params: {
  LinhVucID?: number;
  ThuTucHanhChinhID?: number;
  LoaiXuLy?: number;
  ChucNangHienTai?: string;
  MaQuyTrinh?: string;
}) => {
  const {ChucNangHienTai, LinhVucID, LoaiXuLy, MaQuyTrinh, ThuTucHanhChinhID} =
    params;

  return AcClient.api.get('/HoSo/GetNguoiNhanChuyenHoSo', {
    params: {
      LinhVucID: LinhVucID,
      ThuTucHanhChinhID: ThuTucHanhChinhID,
      LoaiXuLy: LoaiXuLy,
      ChucNangHienTai: ChucNangHienTai,
      MaQuyTrinh: MaQuyTrinh,
    },
  });
};
//GetdanhSachHoSoPheDuyet
const danhSachHoSoPheDuyet = async (params: {
  page: number;
  fromDate: string;
  toDate: string;
  // isFilter: boolean;
  isQuaHan: boolean;
  linhVuc: any;
  // MaHoSo: string;
  OrderBy: string;
  keyword: string;
  donviID?: number;
  phongBanID?: number;
  userID?: number;
  chucNangHienTai: string;
  daNhan: 'daNhan' | 'chuaNhan';
}) => {
  const {
    fromDate,
    toDate,
    page,
    // MaHoSo,
    OrderBy,
    isQuaHan,
    // isFilter,
    linhVuc,
    keyword,
    donviID,
    phongBanID,
    userID,
    chucNangHienTai,
    daNhan,
  } = params;

  const paramsQuery = {
    SoBienBanBanGiao: '',
    HoTenNguoiNop: '',
    ThuTucHanhChinh: '',
    SoBienNhan: '',
    SoBoHoSo: '',
    PhuongXaThiTran: '',
    ChucNangKeTiep: '',
    ChucNangHienTai: chucNangHienTai,
    NguoiNhan: userID,
    LoaiXuLy: '1,3,4',
    DonViID: donviID,
    PhongBanID: phongBanID,
    NguoiNhanHoSo: '',
    ChucNangKeTiepNguoiNhan: '',
    LoaiXuLyNguoiNhan: '',
    TinhTrang: '',
    TinhTrangPT: '',
    TinhTrangXLHS: '',
    IDHCC: 0,
    PageNum: page,
    PageSize: 10,
    ChuoiLinhVuc:
      linhVuc.length > 0
        ? linhVuc.map((item: any) => item.linhVucID).join(',')
        : '',
    TuNgay: fromDate,
    DenNgay: toDate,
    OrderBy: OrderBy,
    ChuoiTimKiem: keyword,
    IsQuaHan: isQuaHan,
    daNhan: daNhan === 'daNhan' ? true : false,
  };
  console.log(paramsQuery, 'paramsQueryparamsQueryparamsQuery');

  try {
    const rs = await AcClient.api.get('/HoSo/GetdanhSachHoSoPheDuyet', {
      params: paramsQuery,
    });

    return rs;
  } catch (error) {
    console.log('Error At danhSachHoSoPheDuyet', error);
    throw error;
  }
};
//GetChiTietHoSo
const getChiTietHoSo = (params: {
  HoSoID?: number;
  ChucNangKeTiep?: string;
  ChucNangHienTai?: string;
  LoaiXuLy?: string;
  UserID?: number;
  DonViID?: number;
}) => {
  const {ChucNangHienTai, ChucNangKeTiep, DonViID, HoSoID, LoaiXuLy, UserID} =
    params;

  try {
    return AcClient.api.get('/HoSo/GetChiTietHoSo', {
      params: {
        HoSoID: HoSoID,
        ChucNangKeTiep: ChucNangKeTiep,
        ChucNangHienTai: ChucNangHienTai,
        LoaiXuLy: LoaiXuLy,
        UserID: UserID,
        DonViID: DonViID,
      },
    });
  } catch (error) {
    console.log('INGetChiTietHoSo ERRORRR ', error);
    throw error;
  }
};
//PostNhanHoSo
const NhanHoSo = (params: {
  HoSo?: {
    hoSoID: any;
    quaTrinhXuLyID: any;
    chucNangHienTai: any;
    nguoiXuLyID: any;
    phongBanXuLyID: any;
    donViXuLyID: any;
  };
}) => {
  return AcClient.api.post('/HoSo/NhanHoSo', params);
};
//PostThuHoiHoSo
const ThuHoiHoSo = (params: {
  HoSo?: {
    ChucNangHienTai?: string;
    ChucNangKeTiep?: string;
    DonViChuyenID?: number;
    DonViNhanID?: number;
    DonViXuLyID?: number;
    DuongDiHoSo?: string[];
    HoSoID?: number;
    HoSoOnlineID?: number;
    KhongGiaiQuyetHoSo?: false;
    LinhVucID?: number;
    LoaiXuLy?: number;
    NguoiChuyenID?: number;
    NguoiXuLyID?: number;
    NhanKetQuaQuaDuongBuuDien?: false;
    NoiDungXuLy?: string;
    PhongBanChuyenID?: number;
    PhongBanID?: number;
    PhongBanXuLyID?: number;
    QuaTrinhXuLyID?: number;
    SoBienBanBanGiao?: string;
    SoBienNhan?: string;
    ThongTinKhac?: string;
    TenDuongDi?: string;
    ThuTucHanhChinhID?: number;
    ToChucID?: number;
    UserID?: number;
    UserName?: string;
  };
}) => {
  const {HoSo} = params;
  return AcClient.api.post('/HoSo/ThuHoiHoSo', {
    HoSo: {
      ChucNangHienTai: HoSo?.ChucNangHienTai,
      ChucNangKeTiep: HoSo?.ChucNangKeTiep,
      DonViChuyenID: HoSo?.DonViChuyenID,
      DonViNhanID: HoSo?.DonViNhanID,
      DonViXuLyID: HoSo?.DonViXuLyID,
      DuongDiHoSo: HoSo?.DuongDiHoSo,
      HoSoID: HoSo?.HoSoID,
      HoSoOnlineID: HoSo?.HoSoOnlineID,
      KhongGiaiQuyetHoSo: HoSo?.KhongGiaiQuyetHoSo,
      LinhVucID: HoSo?.LinhVucID,
      LoaiXuLy: HoSo?.LoaiXuLy,
      NguoiChuyenID: HoSo?.NguoiChuyenID,
      NguoiXuLyID: HoSo?.NguoiXuLyID,
      NhanKetQuaQuaDuongBuuDien: HoSo?.NhanKetQuaQuaDuongBuuDien,
      NoiDungXuLy: HoSo?.NoiDungXuLy,
      PhongBanChuyenID: HoSo?.PhongBanChuyenID,
      PhongBanID: HoSo?.PhongBanID,
      PhongBanXuLyID: HoSo?.PhongBanXuLyID,
      QuaTrinhXuLyID: HoSo?.QuaTrinhXuLyID,
      SoBienBanBanGiao: HoSo?.SoBienBanBanGiao,
      SoBienNhan: HoSo?.SoBienNhan,
      ThongTinKhac: HoSo?.ThongTinKhac,
      TenDuongDi: HoSo?.TenDuongDi,
      ThuTucHanhChinhID: HoSo?.ThuTucHanhChinhID,
      ToChucID: HoSo?.ToChucID,
      UserID: HoSo?.UserID,
      UserName: HoSo?.UserName,
    },
  });
};
//getIHoSoKemTheo
const GetHoSoKemTheo = (params: {HoSoID?: number; DonViID?: number}) => {
  const {DonViID, HoSoID} = params;
  return AcClient.api.get('/HoSo/GetHoSoKemTheo', {
    params: {
      HoSoID: HoSoID,
      DonViID: DonViID,
    },
  });
};
//GetThongTinQuaTrinhXuLyHienTai
const GetThongTinQuaTrinhXuLyHienTai = (quaTrinhXuLyId: number) => {
  return AcClient.api.get('/HoSo/GetThongTinQuaTrinhXuLyHienTai', {
    params: {
      quaTrinhXuLyId: quaTrinhXuLyId,
    },
  });
};
//v
const GetTongHoSoChuaXuLy = (params: {
  DonViID?: number;
  PhongBanID?: number;
  ChucNangHienTai?: string;
  UserID?: number;
}) => {
  return AcClient.api.get('/HoSo/GetTongHoSoChuaXuLy', {
    params: {
      DonViID: params.DonViID,
      PhongBanID: params.PhongBanID,
      ChucNangHienTai: params.ChucNangHienTai,
      UserID: params.UserID,
    },
  });
};

//GetBuocKeTiep
const GetBuocKeTiep = (params: {
  // ThuTucHanhChinhID?: number;
  ChucNangHienTai?: string;
  LoaiXuLy?: string;
  LinhVucID?: number;
}) => {
  const {ChucNangHienTai, LinhVucID, LoaiXuLy} = params;
  return AcClient.api.get('/HoSo/GetBuocKeTiep', {
    params: {
      ChucNangHienTai: ChucNangHienTai,
      LoaiXuLy: LoaiXuLy,
      LinhVucID: LinhVucID,
    },
  });
};
//GetComboDataDieuKien
const GetComboDataDieuKien = (params: {
  TableName?: string;
  TinhThanhID?: number;
  DonViID?: number;
  DieuKien?: string;
  IsMasterDB?: boolean;
}) => {
  const {TableName, DieuKien, DonViID, IsMasterDB, TinhThanhID} = params;
  return AcClient.api.get('/HoSo/GetComboDataDieuKien', {
    params: {
      TableName: TableName,
      TinhThanhID: TinhThanhID,
      DonViID: DonViID,
      DieuKien: DieuKien,
      IsMasterDB: IsMasterDB,
    },
  });
};
//GetKetQuaXuLyHoSo
const GetKetQuaXuLyHoSoCuaDonVi = (params: {
  Thang?: number;
  Nam?: number;
  DonViID?: number;
}) => {
  const {Thang, Nam, DonViID} = params;
  return AcClient.api.get('/HoSo/GetKetQuaXuLyHoSo', {
    params: {
      Thang: Thang,
      Nam: Nam,
      DonViID: DonViID,
    },
  });
};
//GetComBoLinhVuc
const GetComBoLinhVuc = (params: {
  UserID?: number;
  strLinhVucID?: string;
  DonViID?: number;
}) => {
  const {DonViID, UserID, strLinhVucID} = params;
  return AcClient.api.get('/HoSo/GetComBoLinhVuc', {
    params: {
      UserID: UserID,
      strLinhVucID: strLinhVucID,
      DonViID: DonViID, //933
    },
  });
};

//GetThongTinHoSoKhongPheDuyet
const GetThongTinHoSoKhongPheDuyet = (params: {
  HoSoID?: number;
  DonViID?: number;
  QuaTrinhXuLyID?: number;
  LoaiXuLy?: string;
}) => {
  const {DonViID, HoSoID, LoaiXuLy, QuaTrinhXuLyID} = params;
  try {
    return AcClient.api.get('/HoSo/GetThongTinHoSoKhongPheDuyet', {
      params: {
        HoSoID: HoSoID,
        DonViID: DonViID,
        QuaTrinhXuLyID: QuaTrinhXuLyID,
        LoaiXuLy: LoaiXuLy,
      },
    });
  } catch (error) {
    console.log('error At GetThongTinHoSoKhongPheDuyet', JSON.stringify(error));
    throw error;
  }
};
//PostSign
const Sign = (params: {message: string; simid: string; reason: string}) => {
  return AcClient.api.post(`/ESign/SIMPKI/Sign`, {...params});
};
//GetCert
const GetCert = (params: {simid: string}) => {
  return axios.get(`https://api.uat.lokify.xplat.online/ESign/SIMPKI/GetCert`, {
    params: {
      ...params,
    },
  });
};
const getInfo = (id: string) => {
  return AcClient.api.get(`/User/GetListUserMaster?UserID=${id}`, {});
};
const GetKetQuaXuLyHoSoNguoiDung = (params: {
  UserID: number;
  DonViID: number;
  Thang: number;
  Nam: number;
}) => {
  return AcClient.api.get('/HoSo/GetKetQuaXuLyHoSoNguoiDung', {
    params: {
      ...params,
      // DonViID: 34,
    },
  });
};
export type typeUploadFileBody = {
  DonViXuLyID?: number;
  QuaTrinhXuLyID?: number;
  NguoiXuLyID?: number;
  NguoiXuLyHoSoHienTaiID?: number;
  HoSoID?: number;
};
const UploadFile = async (
  params: typeUploadFileBody & {file: DocumentPickerResponse},
) => {
  const {
    DonViXuLyID,
    HoSoID,
    NguoiXuLyHoSoHienTaiID,
    NguoiXuLyID,
    QuaTrinhXuLyID,
    file,
  } = params;

  const formData = new FormData();

  // Fix file structure for Android
  const fileData = {
    uri: file.uri,
    type: file.type || 'application/pdf',
    name: file.name,
  };

  formData.append('FileData', fileData);

  if (DonViXuLyID) formData.append('DonViXuLyID', DonViXuLyID.toString());
  if (QuaTrinhXuLyID)
    formData.append('QuaTrinhXuLyID', QuaTrinhXuLyID.toString());
  if (NguoiXuLyID) formData.append('NguoiXuLyID', NguoiXuLyID.toString());
  if (NguoiXuLyID) formData.append('createdUserID', NguoiXuLyID.toString());
  if (NguoiXuLyHoSoHienTaiID)
    formData.append(
      'NguoiXuLyHoSoHienTaiID',
      NguoiXuLyHoSoHienTaiID.toString(),
    );
  if (HoSoID) formData.append('HoSoID', HoSoID.toString());

  try {
    console.log(JSON.stringify(formData), 'formData');

    const rs = await AcClient.api.post('/File/UploadFile', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      transformRequest: data => data,
    });
    return rs;
  } catch (error) {
    console.log(error, 'errorroorroro');
    throw error;
  }
};
// deleted
const getNguoiKy = (params: {
  TabId: number;
  ControlKey: string;
  DonViID: number;
  PhongBanID: number;
}) => {
  return AcClient.api.get(`/Mobile/GetNguoiKy`, {params});
}; //download file
const DownloadFile = (params: {fileName: string}) => {
  return AcClient.api.get(`/File/DownloadFile`, {
    params: {
      ...params,
    },
  });
};

//delete file
const DeleteFile = (params: {tapTinDinhKemID: number; fileName: string}) => {
  const {fileName, tapTinDinhKemID} = params;
  return AcClient.api.delete('/File/DeleteFile', {
    data: {
      tapTinDinhKemID,
      fileName,
    },
  });
};

const uploadSign = (body: AnyObj) => {
  return AcClient.api.post('/ESign/CauHinhChuKyDienTu', body, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

const getSign = ({loaiChuKy}: {loaiChuKy: number}) => {
  try {
    return AcClient.api.get('/ESign/GetChuKyDienTu', {
      params: {
        LoaiChuKy: loaiChuKy,
      },
    });
  } catch (error) {
    console.log('error At GetSign', JSON.stringify(error));
  }
};

export const handleUser = {
  getInfo,
};
export const handleFunction = {
  NhanHoSo,
  chuyenHoSo,
  pheDuyetHoSo,
  GetDanhMucLinhVuc,
  TuChoiHoSo,
  GetNoiDungGiayTo,
  GetNguoiNhanChuyenHoSo,
  danhSachHoSoPheDuyet,
  getChiTietHoSo,
  GetHoSoKemTheo,
  ThuHoiHoSo,
  GetThongTinQuaTrinhXuLyHienTai,
  GetTongHoSoChuaXuLy,
  GetBuocKeTiep,
  GetComboDataDieuKien,
  GetKetQuaXuLyHoSoCuaDonVi,
  GetComBoLinhVuc,
  GetThongTinHoSoKhongPheDuyet,
  Sign,
  GetCert,
  GetKetQuaXuLyHoSoNguoiDung,
  UploadFile,
  getNguoiKy,
  DownloadFile,
  DeleteFile,
  uploadSign,
  getSign,
};
