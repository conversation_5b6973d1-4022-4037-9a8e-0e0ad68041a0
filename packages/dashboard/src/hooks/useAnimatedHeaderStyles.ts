import Animated, {
  useAnimatedStyle,
  interpolate,
  interpolateColor,
} from 'react-native-reanimated';
type AnimatedHeaderStyles = {
  imageStyle: ReturnType<typeof useAnimatedStyle>;
  headerStyle: ReturnType<typeof useAnimatedStyle>;
};
const HEADER_SCROLL_DISTANCE = 150;
export const useAnimatedHeaderStyles = (
  scrollY: Animated.SharedValue<number>,
): AnimatedHeaderStyles => {
  return {
    imageStyle: useAnimatedStyle(() => ({
      opacity: interpolate(
        scrollY.value,
        [0, HEADER_SCROLL_DISTANCE / 2, HEADER_SCROLL_DISTANCE],
        [1, 0.2, 0],
        'clamp',
      ),
    })),
    headerStyle: useAnimatedStyle(() => ({
      backgroundColor: interpolateColor(
        scrollY.value,
        [0, HEADER_SCROLL_DISTANCE / 2, HEADER_SCROLL_DISTANCE],
        ['transparent', '#366AE2CC', '#366AE2'],
        // 'RGB',
      ),
    })),
  };
};
