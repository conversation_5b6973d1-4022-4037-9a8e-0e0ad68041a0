import * as React from 'react';
import DocumentPicker, {
  DocumentPickerResponse,
} from 'react-native-document-picker';
import {AttachFileIcon} from '../../icons';
import {Button} from 'react-native-paper';
import {handleFunction} from '../../api/dashboard-api';
import {useAuthStore} from '@ac-mobile/common';
import {useQueryClient, useMutation} from 'react-query';
import {Toast} from 'react-native-toast-message/lib/src/Toast';

interface AttachedFileButtonProps {
  HoSoID: any;
  NguoiXuLyHoSoHienTaiID: any;
  QuaTrinhXuLyID: any;
}

export const AttachedFileButton = ({
  HoSoID,
  NguoiXuLyHoSoHienTaiID,
  QuaTrinhXuLyID,
}: AttachedFileButtonProps) => {
  const user = useAuthStore();
  const queryClient = useQueryClient();

  const uploadMutation = useMutation(handleFunction.UploadFile, {
    onSuccess: () => {
      queryClient.invalidateQueries(['useDocumentDetail']);
      queryClient.invalidateQueries(['useNoiDungGiayTo']);
      queryClient.invalidateQueries(['useDanhSachNoiDungGiayTo']);
      Toast.show({
        type: 'success',
        text1: 'Tải file thành công',
      });
    },
    onError: error => {
      Toast.show({
        type: 'error',
        text1: 'Tải file thất bại',
        text2:
          error instanceof Error ? error.message : 'Unknown error occurred',
      });
    },
  });

  const handleUpload = async () => {
    try {
      const res = await DocumentPicker.pick({
        type: [DocumentPicker.types.pdf],
      });

      if (res[0]) {
        await uploadMutation.mutateAsync({
          file: res[0] as DocumentPickerResponse,
          DonViXuLyID: user.user?.donViID,
          HoSoID,
          NguoiXuLyHoSoHienTaiID,
          NguoiXuLyID: user.user?.user_MasterID,
          QuaTrinhXuLyID,
        });
      }
    } catch (error) {
      if (!DocumentPicker.isCancel(error)) {
        Toast.show({
          type: 'error',
          text1: 'Lỗi chọn file',
        });
      }
    }
  };

  return (
    <Button
      mode="outlined"
      onPress={handleUpload}
      icon={() => <AttachFileIcon width={24} height={24} />}
      loading={uploadMutation.isLoading}>
      {uploadMutation.isLoading ? 'Đang tải...' : 'Thêm'}
    </Button>
  );
};
