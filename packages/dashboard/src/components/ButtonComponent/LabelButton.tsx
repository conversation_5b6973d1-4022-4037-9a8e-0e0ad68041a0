import * as React from 'react';
import {Text, View, Pressable} from 'react-native';
import {textStyles, viewStyles} from '../../styles';

export interface LabelButtonItem {
  label: string;
  onPress?: () => void;
  labelStyle?: string;
  style?: string;
}
interface LabelButtonProps {
  buttons?: Array<LabelButtonItem>;
  containerStyle?: string;
}

export const LabelButton = ({buttons, containerStyle}: LabelButtonProps) => {
  return (
    <View
      className={`${viewStyles.viewRow} justify-between ${
        containerStyle || ''
      }`}>
      {buttons?.map((item, index) => {
        return (
          <Pressable
            key={index}
            className={`mx-2 ${item?.style || ''}`}
            onPress={() => {
              item?.onPress && item?.onPress?.();
            }}>
            <Text
              className={`${textStyles.text_14px_bold} text-[#366AE2] ${
                item?.labelStyle || ''
              }`}>
              {item?.label}
            </Text>
          </Pressable>
        );
      })}
    </View>
  );
};
