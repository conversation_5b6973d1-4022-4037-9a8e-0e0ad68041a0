import * as React from 'react';
import {Button, ButtonProps} from 'react-native-paper';
import {textInlineStyles} from '../../styles';

interface ButtonAppProps extends ButtonProps {}

export const ButtonApp = (props: ButtonAppProps) => {
  const {children} = props;
  return (
    <Button
      labelStyle={[textInlineStyles.text_15px_bold, {color: '#ffffff'}]}
      style={{backgroundColor: '#366AE2', borderRadius: 8}}
      {...props}>
      {children}
    </Button>
  );
};
