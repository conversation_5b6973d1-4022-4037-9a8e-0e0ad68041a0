import React from 'react';
import {SimCardIcon} from '../../icons';
import {Button} from 'react-native-paper';

interface DigitalSignatureButtonProps {
  onPress?: () => void;
}

export const DigitalSignatureButton = ({
  onPress,
}: DigitalSignatureButtonProps) => {
  return (
    <Button
      mode="contained"
      // className="rounded-lg"
      onPress={onPress}
      icon={() => <SimCardIcon width={18} height={18} />}>
      <PERSON>ý số
    </Button>
    // <Pressable
    //   className=" flex-row items-center h-[30px] px-2 rounded-lg bg-[#366AE2]"
    //   onPress={onPress}>
    //   <SimCardIcon width={18} height={18} />
    //   <Text className={`${textStyles.text_13px_bold} text-white ml-2`}>
    //     {'Ký số'}
    //   </Text>
    // </Pressable>
  );
};
