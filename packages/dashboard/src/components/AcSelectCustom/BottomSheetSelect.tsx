import {BottomSheetMethods} from '@gorhom/bottom-sheet/lib/typescript/types';
import React, {useEffect} from 'react';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import {textStyles} from '../../styles/QNH_textStyle';
import {selectCustomOption} from './SelectCustom';
import {CustomBottomSheet} from '../AcBottomSheet/CustomBottomSheet';
import {Button} from 'react-native-paper';

type Props = {
  title: string;
  options: selectCustomOption[];
  selectedItem: selectCustomOption | null;
  show: boolean;

  onHandleChange: (value: selectCustomOption) => void;
  onHandleClose: () => void;
};

export const BottomSheetSelect = React.memo(
  ({
    title,
    options,
    selectedItem,
    show,
    onHandleChange,
    onHandleClose,
  }: Props) => {
    const [showDialogStart, setShowDialogStart] =
      React.useState<boolean>(false);

    const dialog = React.useRef<BottomSheetMethods | null>(null);

    const handleCloseDialogStart = React.useCallback(() => {
      if (!dialog.current) {
        return;
      }
      dialog.current?.close();
      setShowDialogStart(false);
    }, [dialog]);

    const onHandleSelectItem = React.useCallback(
      (option: selectCustomOption) => {
        // setSelectedItem(option);
      },
      [],
    );

    console.log({show, dialog});

    const handleShowDialogDonVi = React.useCallback(() => {
      if (!dialog.current) {
        return;
      }

      console.log('handleShowDialogDonVi');

      dialog.current?.expand();
      setShowDialogStart(true);
    }, [dialog]);

    React.useEffect(() => {
      if (show) {
        handleShowDialogDonVi();
      }
      if (!show) {
        handleCloseDialogStart();
      }
    }, [show, handleShowDialogDonVi, handleCloseDialogStart]);

    const handleSubmit = () => {};

    return (
      <View>
        {showDialogStart && (
          <TouchableWithoutFeedback onPress={onHandleClose}>
            <View style={styles.overlayDialogStart} />
          </TouchableWithoutFeedback>
        )}

        <CustomBottomSheet
          snapPoints={['70%']}
          refProp={dialog}
          onHandleChange={() => {}}>
          <View style={styles.contentBottomSheet}>
            <View style={styles.info}>
              <Text style={styles.title} className={`${textStyles.h6}`}>
                {title || ''}
              </Text>
              <View>
                {options.map((option: selectCustomOption, index: number) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.item,
                      selectedItem?.id === option.id && styles.selectedItem,
                    ]}
                    onPress={() => onHandleSelectItem(option)}>
                    <Text>{option.label ?? 'Chưa rõ'}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View>
              <Button
                mode="contained"
                style={styles.button}
                onPress={() => handleSubmit()}>
                Xác nhận
              </Button>
            </View>
          </View>
        </CustomBottomSheet>
      </View>
    );
  },
);

const styles = StyleSheet.create({
  label: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  selector: {
    padding: 12,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    marginVertical: 8,
    backgroundColor: '#fff',
    justifyContent: 'center',
  },
  selectorText: {
    fontSize: 16,
    color: '#333',
  },

  button: {
    borderRadius: 8,
  },
  contentBottomSheet: {
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
    position: 'absolute',
    zIndex: 999,
  },
  overlayDialogStart: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  overlayDialogSelect: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)', // Gray background with transparency
  },
  required: {
    color: 'red',
  },
  item: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  selectedItem: {
    backgroundColor: '#e0f7fa', // Màu nền cho item được chọn
  },
  title: {
    textAlign: 'center',
  },
  info: {
    display: 'flex',
    flexDirection: 'column',
    gap: 16,
  },
});
