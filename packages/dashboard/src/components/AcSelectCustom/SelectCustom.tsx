import React from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {textStyles} from '../../styles/QNH_textStyle';

export type selectCustomOption = {
  id: string | number;
  label: string;
  value: any;
};

type props = {
  placeholder?: string;
  label?: string;
  required?: boolean;
  selectedItem: selectCustomOption | null;

  handleShowOptions: () => void;
};

export const SelectCustom = React.memo(
  ({
    placeholder = 'Chọn dữ liệu',
    label,
    required = false,
    selectedItem,
    handleShowOptions,
  }: props) => {
    return (
      <View>
        {label && (
          <View style={styles.label}>
            <Text className={`${textStyles.subTitle2}`}>
              {label}
              {required && <Text style={styles.required}>*</Text>}
            </Text>
          </View>
        )}

        {/* Button Outline */}
        <TouchableOpacity style={styles.selector} onPress={handleShowOptions}>
          <Text style={styles.selectorText}>
            {selectedItem ? selectedItem.label : placeholder}
          </Text>
        </TouchableOpacity>
      </View>
    );
  },
);

const styles = StyleSheet.create({
  label: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  selector: {
    padding: 12,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    marginVertical: 8,
    backgroundColor: '#fff',
    justifyContent: 'center',
  },
  selectorText: {
    fontSize: 16,
    color: '#333',
  },

  button: {
    borderRadius: 8,
  },
  contentBottomSheet: {
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
    position: 'absolute',
    zIndex: 999,
  },
  overlayDialogStart: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  overlayDialogSelect: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)', // Gray background with transparency
  },
  required: {
    color: 'red',
  },
  item: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  selectedItem: {
    backgroundColor: '#e0f7fa', // Màu nền cho item được chọn
  },
  title: {
    textAlign: 'center',
  },
  info: {
    display: 'flex',
    flexDirection: 'column',
    gap: 16,
  },
});
