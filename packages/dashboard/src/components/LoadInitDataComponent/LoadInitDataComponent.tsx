import React, {createContext, useState, ReactNode} from 'react';

type LoadInitContextType = {
  data?: boolean;
  setData: (newData: boolean) => void;
};

const LoadInitContext = createContext<LoadInitContextType | undefined>(
  undefined,
);

export const LoadInitProvider = ({children}: {children: ReactNode}) => {
  const [data, setData] = useState<boolean>(false);

  return (
    <LoadInitContext.Provider value={{data, setData}}>
      {children}
    </LoadInitContext.Provider>
  );
};

export const useLoadInit = () => {
  const context = React.useContext(LoadInitContext);
  if (!context) {
    throw new Error('useLoadInit phải được sử dụng trong LoadInitProvider');
  }
  return context;
};
