import {Pressable, Text, View} from 'react-native';
import React, {memo} from 'react';
import {useNavigation} from '@react-navigation/native';
import {BackRow} from '../../icons';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {textStyles} from '../../styles';
import {useDanhSachHoSo} from '../../stores';
export type HeaderComponentProp = {
  label?: string;
  Icons?: React.ReactNode;
  isBack?: boolean;
  press?: () => void;
};
export const HeaderComponent = memo(
  ({Icons, isBack, label, press}: HeaderComponentProp) => {
    const nav = useNavigation<any>();
    const insets = useSafeAreaInsets();
    const {filter} = useDanhSachHoSo();
    return (
      <View
        className="flex-row bg-white px-2 pb-4 items-center"
        style={[
          {
            paddingTop: insets.top,
          },
        ]}>
        <View className="flex-1">
          {/* <View className="flex-row items-center"> */}
          <View className="flex-row items-center">
            {isBack && (
              <Pressable
                className="w-10 h-10 items-center justify-center"
                onPress={() => nav.goBack()}>
                <BackRow width={24} height={24} />
              </Pressable>
            )}
            <Text
              className={`${textStyles.text_20px_bold} flex-1 mx-2`}
              numberOfLines={1}>
              {label}
            </Text>
          </View>
        </View>

        {Icons && (
          <View>
            {filter.isFilter && (
              <View className="w-2 h-2 bg-red-500 rounded-full absolute top-2 right-2"></View>
            )}
            <Pressable onPress={press}>{Icons}</Pressable>
          </View>
        )}
      </View>
    );
  },
);
