import React, {memo} from 'react';
import {Appbar, MD3Theme, Text, useTheme} from 'react-native-paper';
import {IconSource} from 'react-native-paper/lib/typescript/components/Icon';
import {textStyles} from '../../styles';
import {useNavigation} from '@react-navigation/native';
import {StyleSheet} from 'react-native';
export type CCAppBarProp = {
  isBack?: boolean;
  label?: React.ReactNode;
  iconsAction?: {
    icon: IconSource;
    onPress: () => void;
  }[];
  backgroundColor?: string;
  icons?: React.ReactNode;
};
export const CCAppBar = memo(
  ({isBack, label, iconsAction, backgroundColor}: CCAppBarProp) => {
    const nav = useNavigation<any>();
    const theme = useTheme();
    const styles = themedStyles(theme);
    return (
      <Appbar.Header
        theme={{colors: {surface: backgroundColor ?? theme.colors.background}}}
        mode="small">
        {isBack ? <Appbar.BackAction onPress={() => nav.goBack()} /> : <></>}
        <Appbar.Content
          title={
            <Text
              className={`${textStyles.text_20px_bold}`}
              numberOfLines={1}
              style={styles.title}>
              {label}
            </Text>
          }
        />
        {iconsAction?.map((e, i) => {
          return <Appbar.Action icon={e.icon} onPress={e.onPress} key={i} />;
        })}
      </Appbar.Header>
    );
  },
);
const themedStyles = (theme: MD3Theme) =>
  StyleSheet.create({
    title: {
      marginLeft: 8,
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.onBackground,
    },
  });
