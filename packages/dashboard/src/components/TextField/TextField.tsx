import React, {useState} from 'react';
import {Text, View, TextInputProps, TextInput} from 'react-native';
import {textStyles} from '../../styles';
import {useImperativeHandle} from 'react';

interface TextFieldProps extends TextInputProps {
  label?: string;
  isRequire?: boolean;
  customError?: string;
}
export interface TextFieldRef {
  checkError?: () => void;
  getValue?: () => string;
}

export const TextField = React.forwardRef<TextFieldRef, TextFieldProps>(
  (props: TextFieldProps, ref) => {
    const {label, isRequire, customError} = props;
    const [text, setText] = useState<string>('');
    const [error, setError] = useState<boolean>(false);

    useImperativeHandle(
      ref,
      () => {
        return {
          // ... your methods ...
          checkError: () => {
            if (isRequire && !text) {
              setError(true);
            }
          },
          getValue: () => {
            return text;
          },
        };
      },
      [],
    );

    return (
      <View>
        {label ? (
          <Text
            className={`${textStyles.text_14px_medium} mb-2 text-[#637381]`}>
            {label}
            {isRequire ? <Text className="text-[#FF5630]">{' *'}</Text> : null}
          </Text>
        ) : null}
        <View className="py-4 px-[14px] border border-[#919EAB33] rounded-lg">
          <TextInput
            className={`text-[14px] font-normal text-[#212B36] ${
              props?.multiline ? 'h-[66px] ' : ''
            } ${error ? 'text-[#FF5630]' : ''}`}
            value={text}
            placeholder={`Nhập ${label?.toLowerCase()}`}
            textAlignVertical="top"
            placeholderTextColor={'#919EAB'}
            onChangeText={(txt: string) => setText(txt)}
            onFocus={e => {
              if (error) {
                setError(false);
              }
              props?.onFocus && props?.onFocus(e);
            }}
            onBlur={e => {
              if (isRequire && !text) {
                setError(true);
              }
              props?.onBlur && props?.onBlur(e);
            }}
            {...props}
          />
        </View>
      </View>
    );
  },
);
