import React, {memo} from 'react';
import {Pressable, StyleProp, Text, View, ViewStyle} from 'react-native';
import {textStyles} from '../../styles';
import {SendIcon} from '../../icons';
import {<PERSON>ton, Chip, useTheme} from 'react-native-paper';

type CardComponentType = {
  isPending?: boolean;
  headerCard?: {label?: string; value?: string};
  bodyCard?: {label?: string; value?: string}[];
  expired?: string;
  Icons?: React.ReactNode;
  timeHandle?: string;
  buttons?: {
    label?: string;
    press?: () => void;
    style?: StyleProp<ViewStyle>;
    type?: string;
    IconsRightButton?: React.ReactNode;
    className?: string;
  }[];
  onPressItem?: () => void;
  status?: string;
};
export const CardComponent = memo(
  ({
    bodyCard,
    headerCard,
    Icons,
    buttons,
    timeHandle,
    onPressItem,
    status,
  }: CardComponentType) => {
    const theme = useTheme();
    return (
      <Pressable
        onPress={onPressItem}
        className="p-4 rounded-xl shadow-lg  mt-2.5 space-y-2.5"
        style={{backgroundColor: theme.colors.background}}>
        <View className="flex-row items-center">
          <View>{Icons && Icons}</View>
          <View
            className="ml-3 flex-1 flex-row justify-between items-center pb-2"
            style={{
              borderBottomWidth: 0.3,
              borderBottomColor: theme.colors.outline,
            }}>
            <View className="flex ">
              <Text
                className={`${textStyles.text_14px_regular}`}
                style={{color: theme.colors.onBackground}}>
                {headerCard?.label}
              </Text>
              <Text
                className="text-base font-bold"
                style={{color: theme.colors.onBackground}}>
                {headerCard?.value}
              </Text>
              {status && (
                <Chip
                  icon={() => (
                    <SendIcon
                      width={25}
                      height={25}
                      fill={theme.colors.tertiary}
                    />
                  )}
                  style={{backgroundColor: theme.colors.tertiaryContainer}}
                  textStyle={{color: theme.colors.tertiary}}>
                  {status}
                </Chip>
              )}
            </View>
          </View>
        </View>
        <View className="gap-2.5 w-full">
          {bodyCard?.map((i, e) => {
            if (i.value !== null) {
              return (
                <View key={e} className="w-[98%]">
                  <Text numberOfLines={2} className="text-sm font-normal">
                    <Text style={{color: theme.colors.outline}}>{i.label}</Text>
                    <Text
                      style={{color: theme.colors.onBackground}}
                      numberOfLines={2}
                      ellipsizeMode="tail">
                      {i.value}
                    </Text>
                  </Text>
                </View>
              );
            }
          })}
        </View>
        <View className="flex-row flex-wrap justify-between items-center mt-1.5 gap-2">
          <View className="flex flex-row items-center w-full">
            {timeHandle && (
              <View>
                <Chip
                  style={{
                    backgroundColor: theme.colors.tertiaryContainer,
                  }}
                  textStyle={{
                    color: theme.colors.tertiary,
                  }}>
                  {timeHandle}
                </Chip>
              </View>
            )}
            <View className="flex-row flex-wrap gap-2 justify-end flex-1">
              {buttons?.map((e, i) => {
                return (
                  <Button
                    style={e?.style}
                    icon={e?.IconsRightButton && (e?.IconsRightButton as any)}
                    key={i}
                    onPress={e.press}
                    mode={i % 2 === 0 ? 'contained' : 'outlined'}>
                    {e.label}
                  </Button>
                );
              })}
            </View>
          </View>
        </View>
      </Pressable>
    );
  },
);
