import BottomSheet, {BottomSheetScrollView} from '@gorhom/bottom-sheet';
import {BottomSheetMethods} from '@gorhom/bottom-sheet/lib/typescript/types';
import React from 'react';
import {StyleSheet} from 'react-native';

type props = {
  children: React.ReactNode;
  refProp: React.RefObject<BottomSheetMethods>;
  snapPoints?: string[];

  onHandleChange: (index: number) => void;
};

export const CustomBottomSheet = ({
  children,
  refProp,
  snapPoints = ['50%'],

  onHandleChange,
}: props) => {
  return (
    <BottomSheet
      enableDynamicSizing={false}
      enableContentPanningGesture={true}
      enablePanDownToClose
      onChange={(index: number) => onHandleChange(index)}
      snapPoints={snapPoints}
      ref={refProp}
      index={-1}>
      <BottomSheetScrollView
        keyboardShouldPersistTaps="handled"
        contentContainerStyle={[styles.contentContainer]}>
        {children}
      </BottomSheetScrollView>
    </BottomSheet>
  );
};

const styles = StyleSheet.create({
  contentContainer: {
    flexGrow: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
    zIndex: 1000,
    paddingBottom: 16,
  },
});
