import {View} from 'react-native';
import React, {memo} from 'react';
import {List} from 'react-native-paper';
type OptionComponent = {
  label?: string;
  IconsRight?: React.ReactNode;
  IconsLeft?: React.ReactNode;
  press?: () => {};
  show?: boolean;
};

type selectOptions = {
  options?: OptionComponent[];
};
export const OptionsComponent = memo(({options}: selectOptions) => {
  return (
    <View className="rounded-xl px-3">
      <View className="mt-4 bg-white rounded-xl">
        {options &&
          options.map(
            (e, i) =>
              e?.show && (
                <List.Item
                  key={i}
                  title={e?.label}
                  left={props =>
                    e?.IconsLeft && <View {...props}>{e?.IconsLeft}</View>
                  }
                  right={props =>
                    e?.IconsRight && <View {...props}>{e?.IconsRight}</View>
                  }
                  onPress={e?.press}
                />
              ),
          )}
      </View>
    </View>
  );
});
