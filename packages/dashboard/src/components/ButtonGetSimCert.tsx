import React from 'react';
import {Button} from 'react-native-paper';
import {useCertStore} from '../stores';

interface ButtonGetSimCertProps {
  label?: string;
  onReloadStart?: () => void;
  onReloadComplete?: () => void;
  buttonStyle?: string;
  labelStyle?: string;
}

export const ButtonGetSimCert: React.FC<ButtonGetSimCertProps> = ({}) => {
  const cert = useCertStore(state => state.cert);
  const getCert = useCertStore(state => state.getCert);
  const loading = useCertStore(state => state.loading);
  const handlePress = async () => {
    try {
      await getCert();
    } catch (error) {}
  };

  return (
    <Button
      mode="contained"
      onPress={handlePress}
      disabled={loading === 'loading'}
      loading={loading === 'loading'}>
      {cert && cert?.length > 0 ? 'Đã lấy chứng chỉ' : 'L<PERSON>y chứng chỉ'}
    </Button>
  );
};
