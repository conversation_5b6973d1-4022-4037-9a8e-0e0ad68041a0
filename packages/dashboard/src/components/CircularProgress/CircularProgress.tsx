import React, {useMemo, useState} from 'react';
import {Dimensions, Text, View} from 'react-native';
import Svg, {Circle, Defs, G, LinearGradient, Stop} from 'react-native-svg';
import {viewStyles} from '../../styles';
import {useTheme} from 'react-native-paper';
import {
  ProgressInfo,
  ProgressSuccess,
  ProgressWarning,
} from '../CustomStyle/ColorStyleCustom';

export const CircularProgress = ({
  total,
  circle1,
  circle2,
  circle3,
}: {
  total: number;
  circle1: number;
  circle2: number;
  circle3: number;
}) => {
  const theme = useTheme();
  const size = Dimensions.get('window').width * 0.6; // <PERSON>ích thước tổng thể
  const strokeWidth = 12; // Độ dày của mỗi vòng
  const radius = (size - strokeWidth) / 2; // Bán kính vòng tròn
  const circumference = 2 * Math.PI * radius; // Chu vi vòng tròn
  const [progressValues, setProgressValues] = useState<number[]>([0, 0, 0]);
  // COMMENT: Old code
  // const gradientColors = [
  //   [theme.colors.secondaryContainer, theme.colors.secondary], // Gradient cho vòng 1
  //   [theme.colors.primaryContainer, theme.colors.primary], // Gradient cho vòng 2
  //   [theme.colors.tertiaryContainer, theme.colors.tertiary], // Gradient cho vòng 3
  // ];
  const gradientColors = [
    [ProgressWarning, ProgressWarning], // Gradient cho vòng 1
    [ProgressInfo, ProgressInfo], // Gradient cho vòng 2
    [ProgressSuccess, ProgressSuccess], // Gradient cho vòng 3
  ];
  useMemo(() => {
    setProgressValues([circle1 / total, circle2 / total, circle3 / total]);
  }, [circle1, circle2, circle3, total]);
  return (
    <View className={viewStyles.center}>
      <Svg width={size} height={size}>
        <Defs>
          {gradientColors.map((colors, index) => (
            <LinearGradient
              key={`gradient-${index}`}
              id={`gradient-${index}`}
              x1="0%"
              y1="0%"
              x2="100%"
              y2="0%">
              <Stop
                offset="0%"
                stopColor={total == 0 ? '#edeff1' : colors[0]}
              />
              <Stop
                offset="100%"
                stopColor={total == 0 ? '#edeff1' : colors[1]}
              />
            </LinearGradient>
          ))}
        </Defs>
        <G rotation="-90" origin={`${size / 2}, ${size / 2}`}>
          {progressValues.map((progress, index) => {
            const offset = circumference * (1 - progress);
            const adjustedRadius = radius - index * (strokeWidth + 5); // Giảm bán kính từng vòng
            return (
              <React.Fragment key={index}>
                {/* Vòng nền (phần còn lại) */}
                <Circle
                  cx={size / 2}
                  cy={size / 2}
                  r={adjustedRadius}
                  stroke={theme.colors.surfaceVariant} // Màu nền
                  strokeWidth={strokeWidth}
                  fill="none"
                />
                <Circle
                  cx={size / 2}
                  cy={size / 2}
                  r={adjustedRadius}
                  stroke={`url(#gradient-${index})`} // Áp dụng gradient
                  strokeWidth={strokeWidth}
                  strokeDasharray={`${circumference} ${circumference}`}
                  strokeDashoffset={offset}
                  fill="none"
                  strokeLinecap={progress < 1 ? 'round' : 'butt'} // Bo tròn 2 đầu nếu progress < 1
                />
              </React.Fragment>
            );
          })}
        </G>
      </Svg>
      <View className="absolute items-center justify-center">
        <Text
          style={{fontSize: 14, fontWeight: 600, color: theme.colors.outline}}>
          Tổng số hồ sơ
        </Text>
        <Text
          style={{color: theme.colors.error, fontWeight: 800, fontSize: 24}}
          className={'leading-10'}>
          {total}
        </Text>
      </View>
    </View>
  );
};
