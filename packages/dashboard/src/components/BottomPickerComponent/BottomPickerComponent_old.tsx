import React, {forwardRef, useCallback, useEffect, useState} from 'react';
import {
  Animated,
  FlatList,
  Image,
  Pressable,
  RefreshControl,
  Text,
  View,
} from 'react-native';
import {ForwardRefComponent} from 'react-native-paper/lib/typescript/utils/forwardRef';
import {Assets} from '../../assets';
import {
  BottomComponent,
  BottomSheetProp,
} from '../BottomComponent/BottomComponent';
import {SearchComponent} from '../SearhComponent/SearchComponent';
import {ActivityIndicator} from 'react-native-paper';
type BottomPickerComponent = {
  title?: string;
  isLoading?: boolean;
  options?:
    | {
        label: string;
        labelRightTop?: string;
        labelRightBottom?: string;
        value: string;
      }[]
    | any;
  refresh?: boolean;
  isSearch?: boolean;
  close: () => void;
  typeSellect: 'click' | 'click-multiple';
  typeRender: 'hasIcon' | 'NoIcon';
  onChange: (e?: any) => void;
  snapPoint?: number;
  translateX?: any;
  initData?:
    | {
        label: string;
        labelRightTop?: string;
        labelRightBottom?: string;
        value: string;
      }[]
    | any;
};

export const BottomPickerComponent: ForwardRefComponent<
  BottomSheetProp,
  BottomPickerComponent
> = forwardRef(
  (
    {
      title,
      options,
      refresh,
      close,
      onChange,
      isSearch,
      typeRender = 'NoIcon',
      typeSellect = 'click',
      snapPoint,
      isLoading = false,
      translateX,
      initData,
    },
    ref,
  ) => {
    const [selectedOptions, setSelectedOptions] = useState<any[]>(
      initData || [],
    );
    const animatedValue = new Animated.Value(0);
    const [selectedOneOptions, setSelectedOneOptions] = useState<any>('asc');
    const [search, setSearch] = useState<string>('');
    const getData = (e: any) => {
      onChange(
        typeSellect == 'click-multiple' ? selectedOptions : selectedOneOptions,
      );
    };
    useEffect(() => {
      setSelectedOptions(initData ? initData : []);
      setSelectedOneOptions(initData ? initData : '');
    }, [initData]);
    const toggleOption = useCallback(
      ({label, value}: {label: string; value: string}) => {
        typeSellect == 'click-multiple'
          ? setSelectedOptions(prev =>
              prev.some(item => item.value === value)
                ? prev.filter(item => item.value !== value)
                : [...prev, {label, value}],
            )
          : setSelectedOneOptions(
              selectedOneOptions.value == value ? ' ' : {label, value},
            );
      },
      [typeRender, typeSellect, selectedOneOptions, selectedOptions, initData],
    );

    const renderItem = useCallback(
      ({item}: {item: any}) => {
        switch (typeRender) {
          case 'NoIcon':
            return (
              <Animated.View
                style={{
                  transform: [{translateX: translateX || animatedValue}],
                  maxHeight: 350,
                }}>
                <Pressable
                  className={`p-2 bg-white my-0.5 rounded-lg ${
                    typeSellect === 'click-multiple'
                      ? selectedOptions.some(i => i.value == item.value)
                        ? 'bg-[#919EAB29]'
                        : ''
                      : selectedOneOptions?.value == item?.value
                      ? 'bg-[#919EAB29]'
                      : ''
                  }`}
                  onPress={() =>
                    toggleOption({label: item?.label, value: item?.value})
                  }>
                  <Text
                    className={`text-[16px] ml-2.5 ${
                      typeSellect === 'click-multiple'
                        ? selectedOptions.some(i => i.value == item.value)
                          ? 'font-bold'
                          : ''
                        : selectedOneOptions?.value == item?.value
                        ? 'font-bold'
                        : ''
                    }`}>
                    {item.label}
                  </Text>
                </Pressable>
              </Animated.View>
            );
          case 'hasIcon':
            return (
              <Animated.View
                style={{
                  transform: [{translateX: translateX || animatedValue}],
                  maxHeight: 350,
                }}>
                <Pressable
                  className={`bg-white rounded-lg ${
                    typeSellect === 'click-multiple'
                      ? selectedOptions.some(i => i.value == item.value)
                        ? 'bg-[#919EAB29] '
                        : ''
                      : selectedOneOptions?.value == item?.value
                      ? 'bg-[#919EAB29]'
                      : ''
                  }`}
                  onPress={() =>
                    toggleOption({label: item?.label, value: item?.value})
                  }>
                  <View className="p-2  flex-row justify-start items-center">
                    <Image
                      className="w-10 h-10 rounded-full"
                      source={Assets.userIcon}
                    />
                    <Text
                      className={`text-base ml-2 w-5/6 ${
                        (typeSellect === 'click-multiple' &&
                          selectedOptions.includes({
                            label: item?.label,
                            value: item?.value,
                          })) ||
                        selectedOneOptions?.value === item?.value
                          ? ''
                          : ''
                      }`}
                      numberOfLines={2}>
                      {item?.label}
                    </Text>
                  </View>
                  <View className="flex justify-center items-end">
                    <Text className="text-[16px] ml-2 text-[rgba(99, 115, 129, 1)] font-normal text-[12px]">
                      {item?.labelRightTop}
                    </Text>
                    <Text className="text-[16px] ml-2 font-normal text-[12px] text-[rgba(99, 115, 129, 1)] text-[rgba(145, 158, 171, 1)]">
                      {item?.labelRightBottom}
                    </Text>
                  </View>
                </Pressable>
              </Animated.View>
            );
          default:
            return (
              <Animated.View
                style={{
                  transform: [{translateX: translateX || animatedValue}],
                  maxHeight: 350,
                }}>
                <Pressable
                  className={`p-3 bg-white my-1 rounded-lg ${
                    (typeSellect == 'click-multiple' &&
                      selectedOptions.includes({
                        label: item?.label,
                        value: item?.value,
                      })) ||
                    selectedOneOptions == item?.value
                      ? 'bg-[#919EAB29]'
                      : ''
                  }`}
                  onPress={() => toggleOption(item?.value)}>
                  <Text
                    className={`text-[16px] ml-2.5 ${
                      typeSellect === 'click-multiple'
                        ? selectedOptions.includes({
                            label: item?.label,
                            value: item?.value,
                          })
                          ? 'font-bold'
                          : ''
                        : selectedOneOptions === item?.value
                        ? 'font-bold'
                        : ''
                    }`}>
                    {item.label}
                  </Text>
                </Pressable>
              </Animated.View>
            );
        }
      },
      [
        selectedOptions,
        selectedOneOptions,
        search,
        typeRender,
        typeSellect,
        initData,
      ],
    );
    const handleChangeSearch = (e: string) => {
      setSearch(e);
    };
    return (
      <BottomComponent ref={ref} snapPoints={snapPoint ? snapPoint : 500}>
        <View className="flex-1 py-2">
          <View className="items-center">
            <Animated.View
              style={{
                transform: [{translateX: translateX || animatedValue}],
              }}>
              <Text className="font-bold text-lg mb-2 p-1">{title}</Text>
            </Animated.View>
          </View>
          {isSearch && (
            <SearchComponent search={search} setSearch={handleChangeSearch} />
          )}

          {!isLoading ? (
            <>
              <FlatList
                data={
                  options
                    ? options.filter((item: any) =>
                        item.label.match(new RegExp(`.*${search}.*`, 'i')),
                      )
                    : []
                }
                refreshControl={
                  false ? (
                    <RefreshControl
                      refreshing={true}
                      onRefresh={() => {}}
                      tintColor={'red'}
                      colors={['red']}
                    />
                  ) : undefined
                }
                refreshing={true}
                renderItem={renderItem}
                nestedScrollEnabled
                onEndReachedThreshold={0.5}
              />
            </>
          ) : (
            <>
              <View className="flex-1 justify-center items-center">
                <ActivityIndicator size={'large'} />
              </View>
            </>
          )}
          <View className="items-center px-3">
            <Pressable
              className="bg-blue-500 p-3 rounded-lg mt-4 mb-2 w-[95%] items-center"
              onPress={getData}>
              <Text className="text-white text-base font-bold">
                {'Xác nhận'}
              </Text>
            </Pressable>
          </View>
        </View>
      </BottomComponent>
    );
  },
);
