import * as React from 'react';
import {Text} from 'react-native';
import {MailIcon} from '../../icons';
import {ActivityIndicator, Card, useTheme} from 'react-native-paper';
import {useCongchucAPITongHoSoChuaXuLy} from '../../useQueryState/UseCongChucAPITongHoSoChuaXuLy';
import {useAuthStore} from '@ac-mobile/common';

export const CardDocument = () => {
  const theme = useTheme();
  const {user} = useAuthStore();

  const {data, isLoading} = useCongchucAPITongHoSoChuaXuLy({
    DonViID: user?.donViID,
    PhongBanID: user?.phongBanID,
    ChucNangHienTai: user
      ? user.metadata.chucNangHienTai.map((item: any) => item.ma).join(',')
      : '',
    UserID: user?.user_PortalID,
  });

  return (
    <Card
      style={{
        backgroundColor: theme.colors.surface,
        padding: 10,
        marginBottom: 16,
        marginHorizontal: 16,
      }}
      elevation={0}>
      <Card.Title
        title={
          <Text style={{color: theme.colors.onSurface, fontWeight: 600}}>
            {'<PERSON><PERSON> sơ chưa xử lý của tôi'}
          </Text>
        }
        subtitle={
          isLoading ? (
            <ActivityIndicator />
          ) : (
            <Text
              style={{color: theme.colors.error, fontWeight: 800, fontSize: 24}}
              className={'leading-10'}>
              {data?.data?.data || 0}
            </Text>
          )
        }
        right={_ => <MailIcon width={56} height={56} />}
      />
    </Card>
  );
};
