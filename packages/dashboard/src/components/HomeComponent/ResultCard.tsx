import React, {memo} from 'react';
import {Text, View} from 'react-native';
import {Badge, useTheme} from 'react-native-paper';
import {textStyle} from '../CustomStyle/TextStyleCustom';

interface ResultCardProps {
  backgroundColor: string;
  label: string;
  value?: number;
}

export const ResultCard = memo(
  ({backgroundColor, label, value}: ResultCardProps) => {
    const theme = useTheme();

    return (
      <View>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <Badge style={{backgroundColor}} size={8} />
          <Text style={[textStyle, {color: theme.colors.onSurface}]}>
            {label}
          </Text>
        </View>
        <View className="ml-5">
          <Text
            style={{
              color: theme.colors.onSurface,
              fontWeight: 600,
              fontSize: 12,
            }}>
            {value || 0}
            {' hồ sơ'}
          </Text>
        </View>
      </View>
    );
  },
);
