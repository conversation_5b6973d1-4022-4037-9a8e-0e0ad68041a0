import React, {memo, useEffect, useRef, useState} from 'react';
import {Text, View} from 'react-native';
import {CircularProgress} from '../CircularProgress/CircularProgress';
import {useAuthStore} from '@ac-mobile/common';
import {
  MonthYearPicker,
  MonthYearPickerRef,
} from '../PickerComponent/MonthYearPicker';
import {
  ActivityIndicator,
  Card,
  Chip,
  Divider,
  useTheme,
} from 'react-native-paper';
import {useCongChucAPIKetQuaXuLyCuaDonVi} from '../../useQueryState/UseCongChucAPIKetQuaXuLyHoSoCuaDonVi';
import {ResultCard} from './ResultCard';
import {useCongChucAPIKetQuaXuLyCuaNguoiDung} from '../../useQueryState/UseCongChucAPIKetQuaXuLyHoSoCuaNguoiDung';
import {
  ProgressInfo,
  ProgressSuccess,
  ProgressWarning,
} from '../CustomStyle/ColorStyleCustom';

export const CardResultDocument = memo(({isMe}: {isMe?: boolean}) => {
  const monthYearPickerRef = useRef<MonthYearPickerRef>(null);
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const {user} = useAuthStore();
  const [circleData, setCircleData] = useState({
    total: 0,
    circle1: 0,
    circle2: 0,
    circle3: 0,
  });
  const {data, isLoading} = useCongChucAPIKetQuaXuLyCuaDonVi({
    Thang: selectedMonth,
    Nam: selectedYear,
    DonViID: user?.donViID || 0,
  });
  const {data: data2, isLoading: isLoading2} =
    useCongChucAPIKetQuaXuLyCuaNguoiDung({
      Thang: selectedMonth,
      Nam: selectedYear,
      DonViID: user?.donViID || 0,
      UserID: user?.user_PortalID || 0,
    });
  useEffect(() => {
    if (!isMe) {
      setCircleData({
        total: data?.data?.data?.tongSoHoSoDaTiepNhan,
        circle1: data?.data?.data?.tongSoHoSoDaTiepNhan,
        circle2: data?.data?.data?.tongSoHoSoDaGiaiQuyet,
        circle3: data?.data?.data?.tongSoHoSoDungHan,
      });
    } else {
      setCircleData({
        total: data2?.data?.data?.tongSoHoSoDaTiepNhan,
        circle1: data2?.data?.data?.tongSoHoSoDaTiepNhan,
        circle2: data2?.data?.data?.tongSoHoSoDaGiaiQuyet,
        circle3: data2?.data?.data?.tongSoHoSoDungHan,
      });
    }
  }, [data, isMe, data2]);

  const theme = useTheme();

  return (
    <Card
      style={{
        backgroundColor: theme.colors.surface,
        paddingHorizontal: 16,
        paddingTop: 16,
        marginHorizontal: 16,
      }}
      elevation={0}>
      <View
        style={[
          {
            display: 'flex',
            justifyContent: 'space-between',
            flexDirection: 'row',
            flexWrap: 'wrap',
            alignItems: 'center',
            marginBottom: 16,
          },
        ]}>
        <Text
          style={{
            color: theme.colors.onSurface,
            fontWeight: 600,
            fontSize: 17,
          }}>
          {'Kết quả xử lý hồ sơ'}
        </Text>
        <Chip
          style={{
            backgroundColor: theme.colors.primaryContainer,
          }}
          textStyle={{
            color: theme.colors.primary,
          }}
          onPress={() => {
            monthYearPickerRef?.current?.open(selectedMonth, selectedYear);
          }}>
          Tháng {selectedMonth}, {selectedYear}
        </Chip>
      </View>
      <View style={{marginBottom: 10}}>
        <CircularProgress
          total={circleData.total}
          circle1={circleData.circle1}
          circle2={circleData.circle2}
          circle3={circleData.circle3}
        />
      </View>
      <Divider />
      <View
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-around',
          paddingBottom: 10,
          gap: 10,
          marginTop: 10,
        }}>
        {isLoading || isLoading2 ? (
          <ActivityIndicator />
        ) : (
          <>
            {/* <ResultCard
              backgroundColor={theme.colors.secondary}
              label="Đã tiếp nhận"
              value={circleData.circle1}
            />
            <ResultCard
              backgroundColor={theme.colors.primary}
              label="Đã giải quyết"
              value={circleData.circle2}
            />
            <ResultCard
              backgroundColor={theme.colors.tertiary}
              label="Đúng hạn"
              value={circleData.circle3}
            /> */}
            <ResultCard
              backgroundColor={ProgressWarning}
              label="Đã tiếp nhận"
              value={circleData.circle1}
            />
            <ResultCard
              backgroundColor={ProgressInfo}
              label="Đã giải quyết"
              value={circleData.circle2}
            />
            <ResultCard
              backgroundColor={ProgressSuccess}
              label="Đúng hạn"
              value={circleData.circle3}
            />
          </>
        )}
      </View>
      <MonthYearPicker
        ref={monthYearPickerRef}
        onConfirm={(month, year) => {
          setSelectedMonth(month);
          setSelectedYear(year);
        }}
      />
    </Card>
  );
});
