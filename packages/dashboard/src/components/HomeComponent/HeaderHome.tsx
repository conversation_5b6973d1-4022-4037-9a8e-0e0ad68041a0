import * as React from 'react';
import {Text, TouchableOpacity, View} from 'react-native';
import {textStyles, viewStyles} from '../../styles';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {ReloadAppButton} from '../ReloadAppButton';
// import {ActivityIndicator} from 'react-native-paper';
// import {useSimCert} from '../../useQueryState/UseSimCert';
import {useAuthStore} from '@ac-mobile/common';
type Props = {
  selectedItem: any;
  onPress: () => void;
};

export const HeaderHome = ({selectedItem, onPress}: Props) => {
  const insets = useSafeAreaInsets();
  // const {isLoading} = useSimCert();
  const {user} = useAuthStore();

  return (
    <View
      className={`${viewStyles.viewRow} px-4 pb-4`}
      style={{paddingTop: insets?.top + 16}}>
      <View className="flex-1">
        <Text className={`${textStyles.text_20px_bold} text-white`}>
          {user?.hoTen ?? 'Khách'}
        </Text>

        <TouchableOpacity onPress={onPress}>
          <Text className={`${textStyles.text_14px_regular} text-white`}>
            {selectedItem?.tenDonVi ?? 'Khách'}
          </Text>
        </TouchableOpacity>
      </View>

      <View className="relative flex flex-row">
        {/* <IconButton
          icon={() => <BellIcon width={24} height={24} />}
          onPress={() => nav.navigate('NotificationScreen')}
          size={24}
        />
        <Badge size={20} className="absolute top-1 right-1 bg-[#FFAB00]">
          {3}
        </Badge> */}
        <ReloadAppButton />
        {/* {isLoading === true && <ActivityIndicator animating={true} />} */}
      </View>
    </View>
  );
};
