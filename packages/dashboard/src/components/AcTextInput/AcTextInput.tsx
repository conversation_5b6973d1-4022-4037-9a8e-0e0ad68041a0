import React, {useState} from 'react';
import {
  Keyboard,
  Pressable,
  StyleProp,
  StyleSheet,
  TextStyle,
  View,
} from 'react-native';
import {TextInput, TextInputProps} from 'react-native-paper';

interface AcTextInputProps extends Partial<TextInputProps> {
  value?: string;
  onChangeText?: (text: string) => void;
  label?: string;
  mode?: 'outlined' | 'flat';
  style?: StyleProp<TextStyle>;
  dismissKeyboardOnTouch?: boolean;
  enableFocusState?: boolean;
}

export const AcTextInput = ({
  value = '',
  onChangeText,
  label,
  mode = 'outlined',
  style,
  dismissKeyboardOnTouch = true,
  enableFocusState = false,
  ...rest
}: AcTextInputProps) => {
  const [isSearchFocused, setIsFocused] = useState(false);

  return (
    <View style={styles.container}>
      <TextInput
        label={label}
        mode={mode}
        value={value}
        onChangeText={onChangeText}
        style={[styles.input, style]}
        onFocus={e => {
          enableFocusState && setIsFocused(true);
          rest.onFocus?.(e);
        }}
        onBlur={e => {
          enableFocusState && setIsFocused(false);
          rest.onBlur?.(e);
        }}
        {...rest}
      />
      {isSearchFocused && dismissKeyboardOnTouch && enableFocusState && (
        <Pressable
          style={styles.overlay}
          onPress={() => {
            Keyboard.dismiss();
            setIsFocused(false);
          }}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    width: '100%',
  },
  input: {
    width: '100%',
    zIndex: 1,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 2,
  },
});
