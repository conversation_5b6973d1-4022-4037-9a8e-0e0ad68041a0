import {LayoutAnimation, StyleSheet, Text, View} from 'react-native';
import Modal from 'react-native-modal';
import React, {useRef, useState} from 'react';
import {CalendarComponentCus} from '../CalendarComponentCus/CalendarComponentCus';
import moment from 'moment';

type ModalCalendaComponentProp = {
  show: boolean;
  submit: (e: any) => void;
  cancel: () => void;
};
export const ModalCalendaComponent = ({
  cancel,
  show,
  submit,
}: ModalCalendaComponentProp) => {
  const [currentMonth, setCurrentMonth] = useState<string>(
    moment().format('YYYY-MM-DD'),
  );
  const incre = () => {
    setCurrentMonth(pev => {
      return moment(pev).add(1, 'month').format('YYYY-MM-DD');
    });
    LayoutAnimation.configureNext(LayoutAnimation.Presets.linear);
  };
  const decre = () => {
    setCurrentMonth(pev => {
      return moment(pev).add(-1, 'month').format('YYYY-MM-DD');
    });
    LayoutAnimation.configureNext(LayoutAnimation.Presets.linear);
  };

  const changeYear = (direction: number) => {
    setCurrentMonth(pev => {
      return moment(pev).year(direction).format('YYYY-MM-DD');
    });
  };
  const calendarRef = useRef<any>();
  const [type, setType] = useState<boolean>(true);
  return (
    <View>
      <Modal isVisible={show}>
        <View style={{maxHeight: '60%'}}>
          <CalendarComponentCus
            isShow={type}
            changeYear={e => {
              changeYear(e);
              setType(!type);
              LayoutAnimation.configureNext(
                LayoutAnimation.Presets.easeInEaseOut,
              );
            }}
            markedDate={{
              [currentMonth]: {
                selected: true,
                disableTouchEvent: true,
              },
            }}
            current={currentMonth}
            onDayPress={e => {
              setCurrentMonth(moment(e.dateString).format('YYYY-MM-DD'));
            }}
            changeType={() => {
              setType(!type);
              LayoutAnimation.configureNext(
                LayoutAnimation.Presets.easeInEaseOut,
              );
            }}
            changeMonthIncre={incre}
            changeMonthDecre={decre}
            cancel={cancel}
            submit={(e: any) => {
              submit(e);
            }}
          />
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({});
