import React, {forwardRef, useImperativeHandle, useState} from 'react';
import {View, Modal, StyleSheet} from 'react-native';
import ScrollPicker from 'react-native-wheel-scrollview-picker';
import {textInlineStyles, textStyles} from '../../styles';
import {ListRangeYearPicker} from '../../DashboardFn';
import {Button, Text} from 'react-native-paper';
import {
  ColorButtonContainedMain,
  ColorButtonTextPrimary,
} from '../CustomStyle/ColorStyleCustom';

export interface MonthYearPickerProps {
  onConfirm?: (month: number, year: number) => void;
}
export interface MonthYearPickerRef {
  open: (selectedMonth: number, selectedYear: number) => void;
  close: () => void;
}
export const MonthYearPicker = forwardRef<
  MonthYearPickerRef,
  MonthYearPickerProps
>(({onConfirm}: MonthYearPickerProps, ref) => {
  const [isModalVisible, setModalVisible] = useState(false);
  const [month, setSelectedMonth] = useState(new Date().getMonth() + 1);
  const [year, setSelectedYear] = useState(new Date().getFullYear());

  const months = Array.from({length: 12}, (_, i) => i + 1);
  const years = ListRangeYearPicker.map(item => item.value);

  const toggleModal = () => {
    setModalVisible(true);
  };
  const closeModal = () => {
    setModalVisible(false);
  };
  const confirmModal = () => {
    onConfirm && onConfirm(month, year);
    setModalVisible(false);
  };

  useImperativeHandle(
    ref,
    () => ({
      open: (selectedMonth, selectedYear) => {
        setSelectedMonth(selectedMonth);
        setSelectedYear(selectedYear);
        toggleModal();
      },
      close: () => {
        closeModal();
      },
    }),
    [],
  );

  return (
    <Modal
      visible={isModalVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={toggleModal}>
      <View className="flex-1 bg-bg-modal justify-center items-center">
        <View className="bg-white w-4/5 rounded-xl p-5">
          <Text className={`${textStyles.text_12px_bold} mb-4 text-[#637381]`}>
            CHỌN THÁNG, NĂM
          </Text>
          <View className="flex-row justify-center items-center w-full">
            {/* Picker Tháng */}
            <View className="w-9 h-[118px] mx-2">
              <ScrollPicker
                dataSource={months.map(m => (m < 10 ? `0${m}` : `${m}`))}
                selectedIndex={months.indexOf(month)}
                onValueChange={value =>
                  setSelectedMonth(parseInt(value as string))
                }
                itemHeight={36}
                style={styles.wheelPickerMonth}
                itemTextStyle={styles.wheelText}
                activeItemTextStyle={styles.wheelSelectedText}
                highlightColor="#212B36"
                wrapperBackground={'transparent'}
                highlightBorderWidth={1}
              />
            </View>

            {/* Picker Năm */}
            <View className="w-9 h-[118px] mx-2">
              <ScrollPicker
                dataSource={years.map(y => `${y}`)}
                selectedIndex={years.indexOf(year.toString())}
                itemHeight={36}
                onValueChange={value =>
                  setSelectedYear(parseInt(value as string))
                }
                style={styles.wheelPickerYear}
                itemTextStyle={styles.wheelText}
                activeItemTextStyle={styles.wheelSelectedText}
                highlightColor="#212B36"
                highlightBorderWidth={1}
                wrapperBackground={'transparent'}
              />
            </View>
          </View>

          {/* Nút hành động */}
          <View className="flex-row justify-end mt-4 w-full">
            {/* COMMENT: Old code */}
            {/* <TouchableOpacity
              className="py-1.5 px-3 rounded-lg ml-3"
              onPress={closeModal}>
              <Text className={textStyles.text_14px_bold}>Hủy</Text>
            </TouchableOpacity>
            <TouchableOpacity
              className="py-1.5 px-3 rounded-lg bg-[#366AE2]"
              onPress={() => confirmModal()}>
              <Text className={`${textStyles.text_14px_bold} text-white`}>
                Chọn
              </Text>
            </TouchableOpacity> */}
            <Button
              mode="text"
              style={{borderRadius: 8}}
              onPress={closeModal}
              textColor={ColorButtonTextPrimary}>
              Huỷ
            </Button>
            <Button
              mode="contained"
              style={{borderRadius: 8}}
              onPress={() => confirmModal()}
              buttonColor={ColorButtonContainedMain}>
              Chọn
            </Button>
          </View>
        </View>
      </View>
    </Modal>
  );
});

const styles = StyleSheet.create({
  wheelPickerMonth: {
    width: 36,
    height: 118,
  },
  wheelPickerYear: {
    width: 48,
    height: 118,
  },
  wheelText: {
    ...textInlineStyles.text_14px_bold,
    fontWeight: '400',
    color: '#919EAB',
  },
  wheelSelectedText: {
    ...textInlineStyles.text_14px_bold,
    fontWeight: '600',
  },
});
