import * as React from 'react';
import {Text, View} from 'react-native';
import {textStyles, viewStyles} from '../../styles';
import {CheckSuccess} from '../../icons';

interface CardSnackBarProps {
  containerStyle?: string;
  message?: string;
}

export const CardSnackBar = ({containerStyle, message}: CardSnackBarProps) => {
  if (!message) {
    return null;
  }
  return (
    <View
      className={`${containerStyle || ''} ${
        viewStyles.viewRow
      } border border-[#22C55E29] bg-[#22C55E14] py-[13px] px-4 rounded-lg`}>
      <CheckSuccess width={24} height={24} />
      <Text className={`${textStyles.text_14px_regular} text-[#118D57] mx-3`}>
        {message}
      </Text>
    </View>
  );
};
