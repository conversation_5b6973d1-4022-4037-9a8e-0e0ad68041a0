import * as React from 'react';
import {Pressable, Text, View} from 'react-native';
import {NavigationProp, useNavigation} from '@react-navigation/native';
import {MainStackParamList} from '../../navigation/MainNavigator';
import {textStyles} from '../../styles';
import {DigitalSignatureButton} from '../ButtonComponent';
import {
  AttachFileGrayIcon,
  Delete,
  LineSvg,
  PdfIcon,
  WriteIcon,
} from '../../icons';
import {handleFunction} from '../../api/dashboard-api';
import {useNoiDungGiayTo} from '../../stores';

interface BoxDocumentProps {
  noiDungGiayTo?: {
    totalRecords: number;
    tapTinDinhKemID: number;
    donViID: string;
    quaTrinhXuLyID: string;
    nguoiXuLyHoSoID: string;
    originalName: string;
    uploadName: string;
    pathName: string;
    fileSize: string;
    userID: string;
    sealed: string;
    sealedDate: string;
    signerID: string;
    hoSoID: string;
    createdUserID: string;
    createdDate: string;
    lastUpdUserID: string;
    lastUpdDate: string;
    totalDVC: number;
  };
  styleLabel?: string;
  signStatus?: boolean;
  showLabel?: boolean;
  isPheDuyet?: boolean;
  fileLocal?: {
    fileCopyUri?: string | null;
    name?: string | null;
    size?: number | null;
    type?: string | null;
    uri?: string | null;
    copyError?: string | null;
  };
  isLocal?: boolean;
}

export const BoxDocumentOld = React.memo(
  ({
    styleLabel,
    signStatus = false,
    showLabel = false,
    noiDungGiayTo,
    isPheDuyet,
    isLocal = false,
    fileLocal,
  }: BoxDocumentProps) => {
    const nav = useNavigation<NavigationProp<MainStackParamList>>();
    const {DeleteHoSoDinhKem, refreshDsHoSo, deleteLocalFile} =
      useNoiDungGiayTo();
    const onSignature = () => nav.navigate('DigitalSignature');
    return isLocal ? (
      <>
        {showLabel && (
          <Pressable
            className='className="py-4 border border-gray-light mb-2 items-center jutify-center px-2 flex-row rounded-lg bg-white mt-2 "'
            onPress={async () => {
              console.log(fileLocal);
              nav.navigate('PdfViewer', {
                url: fileLocal?.uri as string,
                title: fileLocal?.name as string,
              });
              // const rs = await handleFunction.DownloadFile({
              //   fileName: noiDungGiayTo?.uploadName,
              // });
              // if (rs?.data) {
              //   nav.navigate('PdfViewer', {
              //     url: `data:application/pdf;base64,${rs?.data?.data}`,
              //   });
              // }
            }}>
            <AttachFileGrayIcon width={24} height={24} />
            <Text
              className={`${textStyles.text_14px_regular} text-[#366AE2] mx-2 flex-1`}>
              {fileLocal?.name}
            </Text>
            <Pressable>
              <Delete
                width={40}
                height={40}
                onPress={() => {
                  console.log('remo');
                  fileLocal?.uri && deleteLocalFile(fileLocal?.uri);
                }}
              />
            </Pressable>
          </Pressable>
        )}
      </>
    ) : (
      noiDungGiayTo && (
        <>
          {signStatus ? (
            <>
              <View className="py-4 px-2 rounded-lg bg-[#F4F6F8]">
                <View className="flex-row pb-3">
                  <PdfIcon width={24} height={24} />
                  <Text
                    className={`${
                      textStyles.text_14px_regular
                    } text-[#366AE2] mx-2 flex-1 ${styleLabel || ''}`}>
                    {/* {noiDungGiayTo?.originalName}
                     */}
                    Tờ trình UBND sửa quyết định 18.pdf
                  </Text>
                  {signStatus ? (
                    <DigitalSignatureButton onPress={onSignature} />
                  ) : null}
                </View>
                <LineSvg width={'100%'} height={1} />
                <View className="mt-3 flex-row items-start">
                  <WriteIcon width={16} height={24} />
                  <Text
                    className={`${textStyles.text_14px_regular} mx-2 text-[#637381]`}>
                    {
                      'Ký bởi: Trịnh Tất Thắng - UỶ BAN NHÂN DÂN QUẬN HOÀN KIẾM - ỦY BAN NHÂN DÂN THÀNH PHỐ HÀ NỘI\n • Ngày ký: 25/11/2024 14:34:04\n • Chữ ký số hợp lệ\n • Chứng thư số còn hiệu lực'
                    }
                  </Text>
                </View>
              </View>
            </>
          ) : (
            <>
              {showLabel && (
                <Pressable
                  className='className="py-4 border border-gray-light mb-2 items-center jutify-center px-2 flex-row rounded-lg bg-white mt-2 "'
                  onPress={async () => {
                    const rs = await handleFunction.DownloadFile({
                      fileName: noiDungGiayTo?.uploadName,
                    });
                    if (rs?.data) {
                      nav.navigate('PdfViewer', {
                        url: `data:application/pdf;base64,${rs?.data?.data}`,
                      });
                    }
                  }}>
                  <AttachFileGrayIcon width={24} height={24} />
                  <Text
                    className={`${textStyles.text_14px_regular} text-[#366AE2] mx-2 flex-1`}>
                    {noiDungGiayTo?.uploadName}
                  </Text>
                  <Pressable>
                    <Delete
                      width={40}
                      height={40}
                      onPress={() => {
                        DeleteHoSoDinhKem({
                          fileName: noiDungGiayTo.uploadName,
                          tapTinDinhKemID: noiDungGiayTo.tapTinDinhKemID,
                        });
                        refreshDsHoSo();
                      }}
                    />
                  </Pressable>
                </Pressable>
              )}
              {isPheDuyet && (
                <View className="py-4 px-2 rounded-lg bg-[#F4F6F8]">
                  <View className="flex-row pb-3">
                    <PdfIcon width={24} height={24} />
                    <Text
                      className={`${
                        textStyles.text_14px_regular
                      } text-[#366AE2] mx-2 flex-1 ${styleLabel || ''}`}>
                      {noiDungGiayTo?.originalName}
                    </Text>
                  </View>
                  <LineSvg width={'100%'} height={1} />
                  <View className="mt-3 flex-row items-start">
                    <WriteIcon width={16} height={24} />
                    <Text
                      className={`${textStyles.text_14px_regular} mx-2 text-[#637381]`}>
                      {`Ký bởi: ${noiDungGiayTo?.nguoiXuLyHoSoID}\n • Ngày ký: 25/11/2024 14:34:04\n • Chữ ký số hợp lệ\n • Chứng thư số còn hiệu lực`}
                    </Text>
                  </View>
                </View>
              )}
            </>
          )}
        </>
      )
    );
  },
);
