import React, {ReactNode, useMemo, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  LayoutAnimation,
  UIManager,
  Platform,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {viewStyles} from '../../styles';
import {
  ContentCollapsible,
  ContentCollapsibleType,
  DataContentCollapsibleType,
} from './ContentCollapsible';
import {LineSvg} from '../../icons';
import {useTheme} from 'react-native-paper';

if (
  Platform.OS === 'android' &&
  UIManager.setLayoutAnimationEnabledExperimental
) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}
export interface BoxCollapsibleProps {
  title?: string;
  subTitle?: string;
  data?: DataContentCollapsibleType;
  type?: ContentCollapsibleType;
  renderItem?: ReactNode;
  containerStyle?: string;
  emptyComponent?: ReactNode;
}
export const BoxCollapsible = ({
  title = 'Title',
  subTitle,
  data,
  containerStyle,
  emptyComponent = null,
  renderItem,
  type,
}: BoxCollapsibleProps) => {
  const [expanded, setExpanded] = useState(true);
  const theme = useTheme();
  const toggleExpand = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setExpanded(!expanded);
  };
  const elementChildren = useMemo(() => {
    if (!renderItem && !data?.length) {
      return emptyComponent;
    }
    if (data?.length && type) {
      return <ContentCollapsible type={type} data={data} />;
    } else {
      return renderItem;
    }
  }, [renderItem, data, emptyComponent, type]);
  return (
    <View
      style={{backgroundColor: theme.colors.background}}
      className={`rounded-lg p-3 ${viewStyles.shadow} ${containerStyle || ''}`}>
      <TouchableOpacity
        activeOpacity={0.7}
        className="flex-row justify-between items-center py-3 "
        onPress={toggleExpand}>
        <View className="flex-1">
          <Text
            style={[
              {color: theme.colors.onBackground},
              {fontWeight: 600, fontSize: 17},
            ]}>
            {title || '_'}
          </Text>
          {subTitle ? (
            <View className="mt-[4px]">
              <Text style={{color: theme.colors.outline}}>
                {subTitle || '_'}
              </Text>
            </View>
          ) : null}
        </View>
        <Icon
          name={expanded ? 'keyboard-arrow-up' : 'keyboard-arrow-down'}
          size={24}
          color={theme.colors.outline}
        />
      </TouchableOpacity>
      {expanded && <LineSvg height={1} width={'100%'} />}
      {expanded ? elementChildren : null}
    </View>
  );
};
