import React, {memo} from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {textStyles, viewStyles} from '../../styles';
export interface TimeLineItemProps {
  isActive: boolean;
  title: string;
  person?: string;
  date?: string;
  status?: string;
  isHideLineVertical?: boolean;

  buocXuLy?: string;
  chuyenVienXL?: string;
  daNhan?: string;
  dungHen?: string;
  hoiQuy?: boolean;
  ngayNhan?: string;
  ngayXuLy?: string;
  nguyenDoTre?: string;
  noiDungXuLy?: string;
  parentID?: number;
  quaTrinhHienTai?: false;
  quaTrinhXuLyID?: number;
  soNgayGiaiQuyet?: string;
  stt?: number;
  tenDonViNhan?: string;
  tenLoaiXuLy?: string;
  tongNguoiXL?: number;
  treHen?: string;
  truocHen?: string;
}
export const TimeLineItem = memo(
  ({
    isActive,
    title,
    person,
    date,
    status,
    isHideLineVertical = false,
    buoc<PERSON>u<PERSON>y,
    chuyen<PERSON>ien<PERSON><PERSON>,
    da<PERSON><PERSON>,
    dung<PERSON><PERSON>,
    hoi<PERSON>uy,
    ngay<PERSON><PERSON>,
    ngayXuLy,
    nguyenDo<PERSON>re,
    noiDungXuLy,
    parentID,
    quaTrinhHienTai,
    quaTrinhXuLyID,
    soNgayGiaiQuyet,
    stt,
    tenDonViNhan,
    tenLoaiXuLy,
    tongNguoiXL,
    treHen,
    truocHen,
  }: TimeLineItemProps) => {
    return (
      <View>
        <View className={viewStyles.viewRow}>
          <View
            className={`w-3 h-3 rounded-full ${
              quaTrinhHienTai ? 'bg-[#366AE2]' : 'bg-gray-light'
            }`}
          />
          <Text className={`${textStyles.text_14px_medium} ml-4`}>
            {buocXuLy}
          </Text>
        </View>
        <View className="flex-row">
          {!isHideLineVertical && (
            <View className="w-px bg-[#e5e7eb] mx-[6px] mt-2" />
          )}
          <View className={`ml-4 ${isHideLineVertical ? 'ml-8' : ''}`}>
            <Text
              className={`${textStyles.text_12px_regular} text-[#919EAB] leading-[18px] `}>
              {chuyenVienXL}
            </Text>
            {ngayXuLy ? (
              <Text
                className={`${textStyles.text_12px_regular} text-[#919EAB] leading-[18px] `}>
                Ngày xử lý: {ngayXuLy}
              </Text>
            ) : null}
            {tenLoaiXuLy ? (
              <Text
                className={`${textStyles.text_12px_regular} text-[#919EAB] leading-[18px] `}>
                Trạng thái: {tenLoaiXuLy}
              </Text>
            ) : null}
          </View>
        </View>
      </View>
    );
  },
);
