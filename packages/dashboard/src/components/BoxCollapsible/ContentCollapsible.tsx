import * as React from 'react';
import {Text, View} from 'react-native';
import {TextRow} from './TextRow';
import {TimeLineItem, TimeLineItemProps} from './TimeLineItem';
// import {useNavigation} from '@react-navigation/native';
import {BoxDocument} from './BoxDocument';
import {Divider, useTheme} from 'react-native-paper';

export interface ContentCollapsibleItem {
  label: string;
  content: string;
}
export enum ContentCollapsibleType {
  TEXT_ROW = 'TEXT_ROW',
  PROCESS_BAR = 'PROCESS_BAR',
  DOCUMENT = 'DOCUMENT',
}
interface ContentCollapsibleProps {
  data: DataContentCollapsibleType;
  type: ContentCollapsibleType;
}
export type DataContentCollapsibleType =
  | Array<ContentCollapsibleItem>
  | Array<TimeLineItemProps>
  | Array<any>
  | Array<any>;
export type ItemContentCollapsibleType =
  | ContentCollapsibleItem
  | TimeLineItemProps
  | any
  | Array<any>;
export const ContentCollapsible = ({data, type}: ContentCollapsibleProps) => {
  const theme = useTheme();

  const renderElementChildren = (
    item: ContentCollapsibleItem | TimeLineItemProps | any,
    index: number,
  ) => {
    let ele = null;
    switch (type) {
      case ContentCollapsibleType.TEXT_ROW:
        ele = (
          <View key={index}>
            <TextRow item={item as ContentCollapsibleItem} />
          </View>
        );
        break;
      case ContentCollapsibleType.PROCESS_BAR:
        ele = (
          <View key={index}>
            <TimeLineItem
              {...(item as TimeLineItemProps)}
              isHideLineVertical={index === data?.length - 1}
            />
          </View>
        );
        break;
      case ContentCollapsibleType.DOCUMENT:
        ele = (
          <View key={index}>
            <Text style={{color: theme.colors.onSurface}} className="mb-2">
              {item.tenHoSoKemTheo}
            </Text>
            {item.fileName && (
              <BoxDocument
                noiDungGiayTo={{
                  uploadName: item.fileName,
                  originalName: item.fileName,
                }}
              />
            )}
            {!item.fileName && (
              <Text style={{color: theme.colors.error}}>Không có tài liệu</Text>
            )}
            <Divider className="mt-3" />
          </View>
        );
        break;
    }
    return ele;
  };
  return (
    <View className="gap-3 pt-3">
      {data?.map((item, index) => renderElementChildren(item, index))}
    </View>
  );
};
