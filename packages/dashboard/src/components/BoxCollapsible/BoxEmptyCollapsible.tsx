import * as React from 'react';
import {Text, View} from 'react-native';
import {textStyles} from '../../styles';
import {EmptyCollapsible} from '../../icons/EmptyCollapsible';

interface BoxEmptyCollapsibleProps {
  title?: string;
}

export const BoxEmptyCollapsible = ({title}: BoxEmptyCollapsibleProps) => {
  return (
    <View className="py-6 items-center">
      <EmptyCollapsible />
      {title ? (
        <Text className={`${textStyles.text_16px_medium} mt-6 text-[#919EAB]`}>
          {title}
        </Text>
      ) : null}
    </View>
  );
};
