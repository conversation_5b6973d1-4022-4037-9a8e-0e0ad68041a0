import React from 'react';
import {View, Text} from 'react-native';
import {textStyles} from '../../styles';

export interface ExpandableTextProps {
  label?: string;
  content: string;
}
export const ExpandableText = ({label, content}: ExpandableTextProps) => {
  return (
    <View>
      {label ? (
        <Text
          className={`${textStyles.text_14px_medium} text-[#637381] ml-2 mb-[6px]`}>
          {label}
        </Text>
      ) : null}
      <View className="py-4 px-[14px] bg-[#919EAB14] border border-[#919EAB33] rounded-lg">
        <Text
          className={textStyles.text_14px_regular}
          numberOfLines={3}
          ellipsizeMode="tail">
          {content}
        </Text>
      </View>
    </View>
  );
};

export default ExpandableText;
