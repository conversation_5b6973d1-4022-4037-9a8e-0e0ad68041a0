import * as React from 'react';
import {Text, View} from 'react-native';
import {textStyles} from '../../styles';
import {ContentCollapsibleItem} from './ContentCollapsible';

interface TextRowProps {
  item: ContentCollapsibleItem;
}

export const TextRow = ({item}: TextRowProps) => {
  return (
    <View className="flex-row">
      <View className="flex-1 items-start">
        <Text className={`${textStyles.text_14px_regular} text-[#637381]`}>
          {item?.label}
        </Text>
      </View>

      <View className="flex-1 items-end">
        <Text className={`${textStyles.text_14px_regular} text-right`}>
          {item?.content}
        </Text>
      </View>
    </View>
  );
};
