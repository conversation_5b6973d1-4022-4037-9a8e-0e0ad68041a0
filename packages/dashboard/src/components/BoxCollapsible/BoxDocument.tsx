import * as React from 'react';
import {Pressable, Text, View} from 'react-native';
import {NavigationProp, useNavigation} from '@react-navigation/native';
import {MainStackParamList} from '../../navigation/MainNavigator';
import {textStyles} from '../../styles';
import {LineSvg, PdfIcon, SimCardIcon, WriteIcon} from '../../icons';
import {Button, useTheme} from 'react-native-paper';
import {API_CONFIG} from '../../api/api-config';
import {useAuthStore} from '@ac-mobile/common';
import Toast from 'react-native-toast-message';
import {showESign} from '../../configs/common';

export type noiDungGiayTo = {
  listNguoiKy?: [
    {
      chuKy: string;
      chungThu: string;
      ngayKy: string;
      nguoiKy: string;
      phatHanh: string;
    },
  ];
  totalRecords: number;
  tapTinDinhKemID: number;
  donViID: string;
  quaTrinhXuLyID: string;
  nguoiXuLyHoSoID: string;
  originalName: string;
  uploadName: string;
  pathName: string;
  fileSize: string;
  userID: string;
  sealed: string;
  sealedDate: string;
  signerID: string;
  hoSoID: string;
  createdUserID: string;
  createdDate: string;
  lastUpdUserID: string;
  lastUpdDate: string;
  totalDVC: number;
};
interface BoxDocumentProps {
  noiDungGiayTo?: {
    originalName: string;
    uploadName: string;
    listNguoiKy?: [
      {
        chuKy: string;
        chungThu: string;
        ngayKy: string;
        nguoiKy: string;
        phatHanh: string;
      },
    ];
  };
  styleLabel?: string;
  signStatus?: boolean;
  // showLabel?: boolean;
  isPheDuyet?: boolean;
  fileLocal?: {
    fileCopyUri?: string | null;
    name?: string | null;
    size?: number | null;
    type?: string | null;
    uri?: string | null;
    copyError?: string | null;
  };
  // isLocal?: boolean;
  showDelete?: boolean;
  deleteFunction?: () => void;
  chiTietHoSo?: any;
}

export const BoxDocument = React.memo(
  ({
    styleLabel,
    signStatus = false,
    noiDungGiayTo,
    chiTietHoSo,
    deleteFunction,
    showDelete,
  }: BoxDocumentProps) => {
    const user = useAuthStore(state => state.user);
    const nav = useNavigation<NavigationProp<MainStackParamList>>();
    const theme = useTheme();

    const goToDetail = () => {
      if (noiDungGiayTo !== undefined) {
        if (!noiDungGiayTo.uploadName.toLowerCase().endsWith('.pdf')) {
          Toast.show({
            type: 'error',
            text1: 'Không hỗ trợ định dạng này',
          });
          return;
        }

        const paramsPdfViewer: MainStackParamList['PdfViewer'] = {
          url: `${API_CONFIG.GETFILEURL}/api/file/download?filename=${noiDungGiayTo.uploadName}`,
          title: noiDungGiayTo.originalName ?? '',
          uploadParams: {
            DonViXuLyID: user?.donViID,
            QuaTrinhXuLyID: chiTietHoSo?.quaTrinhXulyHoSo?.find(
              (_: {quaTrinhHienTai: boolean}) => _.quaTrinhHienTai == true,
            )?.quaTrinhXuLyID,
            NguoiXuLyID: user?.user_MasterID,
            NguoiXuLyHoSoHienTaiID:
              chiTietHoSo?.thongTinThuLyHoSo?.nguoiXuLyHoSoHienTaiID,
            HoSoID: chiTietHoSo?.thongTinThuLyHoSo?.hoSoID,
          },
          deleteFunction,
          signStatus,
        };
        nav.navigate('PdfViewer', paramsPdfViewer);
      }
    };

    if (!noiDungGiayTo) {
      return null;
    } else {
      return (
        <View
          className="rounded-lg p-3"
          style={{backgroundColor: theme.colors.surfaceVariant}}>
          <Pressable onPress={goToDetail}>
            <View className="flex-row ">
              <PdfIcon width={24} height={24} />
              <Text
                className={`${textStyles.text_14px_regular} mx-2 flex-1 ${
                  styleLabel || ''
                }`}
                style={{color: theme.colors.primary}}>
                {noiDungGiayTo?.originalName}
              </Text>
            </View>
          </Pressable>
          {noiDungGiayTo.listNguoiKy?.length &&
          noiDungGiayTo.listNguoiKy?.length > 0 ? (
            <LineSvg width={'100%'} height={1} />
          ) : (
            <></>
          )}
          <View className="flex-column items-start mt-3">
            {noiDungGiayTo.listNguoiKy?.map((e, i) => {
              return (
                <View className="flex-row" key={i}>
                  <WriteIcon width={16} height={24} />
                  <Text
                    className={`${textStyles.text_14px_regular} mx-2 `}
                    style={{color: theme.colors.onSurfaceVariant}}>
                    {`Ký bởi: ${e.nguoiKy}\n • Ngày ký: ${e.ngayKy}\n • ${e.chuKy}\n • ${e.chungThu}`}
                  </Text>
                </View>
              );
            })}
          </View>
          {showDelete || signStatus ? (
            <View className="flex-row justify-between mt-3">
              {deleteFunction !== undefined && showDelete && (
                <Button
                  mode="outlined"
                  contentStyle={{height: 40}}
                  onPress={deleteFunction}>
                  Xóa
                </Button>
              )}
              {signStatus && showESign && (
                <Button
                  mode="contained"
                  contentStyle={{height: 40}}
                  icon={() => <SimCardIcon width={18} height={18} />}
                  onPress={goToDetail}>
                  Ký SIM
                </Button>
              )}
            </View>
          ) : (
            <></>
          )}
        </View>
      );
    }
  },
);
