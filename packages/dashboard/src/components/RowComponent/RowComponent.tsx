import React, {memo, PropsWithChildren} from 'react';
import {StyleProp, StyleSheet, View, ViewStyle} from 'react-native';
import {ViewProps} from 'react-native-svg/lib/typescript/fabric/utils';
type BoxProps = PropsWithChildren<
  {
    style?: StyleProp<ViewStyle>;
  } & Pick<ViewProps, 'onLayout'>
>;

export const RowComponent: React.FC<BoxProps> = memo(({children}) => {
  return <View style={styles.view}>{children}</View>;
});

export default RowComponent;

const styles = StyleSheet.create({view: {marginTop: 6, gap: 10}});
