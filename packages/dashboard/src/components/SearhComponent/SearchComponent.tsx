import {TextInput, View} from 'react-native';
import React from 'react';
import {SearchIcon} from '../../icons/SearchIcon';

type SearchProp = {
  search: string;
  setSearch: (e: string) => void;
};
export const SearchComponent = ({search, setSearch}: SearchProp) => {
  return (
    <View className="flex flex-row items-center px-3 py-2 border border-gray-300 rounded-lg w-full relative max-h-11">
      <SearchIcon width={40} height={40} />
      <TextInput
        className="p-1 w-[90%] h-[25px]"
        onChangeText={e => {
          setSearch(e);
        }}
        value={search}
      />
    </View>
  );
};
