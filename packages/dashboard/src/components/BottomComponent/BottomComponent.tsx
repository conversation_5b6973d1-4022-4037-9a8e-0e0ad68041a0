import {
  ScrollView,
  StyleProp,
  StyleSheet,
  Text,
  View,
  ViewStyle,
} from 'react-native';
import React, {forwardRef, PropsWithChildren} from 'react';
import {ForwardRefComponent} from 'react-native-paper/lib/typescript/utils/forwardRef';
import RBSheet from 'react-native-raw-bottom-sheet';
export type BottomSheetProp = {
  open: () => void;
  close: () => void;
};

type BottomSheetModalAppProps = {
  snapPoints: number;
  onChange?: (index: number) => void;
};
export const BottomComponent: ForwardRefComponent<
  BottomSheetProp,
  PropsWithChildren<BottomSheetModalAppProps>
> = forwardRef(({children, onChange, snapPoints, key}, ref) => {
  return (
    <RBSheet ref={ref} height={snapPoints}>
      <View className="flex-1">
        {/* <ScrollView
          contentContainerStyle={{flexGrow: 1}}
          keyboardDismissMode="on-drag"
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
          children={children}
        /> */}
        {children}
      </View>
    </RBSheet>
  );
});

const styles = StyleSheet.create({});
