import React, {memo} from 'react';
import {
  Pressable,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Modal from 'react-native-modal';
import {CloseIcon} from '../../icons';
import {textStyles} from '../../styles';

type ModalComponentProp = {
  title?: string;
  titleChild?: string;
  pressTextRight?: string;
  pressTextLeft?: string;
  Icons?: React.ReactNode;
  isShow?: boolean;
  type: 'exit' | 'confirm';
  close?: () => void;
  onPressLeft: () => void;
  onPressRight: () => void;
};
export const ModalComponent = memo(
  ({
    isShow,
    onPressLeft,
    pressTextRight,
    onPressRight,
    close,
    Icons,
    title,
    titleChild,
    type = 'exit',
    pressTextLeft,
  }: ModalComponentProp) => {
    const colorIcon = type == 'exit' ? 'bg-red-orange-warm' : 'bg-blue-weak';
    const colorbtn = type == 'exit' ? 'bg-red-orange' : 'bg-blue-strong';
    return (
      <View>
        <Modal isVisible={isShow}>
          <View className="bg-white rounded-[8px] justify-around p-4 gap-y-3.5 relative h-[30%]">
            <View className="flex-row justify-end">
              <TouchableOpacity onPress={close}>
                <CloseIcon width={20} height={20} />
              </TouchableOpacity>
            </View>
            <View className="justify-center items-center">
              <View
                className={`w-16 h-16 rounded-full justify-center items-center ${colorIcon}`}>
                {Icons && Icons}
              </View>
            </View>
            <View
              style={[
                styles.row,
                {
                  justifyContent: 'center',
                },
              ]}>
              <View style={{alignItems: 'center', rowGap: 10}}>
                <Text style={{fontWeight: 700, fontSize: 20}}>{title}</Text>
                <Text style={{textAlign: 'center'}}>{titleChild}</Text>
              </View>
            </View>
            <View
              style={[
                styles.row,
                {
                  justifyContent: 'space-around',
                },
              ]}>
              <Pressable
                className={`w-[45%] p-4 rounded-lg justify-center items-center bg-gray-light`}
                onPress={onPressLeft}>
                <View>
                  <Text className={`${textStyles.text_15px_bold} `}>
                    {pressTextLeft}
                  </Text>
                </View>
              </Pressable>
              <Pressable
                className={`w-[45%] p-4 rounded-lg justify-center items-center  ${colorbtn}`}
                onPress={onPressRight}>
                <View>
                  <Text className={`${textStyles.text_15px_bold} color-white`}>
                    {pressTextRight}
                  </Text>
                </View>
              </Pressable>
            </View>
          </View>
        </Modal>
      </View>
    );
  },
);

const styles = StyleSheet.create({
  // button: ,
  row: {
    flexDirection: 'row',
    padding: 8,
  },
});
