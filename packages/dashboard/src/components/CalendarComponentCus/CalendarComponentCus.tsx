import moment from 'moment';
import React, {memo, useCallback} from 'react';
import {
  FlatList,
  Pressable,
  StyleProp,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import {Calendar, LocaleConfig} from 'react-native-calendars';
import {DateData, MarkedDates} from 'react-native-calendars/src/types';
import {
  ArrowDownIcon,
  ArrowLeftIcon,
  ArrowRight,
  ArrowUpIcon,
} from '../../icons';
import {textStyles} from '../../styles';
type CalenderAppProps = {
  onDayPress?: (date: DateData) => void;
  markedDate?: MarkedDates;
  minDate?: string;
  maxDate?: string;
  styleContainer?: StyleProp<ViewStyle>;
  style?: StyleProp<ViewStyle>;
  current?: string;
  type?: 'default';
  isShow: boolean;
  changeMonthIncre: () => void;
  changeMonthDecre: () => void;
  changeYear: (e: any) => void;
  submit: (e: any) => void;
  cancel?: () => void;
  changeType?: () => void;
};
LocaleConfig.locales.vi_VN = {
  monthNames: [
    'Tháng 1',
    'Tháng 2',
    'Tháng 3',
    'Tháng 4',
    'Tháng 5',
    'Tháng 6',
    'Tháng 7',
    'Tháng 8',
    'Tháng 9',
    'Tháng 10',
    'Tháng 11',
    'Tháng 12',
  ],
  monthNamesShort: [
    'Janv.',
    'Févr.',
    'Mars',
    'Avril',
    'Mai',
    'Juin',
    'Juil.',
    'Août',
    'Sept.',
    'Oct.',
    'Nov.',
    'Déc.',
  ],
  dayNames: ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'],
  dayNamesShort: ['S', 'M', 'T', 'W', 'T', 'F', 'S'],
};

type ModalRef = {
  open: () => void;
  close: () => void;
};

export const CalendarComponentCus: React.FC<CalenderAppProps> = memo(
  ({
    current,
    maxDate,
    minDate,
    onDayPress,
    changeMonthDecre,
    changeMonthIncre,
    changeYear,
    submit,
    cancel,
    changeType,
    isShow,
  }) => {
    const daysInEnglishToVietnamese: any = {
      ['Sunday']: 'Chủ nhật',
      ['Monday']: 'Thứ Hai',
      ['Tuesday']: 'Thứ Ba',
      ['Wednesday']: 'Thứ Tư',
      ['Thursday']: 'Thứ Năm',
      ['Friday']: 'Thứ Sáu',
      ['Saturday']: 'Thứ Bảy',
    };

    const renderHeader = useCallback(() => {
      return (
        <View className="pl-2 justify-between bg-white">
          <View className="gap-y-2.5">
            <Text
              className={`${textStyles.text_14px_regular} text-[#637381] font-bold text-xs`}>
              Chọn ngày
            </Text>
            <Text className={`${textStyles.text_20px_bold}`}>
              {moment(current, 'YYYY/MM/DD').format('dddd').toString() &&
                daysInEnglishToVietnamese[
                  `${
                    moment(current, 'YYYY/MM/DD')
                      .format('dddd')
                      .toString() as string
                  }`
                ]}
              ,{' '}
              {Number.parseInt(
                moment(current, 'YYYY/MM/DD').format('DD').toString() as string,
              )}{' '}
              Tháng{' '}
              {Number.parseInt(
                moment(current, 'YYYY/MM/DD').format('MM').toString() as string,
              )}
            </Text>
          </View>
          <View className="flex-row justify-between items-center">
            <View className="flex-row items-center space-x-1.5 mt-2">
              <Text className={`${textStyles.text_16px_regular} font-bold`}>
                Tháng {moment(current).format('MM, YYYY')}
              </Text>
              <Pressable onPress={changeType}>
                {isShow ? (
                  <ArrowDownIcon width={22} height={22} />
                ) : (
                  <ArrowUpIcon width={22} height={22} />
                )}
              </Pressable>
            </View>
            {isShow && (
              <View className="flex-row justify-between w-[40%] bg-red">
                <TouchableOpacity onPress={changeMonthDecre}>
                  <ArrowLeftIcon width={40} height={40} />
                </TouchableOpacity>
                <TouchableOpacity onPress={changeMonthIncre}>
                  <ArrowRight width={40} height={40} />
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      );
    }, [
      current,
      daysInEnglishToVietnamese,
      changeType,
      isShow,
      changeMonthDecre,
      changeMonthIncre,
    ]);
    const renderBody = useCallback(() => {
      return isShow ? (
        <View className="px-4">
          <Calendar
            onDayPress={onDayPress}
            current={current}
            key={current}
            hideArrows={true}
            markingType={'custom'}
            renderHeader={() => <></>}
            markedDates={{
              [moment().format('YYYY-MM-DD')]: {
                customStyles: {
                  container: {
                    backgroundColor: '#ffffff',
                    borderWidth: 1,
                  },
                  text: {
                    color: 'black',
                    fontWeight: 'bold',
                  },
                },
              },
            }}
            theme={{
              backgroundColor: '#ffffff',
              calendarBackground: '#ffffff',
              textSectionTitleColor: '#b6c1cd',
              textSectionTitleDisabledColor: '#d9e1e8',
              selectedDayBackgroundColor: 'rgba(54, 106, 226, 1)',
              selectedDayTextColor: '#ffffff',
              todayTextColor: '#00adf5',
              textDayFontWeight: '500',
              textMonthFontWeight: 'bold',
              textDayFontSize: 16,
              textMonthFontSize: 16,
              todayBackgroundColor: '#fff',
            }}
            minDate={minDate}
            maxDate={maxDate}
          />
          <View className="flex-row justify-end p-4 gap-4">
            <Pressable onPress={cancel}>
              <View className="flex justify-center items-center py-2 px-3.5 bg-white">
                <Text className={`${textStyles.text_14px_bold}`}>Huỷ</Text>
              </View>
            </Pressable>
            <Pressable
              onPress={() => {
                submit(current);
              }}>
              <View className="justify-center items-center py-2 px-3.5 bg-black rounded-lg">
                <Text className={`${textStyles.text_14px_bold} text-white`}>
                  OK
                </Text>
              </View>
            </Pressable>
          </View>
        </View>
      ) : (
        <View className="max-h-[75%] justify-center items-center">
          <FlatList
            data={Array.from(
              {length: 2100 - 2016 + 1},
              (_, index) => 2016 + index,
            )}
            showsVerticalScrollIndicator
            keyExtractor={(item, index) => index.toString()}
            numColumns={4}
            renderItem={({item}) => (
              <Pressable
                onPress={e => {
                  changeYear(item);
                }}>
                <View className="p-6">
                  <View
                    className={`${
                      moment(current).format('YYYY') === item.toString()
                        ? 'bg-gray-light px-2 rounded-[10px]'
                        : ''
                    }`}>
                    <Text className={textStyles.text_14px_bold}>{item}</Text>
                  </View>
                </View>
              </Pressable>
            )}
          />
        </View>
      );
    }, [isShow, current]);
    LocaleConfig.defaultLocale = 'vi_VN';
    console.log(moment(current).format('YYYY'));

    return (
      <View className="p-4 bg-white rounded-xl">
        {renderHeader()}
        {renderBody()}
      </View>
    );
  },
);
