import {StyleSheet} from 'react-native';
import React, {memo} from 'react';
import {List} from 'react-native-paper';

type ProfileCardInfoProp = {
  label?: string;
  title?: string;
  Icon?: React.ReactNode;
};
export const ProfileCardInfo = memo(
  ({Icon, label, title}: ProfileCardInfoProp) => {
    return (
      <List.Item
        left={props => Icon && Icon}
        title={title}
        description={label}
        titleStyle={{margin: 0}}
        descriptionStyle={{color: '#64748B'}}
      />
    );
  },
);

const styles = StyleSheet.create({});
