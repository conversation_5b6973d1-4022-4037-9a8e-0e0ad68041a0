import * as React from 'react';
import Svg, {SvgProps, Path, Rect, ClipPath} from 'react-native-svg';

export const Exit = (props: SvgProps) => (
  <Svg width={'1rem'} height={'1rem'} viewBox="0 0 42 42" {...props}>
    <Path
      d="M23.24 30.27H23.11C18.67 30.27 16.53 28.52 16.16 24.6C16.12 24.19 16.42 23.82 16.84 23.78C17.24 23.74 17.62 24.05 17.66 24.46C17.95 27.6 19.43 28.77 23.12 28.77H23.25C27.32 28.77 28.76 27.33 28.76 23.26V16.74C28.76 12.67 27.32 11.23 23.25 11.23H23.12C19.41 11.23 17.93 12.42 17.66 15.62C17.61 16.03 17.26 16.34 16.84 16.3C16.42 16.27 16.12 15.9 16.15 15.49C16.49 11.51 18.64 9.72998 23.11 9.72998H23.24C28.15 9.72998 30.25 11.83 30.25 16.74V23.26C30.25 28.17 28.15 30.27 23.24 30.27Z"
      fill="#212B36"
    />
    <Path
      d="M23.0001 20.75H11.6201C11.2101 20.75 10.8701 20.41 10.8701 20C10.8701 19.59 11.2101 19.25 11.6201 19.25H23.0001C23.4101 19.25 23.7501 19.59 23.7501 20C23.7501 20.41 23.4101 20.75 23.0001 20.75Z"
      fill="#212B36"
    />
    <Path
      d="M13.8499 24.1C13.6599 24.1 13.4699 24.03 13.3199 23.88L9.96994 20.53C9.67994 20.24 9.67994 19.76 9.96994 19.47L13.3199 16.12C13.6099 15.83 14.0899 15.83 14.3799 16.12C14.6699 16.41 14.6699 16.89 14.3799 17.18L11.5599 20L14.3799 22.82C14.6699 23.11 14.6699 23.59 14.3799 23.88C14.2399 24.03 14.0399 24.1 13.8499 24.1Z"
      fill="#212B36"
    />
  </Svg>
);
