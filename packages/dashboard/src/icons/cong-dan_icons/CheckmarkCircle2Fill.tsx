import * as React from 'react';
import Svg, {Path} from 'react-native-svg';

export const SVGCheckmarkCircle2Fill = ({
  color = '#22C55E',
  width = 16,
  height = 16,
  ...props
}) => (
  <Svg width={width} height={height} viewBox="0 0 16 16" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M1.33334 7.99967C1.33334 4.31778 4.31811 1.33301 8.00001 1.33301C9.76812 1.33301 11.4638 2.03539 12.7141 3.28563C13.9643 4.53587 14.6667 6.23156 14.6667 7.99967C14.6667 11.6816 11.6819 14.6663 8.00001 14.6663C4.31811 14.6663 1.33334 11.6816 1.33334 7.99967ZM7.82001 10.4063L10.8667 6.40634V6.38634C11.012 6.1958 11.0446 5.94222 10.9522 5.72113C10.8598 5.50003 10.6565 5.34501 10.4189 5.31446C10.1812 5.28391 9.9453 5.38247 9.80001 5.57301L7.28001 8.90634L6.19334 7.51968C6.04686 7.33152 5.81113 7.23579 5.57494 7.26857C5.33875 7.30134 5.13798 7.45763 5.04827 7.67857C4.95856 7.8995 4.99353 8.15152 5.14001 8.33967L6.76668 10.413C6.89389 10.574 7.08816 10.6674 7.29334 10.6663C7.49967 10.6658 7.69415 10.5698 7.82001 10.4063Z"
      fill={color}
    />
  </Svg>
);
