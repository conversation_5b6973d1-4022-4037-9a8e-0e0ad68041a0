import * as React from 'react';
import Svg, {SvgProps, Path} from 'react-native-svg';
export const SimCardIcon = (props: SvgProps) => (
  <Svg fill="none" {...props}>
    <Path
      fill="#fff"
      d="M12.5 17.063h-6c-2.58 0-4.313-1.733-4.313-4.313v-7.5C2.188 2.67 3.92.937 6.5.937h3.697c1.155 0 2.236.45 3.053 1.26L15.553 4.5a4.276 4.276 0 0 1 1.26 3.053v5.197c0 2.58-1.733 4.313-4.313 4.313Zm-6-15c-1.935 0-3.188 1.252-3.188 3.187v7.5c0 1.935 1.253 3.188 3.188 3.188h6c1.935 0 3.188-1.253 3.188-3.188V7.553c0-.848-.33-1.65-.938-2.258L12.447 3c-.6-.6-1.402-.938-2.257-.938H6.5Z"
    />
    <Path
      fill="#fff"
      d="M11 14.438H8a2.814 2.814 0 0 1-2.813-2.813v-2.25A2.814 2.814 0 0 1 8 6.562h3a2.814 2.814 0 0 1 2.813 2.813v2.25A2.814 2.814 0 0 1 11 14.438Zm-3-6.75a1.69 1.69 0 0 0-1.688 1.687v2.25c0 .93.758 1.688 1.688 1.688h3a1.69 1.69 0 0 0 1.688-1.688v-2.25A1.69 1.69 0 0 0 11 7.687H8Z"
    />
    <Path
      fill="#fff"
      d="M9.5 14.438a.567.567 0 0 1-.563-.563v-6.75c0-.308.256-.563.563-.563.307 0 .563.255.563.563v6.75a.567.567 0 0 1-.563.563Z"
    />
    <Path
      fill="#fff"
      d="M12.875 11.063h-6.75a.567.567 0 0 1-.563-.563c0-.307.255-.563.563-.563h6.75c.307 0 .563.256.563.563a.567.567 0 0 1-.563.563Z"
    />
  </Svg>
);
