import * as React from 'react';
import Svg, {SvgProps, Path, Defs, ClipPath, Rect, G} from 'react-native-svg';

export const Folder: React.FC<SvgProps> = (props: SvgProps) => (
  <Svg fill="none" {...props}>
    <G clipPath="url(#a)">
      <Path
        fill="#F9B552"
        d="M27.38 5.592H4.02a1.6 1.6 0 0 0-1.6 1.6v10.88a1.6 1.6 0 0 0 1.6 1.6h23.36a1.6 1.6 0 0 0 1.6-1.6V7.192a1.6 1.6 0 0 0-1.6-1.6Z"
      />
      <Path
        fill="#FFCF5C"
        d="M28.98 8.152H4.02a3.52 3.52 0 0 0-3.52 3.52v14.4a3.52 3.52 0 0 0 3.52 3.52h24.96a3.52 3.52 0 0 0 3.52-3.52v-14.4a3.52 3.52 0 0 0-3.52-3.52Z"
      />
      <Path
        fill="#FFCF5C"
        d="M12.98 2.392H4.02A3.52 3.52 0 0 0 .5 5.912v5.12a3.52 3.52 0 0 0 3.52 3.52h8.96a3.52 3.52 0 0 0 3.52-3.52v-5.12a3.52 3.52 0 0 0-3.52-3.52Z"
      />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path fill="#fff" d="M.5 0h32v32H.5z" />
      </ClipPath>
    </Defs>
  </Svg>
);
