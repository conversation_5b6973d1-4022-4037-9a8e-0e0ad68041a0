import * as React from 'react';
import Svg, {SvgProps, Path} from 'react-native-svg';
export const WriteIcon = (props: SvgProps) => (
  <Svg fill="none" {...props}>
    <Path
      stroke="#637381"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeMiterlimit={10}
      strokeWidth={1.5}
      d="m9.34 6.4-5.474 5.793c-.206.22-.406.654-.446.954l-.247 2.16c-.087.78.473 1.313 1.247 1.18l2.146-.367c.3-.053.72-.273.927-.5l5.473-5.793c.947-1 1.374-2.14-.1-3.534-1.466-1.38-2.58-.893-3.526.107Z"
    />
    <Path
      stroke="#637381"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeMiterlimit={10}
      strokeWidth={1.5}
      d="M8.427 7.367A4.084 4.084 0 0 0 12.06 10.8M2.5 18.667h12"
    />
  </Svg>
);
