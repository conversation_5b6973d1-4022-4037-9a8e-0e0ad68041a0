import * as React from 'react';
import Svg, {SvgProps, Path} from 'react-native-svg';
export const HomeIconOutLine = (props: SvgProps) => (
  <Svg fill="none" {...props}>
    <Path
      fill={props.color || '#366AE2'}
      d="M18.665 22.75H7.085c-2.74 0-4.96-2.23-4.96-4.97v-7.41c0-1.36.84-3.07 1.92-3.91l5.39-4.2c1.62-1.26 4.21-1.32 5.89-.14l6.18 4.33c1.19.83 2.12 2.61 2.12 4.06v7.28c0 2.73-2.22 4.96-4.96 4.96Zm-8.31-19.31-5.39 4.2c-.71.56-1.34 1.83-1.34 2.73v7.41a3.47 3.47 0 0 0 3.46 3.47h11.58c1.91 0 3.46-1.55 3.46-3.46v-7.28c0-.96-.69-2.29-1.48-2.83l-6.18-4.33c-1.14-.8-3.02-.76-4.11.09Z"
    />
    <Path
      fill={props.color || '#366AE2'}
      d="M12.875 18.75c-.41 0-.75-.34-.75-.75v-3c0-.41.34-.75.75-.75s.75.34.75.75v3c0 .41-.34.75-.75.75Z"
    />
  </Svg>
);
