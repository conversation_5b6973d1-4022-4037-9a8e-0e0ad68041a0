import * as React from 'react';
import Svg, {
  SvgProps,
  Path,
  Defs,
  LinearGradient,
  Stop,
} from 'react-native-svg';
export const MailIcon = (props: SvgProps) => (
  <Svg fill="none" {...props}>
    <Path
      fill="url(#a)"
      d="M9.333 23.228V11.667a7 7 0 0 1 7-7h23.334a7 7 0 0 1 7 7v11.561c0 1.528-.743 2.956-2.02 3.793C40.697 29.61 31.99 35 28 35c-3.99 0-12.697-5.391-16.647-7.979-1.277-.837-2.02-2.265-2.02-3.793Z"
      opacity={0.32}
    />
    <Path
      fill="url(#b)"
      d="M9.331 14.586a155.1 155.1 0 0 0-3.082 2.348 6.231 6.231 0 0 0-2.404 4.498c-.162 2.42-.347 6.485-.347 12.401 0 5.595.33 9.534.64 11.993.256 2.027 1.613 3.635 3.614 4.048 3.21.662 9.337 1.46 20.246 1.46 10.909 0 17.036-.798 20.245-1.46 2.001-.413 3.359-2.02 3.614-4.048.31-2.46.64-6.398.64-11.993 0-5.916-.184-9.98-.346-12.401-.119-1.78-1-3.4-2.404-4.498a155.19 155.19 0 0 0-3.083-2.348v8.642c0 1.528-.742 2.956-2.02 3.793C40.694 29.61 31.988 35 27.998 35S15.3 29.609 11.35 27.021c-1.278-.837-2.02-2.265-2.02-3.793v-8.642Z"
    />
    <Path
      fill="url(#c)"
      d="M16.331 13.417c0-.967.784-1.75 1.75-1.75h10.5a1.75 1.75 0 0 1 0 3.5h-10.5a1.75 1.75 0 0 1-1.75-1.75Z"
    />
    <Path
      fill="url(#d)"
      d="M16.331 21.583c0-.966.784-1.75 1.75-1.75h17.5a1.75 1.75 0 1 1 0 3.5h-17.5a1.75 1.75 0 0 1-1.75-1.75Z"
    />
    <Defs>
      <LinearGradient
        id="a"
        x1={9.333}
        x2={39.024}
        y1={4.667}
        y2={41.209}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#8DB6FC" />
        <Stop offset={1} stopColor="#366AE2" />
      </LinearGradient>
      <LinearGradient
        id="b"
        x1={3.498}
        x2={42.295}
        y1={11.667}
        y2={59.593}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#8DB6FC" />
        <Stop offset={1} stopColor="#366AE2" />
      </LinearGradient>
      <LinearGradient
        id="c"
        x1={3.498}
        x2={42.295}
        y1={11.667}
        y2={59.593}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#8DB6FC" />
        <Stop offset={1} stopColor="#366AE2" />
      </LinearGradient>
      <LinearGradient
        id="d"
        x1={3.498}
        x2={42.295}
        y1={11.667}
        y2={59.593}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#8DB6FC" />
        <Stop offset={1} stopColor="#366AE2" />
      </LinearGradient>
    </Defs>
  </Svg>
);
