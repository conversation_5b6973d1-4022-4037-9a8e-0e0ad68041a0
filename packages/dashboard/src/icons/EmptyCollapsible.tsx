import * as React from 'react';
import Svg, {
  SvgProps,
  Path,
  Defs,
  LinearGradient,
  Stop,
} from 'react-native-svg';
export const EmptyCollapsible = (props: SvgProps) => (
  <Svg fill="none" {...props} width={120} height={120}>
    <Path
      fill="#C4CDD5"
      d="M59.999 78.949c-.082 14.377-.245 36.375-.001 36.418.363 0 .727-.052 1.079-.157l33.75-10.125a3.752 3.752 0 0 0 2.672-3.593v-34.71l-37.5 12.167Z"
      opacity={0.48}
    />
    <Path
      fill="#DFE3E8"
      d="M59.922 78.923 22.5 66.782v34.71a3.751 3.751 0 0 0 2.672 3.592l33.75 10.125c.351.105.715.158 1.078.158-.245-.022-.16-22.06-.078-36.444Z"
      opacity={0.48}
    />
    <Path
      fill="url(#a)"
      d="M60 78.949 22.5 66.782 12.982 80.89a1.873 1.873 0 0 0 .976 2.833l33.74 10.96a1.875 1.875 0 0 0 2.132-.73l10.168-15.004Z"
      opacity={0.8}
    />
    <Path
      fill="url(#b)"
      d="M97.5 66.782 60 78.949l10.168 15.004a1.875 1.875 0 0 0 2.132.73l33.741-10.96a1.874 1.874 0 0 0 .975-2.834L97.5 66.782Z"
      opacity={0.8}
    />
    <Path
      fill="url(#c)"
      d="M50.92 41.82a1.876 1.876 0 0 0-2.13-.728L15.048 52.055a1.872 1.872 0 0 0-.976 2.831l8.425 12.492 37.5-12.208-9.077-13.35Z"
      opacity={0.64}
    />
    <Path
      fill="url(#d)"
      d="M105.924 54.29a1.869 1.869 0 0 0-.24-2.385 1.877 1.877 0 0 0-.735-.446L71.207 40.496a1.876 1.876 0 0 0-2.13.728L60 54.574l37.5 12.208 8.424-12.491Z"
      opacity={0.64}
    />
    <Path
      fill="#C4CDD5"
      d="M97.5 66.782 60 54.574 22.5 66.782 60 78.95l37.5-12.167Z"
      opacity={0.48}
    />
    <Path
      fill="#C4CDD5"
      d="M60 66.676V55L22.5 67.208 60 79.375V66.676Z"
      opacity={0.48}
    />
    <Path
      fill="#919EAB"
      fillOpacity={0.12}
      d="M57.998 12.808c2.029-11.488 19.61-10.745 9.375 0h-9.375ZM57.998 15.718c2.029 11.486 19.61 10.746 9.375 0h-9.375Z"
    />
    <Path
      fill="#919EAB"
      fillOpacity={0.24}
      d="M52.465 59.959a16.431 16.431 0 0 1-2.107-1.628 1.875 1.875 0 0 0-2.535 2.762 20.01 20.01 0 0 0 2.59 2.005 1.875 1.875 0 0 0 2.052-3.14Zm-5.842-5.76a16.3 16.3 0 0 1-1.414-2.274 1.875 1.875 0 0 0-3.343 1.7c.476.934 1.054 1.87 1.738 2.798a1.875 1.875 0 0 0 3.019-2.224Zm-2.742-7.652a8.68 8.68 0 0 1 .332-2.192 1.874 1.874 0 1 0-3.605-1.033c-.29 1.02-.45 2.072-.477 3.131a1.876 1.876 0 0 0 3.75.094Zm2.884-6.262a11.076 11.076 0 0 1 1.613-1.232 1.877 1.877 0 0 0 .082-3.118 1.875 1.875 0 0 0-2.083-.055 14.723 14.723 0 0 0-2.16 1.654 1.874 1.874 0 0 0 2.548 2.75Zm6.973-3.235c.99-.18 1.991-.29 2.997-.33a1.877 1.877 0 0 0-.16-3.748 25.312 25.312 0 0 0-3.52.39 1.876 1.876 0 0 0 .683 3.688Zm9.773-.11a44.95 44.95 0 0 0 4.47-.16 1.875 1.875 0 0 0-.332-3.735 41.04 41.04 0 0 1-4.095.145 1.875 1.875 0 0 0-.043 3.75Zm10.633-1.31c1.856-.567 3.424-1.295 4.736-2.13a1.875 1.875 0 1 0-2.015-3.162 15.567 15.567 0 0 1-3.818 1.706 1.875 1.875 0 1 0 1.097 3.587Zm9.544-7.377a11.644 11.644 0 0 0 1.138-5.631 1.875 1.875 0 1 0-3.746.174 7.904 7.904 0 0 1-.77 3.825 1.877 1.877 0 0 0 3.378 1.632Zm-1.195-12.261c-1.367-1.847-3.22-3.133-5.242-3.548a1.877 1.877 0 0 0-.754 3.675c1.166.239 2.194 1.04 2.983 2.106a1.874 1.874 0 1 0 3.013-2.233ZM57.83 16.157h12.555a1.875 1.875 0 0 0 0-3.75H57.83a1.877 1.877 0 0 0 0 3.75Z"
    />
    <Defs>
      <LinearGradient
        id="a"
        x1={22.748}
        x2={27.989}
        y1={66.767}
        y2={101.442}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#F4F6F8" />
        <Stop offset={1} stopColor="#C4CDD5" />
      </LinearGradient>
      <LinearGradient
        id="b"
        x1={60.152}
        x2={99.684}
        y1={78.821}
        y2={101.557}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#F4F6F8" />
        <Stop offset={1} stopColor="#C4CDD5" />
      </LinearGradient>
      <LinearGradient
        id="c"
        x1={17.588}
        x2={23.245}
        y1={60.101}
        y2={33.434}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#F4F6F8" />
        <Stop offset={1} stopColor="#C4CDD5" />
      </LinearGradient>
      <LinearGradient
        id="d"
        x1={70.039}
        x2={72.061}
        y1={40.044}
        y2={72.96}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#F4F6F8" />
        <Stop offset={1} stopColor="#C4CDD5" />
      </LinearGradient>
    </Defs>
  </Svg>
);
