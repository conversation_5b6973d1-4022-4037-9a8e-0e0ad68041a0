import axios, {
  AxiosRequestHeaders,
  AxiosRequestConfig,
  AxiosError,
} from 'axios';

const check = async (
  config: AxiosRequestConfig,
): Promise<AxiosRequestConfig> => {
  try {
    if (true) {
      config.headers = {
        ...(config.headers as AxiosRequestHeaders),
        Authorization: `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyIjoidGllcG5oYW5fcGhhbmNodXRyaW5oIiwidXNlcklEIjoiMTY2OTIiLCJUZWNoSUQiOiIiLCJleHAiOjE3MzY0ODM1NzQsImlzcyI6ImRpY2h2dWNvbmcuaGFub2kuZ292LnZuIiwiYXVkIjoiZGljaHZ1Y29uZy5oYW5vaS5nb3Yudm4ifQ.kr25zQIdjQGOzjXQX4T8XkykbG4585_RJ-cz-MKy7e8`,
      };
    }
    if (config.method?.toUpperCase() === 'GET') {
      config.params = {...config.params};
    }
    return config;
  } catch (error) {
    console.error(error);
    throw error;
  }
};
const handleError = (error: AxiosError): Promise<AxiosError> => {
  console.error(error.message);
  return Promise.reject(error);
};
const api = axios.create();

api.interceptors.request.use(check, handleError);

export default api;
