/* eslint-disable react-native/no-inline-styles */
import {ApiClient, useAuthStore} from '@ac-mobile/common';
import {Federated} from '@callstack/repack/client';
import {addEventListener} from '@react-native-community/netinfo';
import {NavigationContainer} from '@react-navigation/native';
import {NativeWindStyleSheet, useColorScheme} from 'nativewind';
import React, {useEffect, useState} from 'react';
import {Dimensions, StyleSheet, View} from 'react-native';
import RNBootSplash from 'react-native-bootsplash';
import {
  DefaultTheme,
  Icon,
  MD3Colors,
  MD3Theme,
  PaperProvider,
  Text,
  useTheme,
} from 'react-native-paper';
import {SafeAreaProvider, SafeAreaView} from 'react-native-safe-area-context';
import Toast from 'react-native-toast-message';
import ErrorBoundary from './components/ErrorBoundary';
import SplashScreen from './components/SplashScreen';
import MainNavigator from './navigation/MainNavigator';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {QueryClient, QueryClientProvider} from 'react-query';

const SignInScreen = React.lazy(() =>
  Federated.importModule('auth', './SignInScreen'),
);

const SignInScreenContainer = () => {
  React.useEffect(() => {
    RNBootSplash.hide({fade: true});
  }, []);

  return <SignInScreen />;
};

const AcClient = ApiClient.getInstance('ac-app');
AcClient.setEndpoint('https://api.demo.pro/api');

const Main = ({children}: any) => {
  const theme = useTheme();

  const [isConnected, setIsConnected] = useState<boolean | undefined | null>(
    true,
  );

  useEffect(() => {
    const unsubscribe = addEventListener(state => {
      // console.log('Connection type', state.type);
      // console.log('Is connected?', state.isConnected);
      setIsConnected(state.isConnected);
    });

    // Unsubscribe
    return () => {
      unsubscribe();
    };
  }, []);

  return (
    <View
      style={{
        flex: 1,
        // marginTop: Platform.OS === 'android' ? 0 : insets.top,
        // marginTop: insets.top,
        backgroundColor: theme.colors.onPrimaryContainer,
      }}>
      {!isConnected && (
        <View
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'row',
            gap: 4,
          }}>
          <Icon size={12} color={theme.colors.error} source="wifi-alert" />
          <Text style={{fontSize: 12, color: theme.colors.error}}>
            Vui lòng kiểm tra kết nối mạng!
          </Text>
        </View>
      )}
      {children}
    </View>
  );
};

type ACColors = {
  myOwnColor: string;
};

const theme: MD3Theme & {colors: typeof MD3Colors & ACColors} = {
  ...DefaultTheme,
  // Specify custom property
  // myOwnProperty: true,
  dark: false,
  colors: {
    ...DefaultTheme.colors,
    primary: '#254ed3',
    // primary: '#1677ff', // ant design
    // primary: '#407BFF',
    // ...DefaultTheme.colors.primaryContainer,
    primaryContainer: '#dae2ff',
    onSurface: '#1c1b1d',
    Surface: 'red',
    myOwnColor: '#BADA55',
  } as any,
};

const App = () => {
  const {colorScheme} = useColorScheme();
  console.log(colorScheme, 'colorScheme');

  const {user} = useAuthStore();
  NativeWindStyleSheet.setColorScheme(colorScheme || 'light');
  const queryClient = new QueryClient();
  const screenHeight = Dimensions.get('window').height;
  return (
    <GestureHandlerRootView
      style={[styles.containerGesture, {paddingTop: screenHeight * 0.5}]}>
      <QueryClientProvider client={queryClient}>
        <SafeAreaView
          style={{
            flex: 1,
            padding: 0,
            margin: 0,
          }}>
          <SafeAreaProvider>
            <PaperProvider theme={theme} settings={{}}>
              <View style={styles.container}>
                <ErrorBoundary name="AuthProvider">
                  <React.Suspense fallback={<SplashScreen />}>
                    {(!user ||
                      (typeof user === 'object' &&
                        Object.keys(user).length === 0)) && (
                      <React.Suspense fallback={<SplashScreen />}>
                        <SignInScreenContainer />
                      </React.Suspense>
                    )}
                    {user && (
                      <NavigationContainer
                        onReady={() => RNBootSplash.hide({fade: true})}>
                        <Main>
                          <MainNavigator />
                        </Main>
                      </NavigationContainer>
                    )}
                  </React.Suspense>
                </ErrorBoundary>
                <Toast />
              </View>
            </PaperProvider>
          </SafeAreaProvider>
        </SafeAreaView>
      </QueryClientProvider>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  containerGesture: {
    flex: 1,
    backgroundColor: 'grey',
  },
});

export default App;
