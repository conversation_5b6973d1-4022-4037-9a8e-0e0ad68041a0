import moment from 'moment';
import {Platform} from 'react-native';

export const formatDateTime = (dateTime: string, defaultValue = '-') => {
  return (
    moment(dateTime)
      .format('HH:mm DD/MM/YYYY')
      .replace(`/${new Date().getFullYear()}`, '') ||
    defaultValue ||
    dateTime
  );
};
export const getRangeYearPicker = (range: number, now?: boolean) => {
  const currentYear = new Date().getFullYear();
  return Array.from({length: range * (now ? 1 : 2) + 1}, (_, i) => {
    const newYear = now ? currentYear - i : currentYear - range + i;
    const _year = newYear.toString();
    return {
      label: _year,
      value: _year,
    };
  });
};
export const ListRangeYearPicker = getRangeYearPicker(100, false);

export const IsPlatformIOS = Platform.OS === 'ios';
export const IsPlatformANDROID = Platform.OS === 'android';
export const formatCurrentDate = (date?: Date) => {
  if (!date) {
    date = new Date();
  }
  const days = [
    'Chủ nhật',
    'Thứ 2',
    'Thứ 3',
    'Thứ 4',
    'Thứ 5',
    'Thứ 6',
    'Thứ 7',
  ];
  const dayName = days[date.getDay()];

  const formattedDate = new Intl.DateTimeFormat('vi-VN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  }).format(date);

  return `${dayName}, ngày ${formattedDate}`;
};
