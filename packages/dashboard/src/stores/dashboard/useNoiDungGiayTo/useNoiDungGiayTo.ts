import {create} from '@ac-mobile/common';
import {handleFunction} from '../../../api/dashboard-api';
import Toast from 'react-native-toast-message';
type INoiDungGiayTo = {
  totalRecords: number;
  listNguoiKy: [
    {
      chuKy: string;
      chungThu: string;
      ngayKy: string;
      nguoiKy: string;
      phatHanh: string;
    },
  ];
  tapTinDinhKemID: number;
  donViID: string;
  quaTrinhXuLyID: string;
  nguoiXuLyHoSoID: string;
  originalName: string;
  uploadName: string;
  pathName: string;
  fileSize: string;
  userID: string;
  sealed: string;
  sealedDate: string;
  signerID: string;
  hoSoID: string;
  createdUserID: string;
  createdDate: string;
  lastUpdUserID: string;
  lastUpdDate: string;
  totalDVC: number;
};
type IFileLocal = {
  fileCopyUri?: string | null;
  name?: string | null;
  size?: number | null;
  type?: string | null;
  uri?: string | null;
  copyError?: string | null;
};
type initDataNoiDungGiayTo = {
  dsNoiDungGiayTo: INoiDungGiayTo[];
  dsHoSoLocal: IFileLocal[];
  isRefresh: boolean;
  getAllDanhSachNoiDungGiayTo: (params: {
    DonViID?: number;
    HoSoID?: number;
    TotalRecords?: number;
    UserID?: number;
    PageNum?: number;
    PageSize?: number;
  }) => void;
  refreshDsHoSo: () => void;

  DeleteHoSoDinhKem: (params: {
    fileName: string;
    tapTinDinhKemID: number;
  }) => void;
  saveLocalFile: (param?: IFileLocal) => void;
  clearLocalFile: () => void;
  deleteLocalFile: (uri?: string) => void;
};
export const useNoiDungGiayTo = create<initDataNoiDungGiayTo>()((set, get) => ({
  dsNoiDungGiayTo: [],
  dsHoSoLocal: [],
  isRefresh: false,
  refreshDsHoSo: () => {
    set({isRefresh: !get().isRefresh});
  },
  getAllDanhSachNoiDungGiayTo: async (params: {
    DonViID?: number;
    HoSoID?: number;
    TotalRecords?: number;
    UserID?: number;
    PageNum?: number;
    PageSize?: number;
  }) => {
    try {
      const data = await handleFunction.GetNoiDungGiayTo({...params});
      if (data) {
        set({dsNoiDungGiayTo: data?.data?.data});
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'load nội dung giấy tờ thất bại',
      });
    }
  },

  DeleteHoSoDinhKem: async (params: {
    fileName: string;
    tapTinDinhKemID: number;
  }) => {
    const {fileName, tapTinDinhKemID} = params;
    await handleFunction.DeleteFile({
      fileName: fileName,
      tapTinDinhKemID: tapTinDinhKemID,
    });
  },
  saveLocalFile: async (param?: IFileLocal) => {
    if (get().dsHoSoLocal?.some(i => param?.uri == i.uri)) {
      Toast.show({
        type: 'error',
        text1: 'Tệp đã được chọn',
      });
      return;
    }
    set({dsHoSoLocal: [...get().dsHoSoLocal, {...param}]});
  },
  clearLocalFile: () => {
    set({dsHoSoLocal: []});
  },
  deleteLocalFile: (uri?: string) => {
    set(state => ({
      dsHoSoLocal: state.dsHoSoLocal.filter(i => i.uri !== uri),
    }));
  },
}));
