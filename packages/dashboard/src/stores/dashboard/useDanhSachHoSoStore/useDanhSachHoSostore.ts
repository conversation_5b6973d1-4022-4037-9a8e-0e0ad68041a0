import {AnyObj, create} from '@ac-mobile/common';
import {handleFunction} from '../../../api/dashboard-api';
import {CalendarDate} from 'react-native-paper-dates/lib/typescript/Date/Calendar';
import moment from 'moment';
import {defaultValueFilter} from '../../../screens/dashboard/Search/searchHelperfn';
export type IDanhSachHoSo = {
  boHoSoID?: string;
  boSungHoSo?: boolean;
  boSungID?: number;
  checkDuongDi?: boolean;
  chiNhanhDiemTiepNhan?: string;
  chucNangHienTai?: string;
  chucNangKeTiep?: string;
  DocId?: string;
  chucNangTruoc?: string;
  coTheChuyen?: boolean;
  daBoSung?: boolean;
  daLuong?: boolean;
  daNhan?: boolean;
  daNhapLieu?: boolean;
  daSoHoa?: number;
  daThanhToan?: boolean;
  danhSachDinhKemBoSung?: string;
  diaChi?: string;
  diaChiDangKy?: string;
  diaChiUyQuyenToChuc?: string;
  donViBanHanh?: string;
  donViNhanID?: string;
  donViXuLyHoSoID?: number;
  donViXuLyID?: string;
  dungOrTre?: number;
  duongDiHoSo?: string;
  duongDiID?: number;
  duongID?: string;
  errorMessage?: string;
  fileLLTP?: string;
  follow?: boolean;
  giaiDoan2?: boolean;
  hasFile?: number;
  hoSoID?: number;
  hoSoOnlineID?: number;
  hoSoPhiDiaGioi?: boolean;
  hoSoVneID?: string;
  hoTenNguoiNop?: string;
  hoanTat?: boolean;
  isEnable?: boolean;
  khongGiaiQuyetHoSo?: boolean;
  khongPheDuyetHoSo?: boolean;
  kiemTraNhapLieu?: boolean;
  lienThong?: boolean;
  linhVucID?: number;
  lkhS_isChecked?: string;
  loaiModule?: string;
  loaiXuLy?: number;
  maQuyTrinh?: string;
  maTraCuu?: string;
  namSinh?: string;
  ngay?: string;
  ngayChuyen?: string;
  ngayDenHenXuLy?: string;
  ngayHenTra?: string;
  ngayHoanTatHoSo?: string;
  ngayHopLe?: string;
  ngayNhan?: string;
  ngayNhanXuLy?: string;
  ngayPhatHanh?: string;
  ngaySinh?: string;
  ngayThucTra?: string;
  nghiepVu?: boolean;
  nguoiDungTen?: boolean;
  nguoiDungTenHoSo?: string;
  nguoiNhanID?: string;
  nguoiXuLy?: string;
  nguoiXuLyID?: string;
  nguyenDoTre?: string;
  nhacMau?: string;
  nhanBoSung?: boolean;
  nhanBoSungHoSo?: boolean;
  nhanKetQuaQuaDuongBuuDien?: boolean;
  nhanLienThong?: string;
  noiDungKhac?: string;
  phanHoi?: boolean;
  phanHoiHoSo?: boolean;
  phongBanXuLyID?: string;
  phuongID?: string;
  quaTrinhXuLyHienTaiID?: number;
  quanHuyenXuLy?: string;
  quanID?: string;
  scanFile?: number;
  sdT_EmailUyQuyenToChuc?: string;
  showFormLyDoTre?: boolean;
  sms?: boolean;
  soBienBanBanGiao?: string;
  soBienLai?: string;
  soBienNhan?: string;
  soBoHoSo?: string;
  soDienThoaiGoiThongBao?: string;
  soGiayToTuyThan?: string;
  soGioConLai?: string;
  soLuongNopHoSoGoc?: string;
  soNgayConLai?: number;
  soNgayGiaiQuyet?: number;
  soPhatHanh?: string;
  tenChucNangHienTai?: string;
  tenDonViNhanHCC?: string;
  tenDuongDi?: string;
  tenLinhVuc?: string;
  tenPhuongXa?: string;
  tenThuTuc?: string;
  tenToChuc?: string;
  thangSinh?: string;
  thoiGian?: string;
  thongTinLienLac?: string;
  thuHoi?: boolean;
  thuTucHanhChinhID?: number;
  tinhTrang?: string;
  totalRecords?: 1;
  trangThaiHoSo?: string;
  trangThaiLienThong?: string;
  trangThaiPhi?: string;
  trangThaiVanBanDi?: string;
  trichYeu?: string;
  xuLyLai?: boolean;
  ycbsHoSo?: string;
  listNguoiNhan?: {label: string; value: string}[];
  khongPheDuyetID?: number;
  lyDoID?: string;
  LinhVucID?: number;
  ngayNhanLaiHoSo?: string;
  ngayXuly?: string;
  noiDungXuLy?: string;
  yKienXuLy?: string;
  nguoiKyID?: string;
  phongBanID?: string;
  donViID?: string;
  quaTrinhXuLyID?: string;
  lanhDaoXuLy?: number;
  nguoiXuLyHoSoHienTaiID?: number;
};

export type IFilterType = {
  // MaHoSo: string;
  linhVuc: any;
  fromDate: CalendarDate;
  toDate: CalendarDate;
  isQuaHan: boolean;
  OrderBy: {label: string; value: string};
  keyword: string;
};
type danhSachHoSoFunction = {
  page: number;
  size: number;
  refresh: boolean;
  loadMore: boolean;
  hasMore: boolean;
  isLoading: boolean;
  filter: IFilterType;
  findData: AnyObj;
  setFindData: (data: AnyObj) => void;
  dsHoSoChuaNhan: IDanhSachHoSo[];
  dsHoSoDaNhan: IDanhSachHoSo[];
  dsHosoDaXuLy: IDanhSachHoSo[];
  handleGetDanhSachHoSo: ({
    donviID,
    phongbanID,
    userID,
    chucNangHienTai,
  }: {
    donviID: number;
    phongbanID: number;
    userID: number;
    chucNangHienTai: string;
  }) => void;
  handleSetFilter: (params: {
    fromDate: CalendarDate;
    toDate: CalendarDate;
    isQuaHan: boolean;
    linhVuc: string;
    OrderBy: {label: string; value: string};
    keyword: string;
  }) => void;
};

export const useDanhSachHoSo = create<IDanhSachHoSo & danhSachHoSoFunction>()(
  (set, get) => ({
    dsHoSo: [],
    dsHoSoDaNhan: [],
    dsHoSoChuaNhan: [],
    dsHosoDaXuLy: [],
    loadMore: false,
    refresh: false,
    hasMore: true,
    isLoading: false,
    page: 1,
    size: 50,
    findData: {
      ...defaultValueFilter,
    },
    setFindData: data => {
      set({findData: data});
    },
    filter: {
      ...defaultValueFilter,
    },
    handleSetFilter: (params: IFilterType) => {
      set({
        filter: {
          ...params,
        },
      });
    },
    handleGetDanhSachHoSo: async ({
      donviID,
      phongbanID,
      userID,
      chucNangHienTai,
    }: {
      donviID: number;
      phongbanID: number;
      userID: number;
      chucNangHienTai: string;
    }) => {
      set({isLoading: true});
      const {data} = await handleFunction.danhSachHoSoPheDuyet({
        page: get().page,
        fromDate: get().filter.fromDate
          ? moment(get().filter.fromDate).format('YYYY-MM-DD')
          : '',
        toDate: get().filter.toDate
          ? moment(get().filter.toDate).format('YYYY-MM-DD')
          : '',
        isQuaHan: get().filter.isQuaHan,
        linhVuc: get().filter.linhVuc,
        OrderBy: 'asc',
        keyword: get().filter.keyword,
        userID: userID,
        donviID: donviID,
        phongbanID,
        chucNangHienTai,
        daNhan: 'daNhan',
      });
      if (data && Array.isArray(data?.data)) {
        set({
          dsHoSoDaNhan: data?.data.filter(
            (_: IDanhSachHoSo) => _.daNhan == true && _.lanhDaoXuLy == 0,
          ),
          dsHoSoChuaNhan: data?.data.filter(
            (_: IDanhSachHoSo) => _.daNhan == false,
          ),
          dsHosoDaXuLy: data?.data.filter(
            (_: IDanhSachHoSo) =>
              _.daNhan == true && (_.lanhDaoXuLy == 1 || _.lanhDaoXuLy == 2),
          ),
          page: 1,
          loadMore: false,
          refresh: false,
        });
      }
      set({isLoading: false});
    },
  }),
);
