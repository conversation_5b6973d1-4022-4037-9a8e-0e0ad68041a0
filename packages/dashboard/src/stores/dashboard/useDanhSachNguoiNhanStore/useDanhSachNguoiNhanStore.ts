import {create} from '@ac-mobile/common';
import {handleFunction} from '../../../api/dashboard-api';
type InguoiNhanChuyenHoSo = {
  value: any;
  ten: string;
  ma: string;
};
type initData = {
  dsNguoiNhan: InguoiNhanChuyenHoSo[];
  getNguoiNhanChuyenHoSo: (params: {
    LinhVucID?: number;
    ThuTucHanhChinhID?: number;
    LoaiXuLy?: number;
    ChucNangHienTai?: string;
    MaQuyTrinh?: string;
  }) => void;
};
export const useDanhSachNguoiNhan = create<initData>()((set, get) => ({
  dsNguoiNhan: [],
  getNguoiNhanChuyenHoSo: async (params: {
    LinhVucID?: number;
    ThuTucHanhChinhID?: number;
    LoaiXuLy?: number;
    ChucNangHienTai?: string;
    MaQuyTrinh?: string;
  }) => {
    try {
      const {data} = await handleFunction.GetNguoiNhanChuyenHoSo({...params});
      if (data) {
        const newdata = data?.data.map((item: InguoiNhanChuyenHoSo) => {
          return {label: item.ten, value: item.ma};
        });

        set({
          dsNguoiNhan: newdata,
        });
      }
    } catch (error) {
      console.log('error at GetNguoiNhanChuyenHoSo', error);
      throw error;
    }
  },
}));
