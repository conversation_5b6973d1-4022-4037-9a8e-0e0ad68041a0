import {AcUser, AnyObj, create, useAuthStore} from '@ac-mobile/common';
import {handleUser} from '../../../api/dashboard-api';

export type IUser = {
  user_MasterID: number;
  userName: String;
  phongBanID: number;
  tenPhongBan: String | null;
  donViID: number;
  user_PortalID: number;
  canBoID: number;
  maDonVi: String;
  tenDonVi: String;
  ngheNghiep: String;
  maSoCanBo: String;
  soBHXH: String;
  conNguoiID: number;
  chuyenMon: String;
  donViCapChaID: number;
  hoTen: String;
  laDonViChinh: boolean;
  capDonViID: number;
  soNha: String;
  duongID: number;
  tinhThanhID: number;
  quanHuyenID: number;
  phuongXaID: number;
  used: boolean;
  authorised: boolean;
};

type danhSachHoSoFunction = {
  isLoading: boolean;
  userMasters: AcUser[];

  handleGetListUserMaster: () => void;
};

export const useUser = create<danhSachHoSoFunction>()((set, get) => ({
  isLoading: false,
  userMasters: [],

  handleGetListUserMaster: async () => {
    try {
      const {user} = useAuthStore.getState();
      if (!user) {
        return;
      }
      set({isLoading: true});
      const response = await handleUser.getInfo(`${user?.user_PortalID}`);

      if (response?.data?.data) {
        const dataFormat = response.data.data.filter((item: AnyObj) => {
          if (item.user_MasterID !== user?.user_MasterID) {
            return {
              user_MasterID: item?.user_MasterID || 0,
              userName: item?.userName || '',
              phongBanID: item?.phongBanID || 0,
              tenPhongBan: item?.tenPhongBan || null,
              donViID: item?.donViID || 0,
              user_PortalID: item?.user_PortalID || 0,
              canBoID: item?.canBoID || 0,
              maDonVi: item?.maDonVi || '',
              tenDonVi: item?.tenDonVi || '',
              ngheNghiep: item?.ngheNghiep || '',
              maSoCanBo: item?.maSoCanBo || '',
              soBHXH: item?.soBHXH || '',
              conNguoiID: item?.conNguoiID || 0,
              chuyenMon: item?.chuyenMon || '',
              donViCapChaID: item?.donViCapChaID || 0,
              hoTen: item?.hoTen || '',
              laDonViChinh: item?.laDonViChinh || false,
              capDonViID: item?.capDonViID || 0,
              soNha: item?.soNha || '',
              duongID: item?.duongID || 0,
              tinhThanhID: item?.tinhThanhID || 0,
              quanHuyenID: item?.quanHuyenID || 0,
              phuongXaID: item?.phuongXaID || 0,
              used: item?.used || false,
              authorised: item?.authorised || false,
            } as AcUser;
          }
        });
        set({userMasters: [user, ...dataFormat]});
        return;
      }
      set({userMasters: []});
    } catch (error) {
      console.log('handleGetListUserMaster error', error);
      set({userMasters: []});
    } finally {
      set({isLoading: false});
    }
  },
}));
