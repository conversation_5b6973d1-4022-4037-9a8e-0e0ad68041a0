import {create} from '@ac-mobile/common';
import {handleFunction} from '../../../api/dashboard-api';

export type hoSoKhongPheDuyetType = {
  khongPheDuyetID: number;
  hoSoID: number;
  lyDoID: string;
  soBienNhan: string;
  soBoHoSo: string;
  LinhVucID: number;
  tenLinhVuc: string;
  tenThuTuc: string;
  ngayNhan: string;
  ngayNhanLaiHoSo: string;
  hoTenNguoiNop: string;
  ngayXuly: string;
  noiDungXuLy: string;
  yKienXuLy: string;
  loaiXuLy: string;
  lyDoKhac: string;
  nguoiKyID: string;
  nguoiXuLyID: string;
  daNhan: boolean;
  phongBanID: string;
  donViID: string;
  quaTrinhXuLyID: string;
};

type initDataHoSoKhongPheDuyet = {
  hoSoKhongPheDuyet?: hoSoKhongPheDuyetType;
  handleGetHoSoKhongPheDuyet: (params: {
    DonViID?: number;
    HoSoID?: number;
    LoaiXuLy?: string;
    QuaTrinhXuLyID?: number;
  }) => void;
};
export const HoSoKhongPheDuyetStore = create<initDataHoSoKhongPheDuyet>()(
  (set, get) => ({
    hoSoKhongPheDuyet: undefined,
    handleGetHoSoKhongPheDuyet: async (params: {
      DonViID?: number;
      HoSoID?: number;
      LoaiXuLy?: string;
      QuaTrinhXuLyID?: number;
    }) => {
      const data = await handleFunction.GetThongTinHoSoKhongPheDuyet({
        ...params,
      });
      if (data) {
        set({hoSoKhongPheDuyet: {...data?.data?.data}});
      }
    },
  }),
);
