import {create} from '@ac-mobile/common';
import {handleFunction} from '../../../api/dashboard-api';
type IHoSoDinhKem = {
  createdDate: string;
  createdUserID: string;
  // hoSoKemTheoID: number;
  donViID: string;
  eform: string;
  fileDaKySo: string;
  fileID: string;
  fileName: string;
  filePath: string;
  ghiChu: string;
  hoSoID: string;
  hoSoKTIDFix: number;
  hoSoKemTheoID: number;
  hoSoOnlineID: number;
  isDaNopHoSoGoc: boolean;
  lastUpdDate: string;
  lastUpdUserID: string;
  rowNumber: number;
  soBanChinh: number;
  soBanPhoTo: number;
  soBanSao: number;
  tenFile: string;
  tenHoSoKemTheo: string;
  urlFileDauRa: string;
  urlFileDauVao: string;
};
type IHoSoKemTheo = {
  IHoSoKemTheoID?: number;
  rowNumber?: number;
  donViID?: string;
  tenIHoSoKemTheo?: string;
  soBanChinh?: number;
  soBanSao?: number;
  soBanPhoTo?: number;
  ghiChu?: string;
  hoSoID?: string;
  fileName?: string;
  filePath?: string;
  createdUserID?: string;
  createdDate?: string;
  lastUpdUserID?: string;
  lastUpdDate?: string;
  isDaNopHoSoGoc?: boolean;
  fileID?: string;
  eform?: string;
  dM_IHoSoKemTheoID?: number;
  hoSoOnlineID?: number;
  hoSoKTIDFix?: number;
  urlFileDauVao?: string;
  urlFileDauRa?: string;
  fileDaKySo?: string;
  tenFile?: string;
};
type IQuaTrinhXulyHoSo = {
  stt?: number;
  quaTrinhXuLyID?: number;
  quaTrinhHienTai?: boolean;
  hoiQuy?: boolean;
  buocXuLy?: string;
  chuyenVienXL?: string;
  soNgayGiaiQuyet?: string;
  parentID?: number;
  ngayNhan?: string;
  ngayXuLy?: string;
  dungHen?: string;
  truocHen?: string;
  treHen?: string;
  nguyenDoTre?: string;
  noiDungXuLy?: string;
  daNhan?: string;
  tenDonViNhan?: string;
  tongNguoiXL?: number;
  tenLoaiXuLy?: string;
};
type IThongTinThuLyHoSo = {
  hoSoID?: number;
  tenThuTuc?: string;
  hoTenNguoiNop?: string;
  soBienNhan?: string;
  ngayNhan?: string;
  ngayDenHen?: string;
  ngayHenTra?: string;
  thoiGianXuLyChoPhep?: number;
  linhVucID?: number;
  chucNangKeTiep?: string;
  tenChucNangKeTiep?: string;
  loaiXuLy?: string;
  chucNangHienTai?: string;
  tenChucNangHienTai?: string;
  noiDungXuLyTongHop?: string;
  userID?: string;
  donViID?: string;
  quaTrinhXuLyID?: number;
  nguoiXuLyHoSoHienTaiID?: number;
  treHanXuLy?: number;
  noiDungXuLy?: string;
  lyDoTreHanID?: number;
  nguyenDoTre?: string;
  nhanBanGiayDongDau?: boolean;
};
type IChiTietHoSo = {
  thongTinThuLyHoSo?: IThongTinThuLyHoSo;
  hoSoKemTheo?: IHoSoKemTheo[];
  quaTrinhXulyHoSo?: IQuaTrinhXulyHoSo[];
};
type initDataHoSoDinhKem = {
  chiTietHoSo?: IChiTietHoSo;
  dsHoSoDinhKem: IHoSoDinhKem[];
  loading: boolean;
  getDanhSachHoSoDinhKem: (params: {HoSoID: number; DonViID: number}) => void;
  getChiTietHoSo: (params: {
    ChucNangHienTai: string;
    ChucNangKeTiep: string;
    DonViID: number;
    LoaiXuLy: string;
    UserID: number;
    HoSoID: number;
  }) => void;
  resetDsHoSoDinhKem: () => void;
  resetChiTietHoSo: () => void;
};
export const useHoSoDinhKemStore = create<initDataHoSoDinhKem>()(
  (set, get) => ({
    dsHoSoDinhKem: [],
    chiTietHoSo: undefined,
    loading: false,
    getDanhSachHoSoDinhKem: async (params: {
      HoSoID?: number;
      DonViID?: number;
    }) => {
      const {DonViID, HoSoID} = params;
      // console.log('LÀNKFLN', params);

      const {data} = await handleFunction.GetHoSoKemTheo({
        HoSoID: HoSoID,
        DonViID: DonViID,
      });
      // console.log(, data?.data);
      if (data) {
        const dataMaper = data?.data.map((_: IHoSoDinhKem) => {
          return {
            title: _.tenHoSoKemTheo,
            file: _.fileName ? _.fileName : undefined,
          };
        });
        set({
          dsHoSoDinhKem: dataMaper,
        });
      }
    },
    getChiTietHoSo: async (params: {
      ChucNangHienTai: string;
      ChucNangKeTiep: string;
      DonViID: number;
      LoaiXuLy: string;
      UserID: number;
      HoSoID: number;
    }) => {
      set({loading: true});
      const {
        ChucNangHienTai,
        ChucNangKeTiep,
        DonViID,
        LoaiXuLy,
        UserID,
        HoSoID,
      } = params;
      const {data} = await handleFunction.getChiTietHoSo({
        HoSoID: HoSoID,
        ChucNangKeTiep: ChucNangKeTiep,
        ChucNangHienTai: ChucNangHienTai,
        UserID: UserID,
        DonViID: DonViID,
        LoaiXuLy: LoaiXuLy,
      });

      if (data) {
        set({chiTietHoSo: data?.data});
        set({loading: false});
      }
    },
    resetDsHoSoDinhKem: () => {
      set({dsHoSoDinhKem: []});
    },
    resetChiTietHoSo: () => {
      set({chiTietHoSo: undefined});
    },
  }),
);
