import {create, createJSONStorage, persist} from '@ac-mobile/common';
import {ILoading} from '../../model';
import AsyncStorage from '@react-native-async-storage/async-storage';

export type CertStoreState = {
  loading: ILoading;
  cert: string | null;
};
export type SimStoreAction = {
  getCert: () => void;
};

const initState: CertStoreState = {
  loading: 'none',
  cert: null,
};
export const useCertStore = create<CertStoreState & SimStoreAction>()(
  persist(
    set => ({
      ...initState,
      getCert: () => {
        set({loading: 'loading'});
        setTimeout(() => {
          set({
            loading: 'success',
            cert: '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',
          });
        }, 2000);
      },
    }),
    {
      name: 'sim-cert-persist-v1',
      storage: createJSONStorage(() => AsyncStorage),
    },
  ),
);
