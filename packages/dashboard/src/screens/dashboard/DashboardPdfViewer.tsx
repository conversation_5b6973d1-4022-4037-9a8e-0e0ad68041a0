import React, {useCallback, useEffect, useState} from 'react';
import {StyleSheet, Dimensions, View, Text} from 'react-native';
const RNFS = require('react-native-fs');
import {PDFDocument} from 'pdf-lib';
import {SIGNATURE_TYPE} from '../../utils/constance';
import {handleFunction} from '../../api/dashboard-api';
import Toast from 'react-native-toast-message';
import {CCAppBar} from '../../components/HeaderComponent/CCAppBar';
import {
  ActivityIndicator,
  Button,
  Dialog,
  IconButton,
  Portal,
  TextInput,
  useTheme,
} from 'react-native-paper';
import {useRoute, RouteProp, useNavigation} from '@react-navigation/native';
import {MainStackParamList} from '../../navigation/MainNavigator';
import {useAuthStore} from '@ac-mobile/common';
import {simPKiFile} from '../../api/sign-api';
import {useQueryClient} from 'react-query';
import {DocumentPickerResponse} from 'react-native-document-picker';
import {useCongchucAPIGetSignBase64} from '../../useQueryState/UseCongChucAPIGetSignBase64';
import {
  _base64ToArrayBuffer,
  _getImageDimensions,
  _uint8ToBase64,
} from './DashboardPdfViewerHelperFunction';
import {useSimCert} from '../../useQueryState/UseSimCert';
import Pdf from 'react-native-pdf';
import WebView from 'react-native-webview';
import htmlContent from './HtmlContent';
import {SaveIcon, EditIcon} from '../../icons';
const signatureWidth = 100;
const signatureHeight = 100;

const getFilenameFromUrl = (url: string) => {
  const match = url.match(/filename=([^&]+)/);
  return match ? decodeURIComponent(match[1]) : null;
};

const initSignatureData = {
  boxsize: {
    x: null,
    y: null,
  },
  imgbase64: '',
  offset: {
    x: null,
    y: null,
  },
  trangky: null,
};

const PdfViewer = () => {
  const params = useRoute<RouteProp<MainStackParamList, 'PdfViewer'>>().params;
  const {url, title, uploadParams, signStatus} = params;

  const navigation = useNavigation();
  const [webViewLoading, setWebViewLoading] = useState(true);
  const deleteRef = React.useRef(params.deleteFunction);
  const originFileName = getFilenameFromUrl(url ?? '');
  const queryClient = useQueryClient();
  const {user} = useAuthStore();
  const [sizeDialogVisible, setSizeDialogVisible] = useState(false);
  const [fileDownloaded, setFileDownloaded] = useState(false);
  const [pdfEditMode, setPdfEditMode] = useState(false);
  const [pathHistory, setPathHistory] = useState<string[]>([]);
  const [reloadKey, setReloadKey] = useState(0);
  const [signatureType, setSignatureType] = useState(SIGNATURE_TYPE.MAIN);
  const [SIMSignLoading, setSIMSignLoading] = useState(false);
  const [saveDocLoading, setSaveDocLoading] = useState(false);
  const [simSignSuccess, setSIMSignSuccess] = useState(false);
  const [webConfirmSign, setWebConfirmSign] = useState(false);
  const [simCertData, setSimCertData] = useState<any>({cert: null});
  const [signatureData, setSignatureData] = useState<any>(initSignatureData);
  const [webViewKey, setWebViewKey] = useState(0);
  const [signatureSize, setSignatureSize] = useState({
    sizeWidth: signatureWidth,
    sizeHeight: signatureHeight,
  });
  const [tempSize, setTempSize] = useState({
    width: signatureSize.sizeWidth,
    height: signatureSize.sizeHeight,
  });

  useEffect(() => {
    setTempSize({
      width: signatureSize.sizeWidth,
      height: signatureSize.sizeHeight,
    });
  }, [signatureSize]);
  const {data: simCertDataRes} = useSimCert({
    simId: user?.metadata?.phone?.replace(/[^0-9]/g, '')?.startsWith('0')
      ? `84${user?.metadata?.phone?.replace(/[^0-9]/g, '')?.substring(1)}`
      : user?.metadata?.phone?.replace(/[^0-9]/g, ''),
  });

  useEffect(() => {
    if (simCertDataRes?.data?.data?.x509Certificate) {
      const decodedCert = atob(simCertDataRes?.data?.data?.x509Certificate);
      setSimCertData({
        cert: decodedCert,
      });
    }
    console.log(user?.metadata.phone);
  }, [simCertDataRes, user]);

  const {data: signatureMainData, isLoading: signatureMainLoading} =
    useCongchucAPIGetSignBase64({
      loaiChuKy: 1,
      existToast: false,
    });
  const {data: signatureDotData, isLoading: signatureDotLoading} =
    useCongchucAPIGetSignBase64({
      loaiChuKy: 2,
      existToast: false,
    });

  const theme = useTheme();
  const webViewRef = React.useRef<WebView | null>(null);
  useEffect(() => {
    if (pdfEditMode === false) {
      setSignatureData(initSignatureData);
    }
  }, [pdfEditMode]);

  const downloadFile = React.useCallback(() => {
    if (!fileDownloaded) {
      console.log('pdf url: ', url);
      try {
        RNFS.downloadFile({
          fromUrl: url,
          toFile: `${RNFS.CachesDirectoryPath}/temp_${originFileName}`,
        }).promise.then(async () => {
          // Read the downloaded file
          const contents = await RNFS.readFile(
            `${RNFS.CachesDirectoryPath}/temp_${originFileName}`,
            'base64',
          );
          const arrayBuffer = _base64ToArrayBuffer(contents);

          const pdfDoc = await PDFDocument.load(arrayBuffer);
          const pdfBytes = await pdfDoc.save();
          const pdfBase64 = _uint8ToBase64(pdfBytes);

          await RNFS.writeFile(
            `${RNFS.CachesDirectoryPath}/${originFileName}`,
            pdfBase64,
            'base64',
          );
          setPathHistory([`${RNFS.CachesDirectoryPath}/${originFileName}`]);
          setFileDownloaded(true);
        });
      } catch (error) {
        console.log(error, 'error', url);
      }
    }
  }, [fileDownloaded, originFileName, url]);

  useEffect(() => {
    downloadFile();
  }, [downloadFile]);

  const signSIM = useCallback(async () => {
    try {
      if (user && simCertData?.cert && params.url) {
        let imageString = null;
        if (signatureType === SIGNATURE_TYPE.MAIN) {
          imageString = signatureMainData;
        } else {
          imageString = signatureDotData;
        }
        if (imageString === null) {
          setSIMSignLoading(false);
          return;
        }

        const simPKiParam = {
          signProfile: {
            simId: user?.metadata?.phone
              ?.replace(/[^0-9]/g, '')
              ?.startsWith('0')
              ? `84${user?.metadata?.phone
                  ?.replace(/[^0-9]/g, '')
                  ?.substring(1)}`
              : user?.metadata?.phone?.replace(/[^0-9]/g, ''),
            signerCertificate: simCertData?.cert,
            reason: 'Ky tai lieu boi app cong chuc',
            textContent: '',
            visibleSignature: {
              offset: `${signatureData.offset.x},${signatureData.offset.y}`,
              boxSize: `${signatureData.boxsize.x},${signatureData.boxsize.y}`,
              requiredText: '',
              imageData: signatureData.imgbase64 || '',
              page: signatureData.trangky,
            },
            checkText: false,
            checkMark: false,
          },
          fileName: `SIGNED_BY_SIM_${params.title}`,
          fileUrl: params.url,
        };

        const file = {
          uri: `file://${pathHistory[pathHistory.length - 1]}`,
          type: 'application/pdf',
          name: `DaKySIM_${params.title}`,
        } as DocumentPickerResponse;

        const rs = await simPKiFile({...simPKiParam, file});
        return rs;
      }
    } catch (error) {
      console.log(error, 'error at call back sign sim');
      throw error;
    }
  }, [
    params,
    pathHistory,
    signatureData,
    signatureDotData,
    signatureMainData,
    signatureType,
    simCertData,
    user,
  ]);

  const onWebViewMessage = async (event: any) => {
    var message = event.nativeEvent.data;

    var command = JSON.parse(message);

    if (command?.command === 'onload') {
      const base64PDF = await RNFS.readFile(
        `${RNFS.CachesDirectoryPath}/temp_${originFileName}`,
        'base64',
      );
      let imageString = null;
      if (signatureType === SIGNATURE_TYPE.MAIN) {
        imageString = signatureMainData;
      } else {
        imageString = signatureDotData;
      }

      webViewRef.current?.injectJavaScript(
        `window.locadFilePdf("data:application/pdf;base64,${base64PDF}","data:image/png;base64,${imageString}",${signatureSize.sizeWidth})`,
      );

      return;
    }
    if (command?.command === 'daky') {
      const {data} = command;
      setSignatureData(data);
      setWebConfirmSign(true);
    }
  };
  const confirmSIMSign = useCallback(async () => {
    setSIMSignLoading(true);
    const newSigned = await signSIM();
    if (!newSigned?.data) {
      setSIMSignLoading(false);
      Toast.show({
        type: 'error',
        text1: 'Không thể ký tài liệu',
      });
      setSIMSignLoading(false);
    }
    const response = newSigned?.data;

    const reader = new FileReader();
    reader.readAsDataURL(response);

    reader.onloadend = async () => {
      const base64data = reader.result?.toString().split(',')[1];
      if (base64data) {
        const tempFilePath = `${
          RNFS.CachesDirectoryPath
        }/temp_signedSIMX_${new Date().getTime()}.pdf`;
        await RNFS.writeFile(tempFilePath, base64data, 'base64');
        const uri = `file://${tempFilePath}`;

        setPathHistory([...pathHistory, uri]);
        setPdfEditMode(false);
        setSIMSignLoading(false);
        setSIMSignSuccess(true);
      }
    };
  }, [pathHistory, signSIM]);

  useEffect(() => {
    if (signatureData.imgbase64.length > 0 && webConfirmSign) {
      confirmSIMSign();
      setWebConfirmSign(false);
    }
  }, [confirmSIMSign, signatureData, webConfirmSign]);

  const saveOnPress = React.useCallback(async () => {
    setSaveDocLoading(true);
    const file = {
      uri: `file://${pathHistory[pathHistory.length - 1]}`,
      type: 'application/pdf',
      name: `DaKySo_${params.title}`,
    };

    const uploadFileParams = {
      file: file as DocumentPickerResponse,
      DonViXuLyID: user?.donViID,
      HoSoID: uploadParams?.HoSoID,
      NguoiXuLyHoSoHienTaiID: uploadParams?.NguoiXuLyHoSoHienTaiID,
      NguoiXuLyID: uploadParams?.NguoiXuLyID,
      QuaTrinhXuLyID: uploadParams?.QuaTrinhXuLyID,
    };
    try {
      await handleFunction.UploadFile(uploadFileParams);
      Toast.show({
        type: 'success',
        text1: 'Ký số tài liệu thành công',
      });
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Không thể tải lên tài liệu',
      });
      setSaveDocLoading(false);
    }

    setSaveDocLoading(false);
    if (deleteRef.current) {
      setSaveDocLoading(true);
      Toast.show({
        type: 'info',
        text1: 'Đang cập nhật tài liệu',
      });
      await deleteRef.current();

      await queryClient.invalidateQueries(['useDocumentDetail']);
      await queryClient.invalidateQueries(['useNoiDungGiayTo']);
      await queryClient.invalidateQueries(['useDanhSachNoiDungGiayTo']);
      Toast.show({
        type: 'info',
        text1: 'Cập nhật tài liệu thành công',
      });
      setSaveDocLoading(false);
    }
    navigation.goBack();
  }, [navigation, params.title, pathHistory, queryClient, uploadParams, user]);

  useEffect(() => {
    if (simSignSuccess === true) {
      saveOnPress();
    }
  }, [simSignSuccess, saveOnPress]);

  return (
    <View className="flex-1">
      <CCAppBar
        label={
          pdfEditMode
            ? `Chế độ ký tài liệu: ${
                signatureType === SIGNATURE_TYPE.MAIN
                  ? 'Chữ ký chính'
                  : 'Chữ ký nháy'
              }`
            : title
            ? title
            : 'Chi tiết tài liệu'
        }
        backgroundColor={
          pdfEditMode ? theme.colors.secondaryContainer : undefined
        }
        isBack={saveDocLoading ? false : true}
        iconsAction={
          // newPdfSaved &&
          !saveDocLoading && pathHistory.length > 1 && simSignSuccess
            ? [
                {
                  icon: () => <SaveIcon color={theme.colors.primary} />,
                  onPress: saveOnPress,
                },
              ]
            : []
        }
      />

      {pdfEditMode && (
        <View style={{flex: 1}}>
          <WebView
            source={{
              html: htmlContent,
              // uri: 'https://dl-storage.uat.lokify.xplat.online/ihanoi-mini-apps-staff/esignweb/pdfhtmlview.html',
            }}
            key={webViewKey}
            allowFileAccess={true}
            ref={webViewRef}
            style={styles.container}
            cacheEnabled={false}
            cacheMode="LOAD_NO_CACHE"
            onLoadStart={() => setWebViewLoading(true)}
            onLoadEnd={() => setWebViewLoading(false)}
            javaScriptEnabled={true}
            onMessage={onWebViewMessage}
          />
          {webViewLoading && (
            <View
              style={[
                StyleSheet.absoluteFill,
                {
                  backgroundColor: 'rgba(255,255,255,0.8)',
                  justifyContent: 'center',
                  alignItems: 'center',
                },
              ]}>
              <ActivityIndicator size="large" color={theme.colors.primary} />
            </View>
          )}
        </View>
      )}
      {saveDocLoading && (
        <View
          style={{
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(255,255,255,0.9)',
          }}>
          <View
            style={{
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text
              style={[
                styles.buttonText,
                {marginTop: 16, color: theme.colors.primary},
              ]}>
              Đang cập nhật tài liệu
            </Text>
          </View>
        </View>
      )}
      {!pdfEditMode && !saveDocLoading && (
        <Pdf
          key={reloadKey}
          fitPolicy={0}
          enablePaging={true}
          trustAllCerts={false}
          source={{
            uri: pathHistory[pathHistory.length - 1] ?? undefined,
            cache: false,
          }}
          onError={error => console.log('PDF Error:', error)}
          onLoadProgress={percent => {
            console.log(percent, '%:percent');
          }}
          style={styles.pdf}
        />
      )}
      {signStatus && (
        <View
          style={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            flexDirection: 'row',
            padding: 16,
            gap: 8,
            justifyContent: 'flex-end',
            backgroundColor: theme.colors.background,
          }}>
          <View style={{flex: 1, flexDirection: 'column', gap: 8}}>
            {pdfEditMode && pathHistory.length === 1 && !SIMSignLoading && (
              <View style={{width: '100%', alignItems: 'center'}}>
                <Button onPress={() => setSizeDialogVisible(true)}>
                  Kích thước chiều ngang: {signatureSize.sizeWidth}
                  <EditIcon width={24} height={24} />
                </Button>
              </View>
            )}
            {SIMSignLoading && <ActivityIndicator />}
          </View>
          {!pdfEditMode ? (
            <>
              <Button
                mode="contained"
                onPress={async () => {
                  if (signatureMainLoading) return;
                  setSignatureType(SIGNATURE_TYPE.MAIN);
                  setPdfEditMode(!pdfEditMode);
                  const {width, height} = await _getImageDimensions(
                    signatureMainData,
                  );
                  setSignatureSize({
                    sizeWidth: width,
                    sizeHeight: height,
                  });
                  setReloadKey(prev => prev + 1);
                }}
                disabled={
                  signatureMainLoading ||
                  signatureMainData === null ||
                  simCertData?.cert === null
                }>
                Ký chính thức
              </Button>
              <Button
                mode="contained-tonal"
                onPress={async () => {
                  if (signatureDotLoading) return;
                  setSignatureType(SIGNATURE_TYPE.DOT);
                  setPdfEditMode(!pdfEditMode);
                  const {width, height} = await _getImageDimensions(
                    signatureDotData,
                  );
                  setSignatureSize({
                    sizeWidth: width,
                    sizeHeight: height,
                  });
                  setReloadKey(prev => prev + 1);
                }}
                disabled={
                  signatureDotLoading ||
                  signatureDotData === null ||
                  simCertData?.cert === null
                }>
                Ký nháy
              </Button>
            </>
          ) : (
            <IconButton
              icon="close"
              mode="contained-tonal"
              size={24}
              style={{
                backgroundColor: theme.colors.errorContainer,
                elevation: 2,
              }}
              iconColor={theme.colors.error}
              onPress={() => {
                setPdfEditMode(false);
                setPathHistory([pathHistory[0]]);
              }}
              disabled={signatureDotLoading || signatureDotData === null}
            />
          )}
        </View>
      )}

      <Portal>
        <Dialog
          visible={sizeDialogVisible}
          onDismiss={() => setSizeDialogVisible(false)}>
          <Dialog.Title>Điều chỉnh kích thước chữ ký</Dialog.Title>
          <Dialog.Content>
            <TextInput
              label="Chiều rộng"
              value={tempSize.width.toString()}
              keyboardType="numeric"
              onChangeText={text =>
                setTempSize(prev => ({...prev, width: parseInt(text) || 0}))
              }
              style={{marginBottom: 10}}
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setSizeDialogVisible(false)}>Hủy</Button>
            <Button
              onPress={() => {
                setSignatureSize({
                  sizeWidth: tempSize.width,
                  sizeHeight: tempSize.height,
                });
                setSignatureData(initSignatureData);
                setSizeDialogVisible(false);
                setWebViewKey(prev => prev + 1);
              }}>
              Xác nhận
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f4f4f4',
  },
  headerText: {
    color: '#508DBC',
    fontSize: 20,
    marginBottom: 20,
    alignSelf: 'center',
  },
  pdf: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height - 180,
  },
  button: {
    alignItems: 'center',
    backgroundColor: '#508DBC',
    padding: 10,
    marginVertical: 10,
  },
  buttonText: {
    color: '#DAFFFF',
  },
  message: {
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#FFF88C',
  },
});
export default PdfViewer;
