import React from 'react';
import ErrorBoundary from '../../components/ErrorBoundary';
import SplashScreen from '../../components/SplashScreen';
import {Button, Text} from 'react-native-paper';
import {View} from 'react-native';

const DashBoardTest2Screen = ({navigation}: any) => {
  return (
    <ErrorBoundary name="TestScreen">
      <React.Suspense fallback={<SplashScreen />}>
        <View className="flex-1 items-center p-4 gap-4">
          <Text>DashBoardTest2Screen</Text>
          <Button
            mode="contained"
            onPress={() => {
              navigation.navigate('PdfViewer');
            }}>
            Go to DashBoard PdfViewer
          </Button>
          <Button
            mode="contained"
            onPress={() => {
              navigation.navigate('WebView');
            }}>
            Go to DashBoard WebView
          </Button>
        </View>
      </React.Suspense>
    </ErrorBoundary>
  );
};

export default DashBoardTest2Screen;
