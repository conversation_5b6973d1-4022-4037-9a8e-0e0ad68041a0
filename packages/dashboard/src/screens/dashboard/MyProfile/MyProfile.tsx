import React from 'react';
import {ScrollView, View} from 'react-native';
import {AwardIcon, EmailICon, UnitIcon, UserIconProfile} from '../../../icons';
import {textStyles} from '../../../styles';
import {useAuthStore} from '@ac-mobile/common';
import {CCAppBar} from '../../../components/HeaderComponent/CCAppBar';
import {Divider, List} from 'react-native-paper';

export const MyProfile = () => {
  const {user} = useAuthStore();

  console.log('1123', user);

  return (
    <View className="flex-1">
      <CCAppBar label="Hồ sơ cá nhân" isBack />
      <ScrollView
        className="flex-1"
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{paddingBottom: 16}}>
        <View className="bg-white rounded-lg mt-2.5 mx-4">
          <List.Section>
            <List.Subheader className={`${textStyles.text_17px_bold} p-4`}>
              Thông tin cá nhân
            </List.Subheader>
            <List.Item
              left={props => (
                <UserIconProfile {...props} width={25} height={25} />
              )}
              title={user?.hoTen}
              description="Họ tên"
            />
            <List.Item
              left={props => <EmailICon {...props} width={25} height={25} />}
              title={user?.metadata?.email || 'Chưa cập nhật'}
              description="Email"
            />
            <List.Item
              left={props => <UnitIcon {...props} width={25} height={25} />}
              title={user?.tenDonVi || 'Chưa cập nhật'}
              description="Đơn vị/phòng ban"
            />
          </List.Section>
          <Divider />
          <List.Section>
            <List.Subheader className={`${textStyles.text_17px_bold} p-4`}>
              Danh sách chức năng
            </List.Subheader>
            {user?.metadata?.chucNangHienTai.map((item: any, index: number) => (
              <List.Item key={index} title={item.ten} description={item.ma} />
            ))}
          </List.Section>
        </View>
      </ScrollView>
    </View>
  );
};
