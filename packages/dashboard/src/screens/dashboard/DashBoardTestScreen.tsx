import React from 'react';
import ErrorBoundary from '../../components/ErrorBoundary';
import SplashScreen from '../../components/SplashScreen';
import {Button, Text} from 'react-native-paper';
import {View} from 'react-native';

const DashBoardTestScreen = ({navigation}: any) => {
  return (
    <ErrorBoundary name="TestScreen">
      <React.Suspense fallback={<SplashScreen />}>
        <View className="flex-1 items-center p-4 gap-4">
          <Text>DashBoardTestScreen</Text>
          <View>
            <Button
              mode="contained"
              onPress={() => {
                navigation.navigate('Test1Screen');
              }}>
              Go to DashBoard Test1Screen
            </Button>
          </View>
        </View>
      </React.Suspense>
    </ErrorBoundary>
  );
};

export default DashBoardTestScreen;
