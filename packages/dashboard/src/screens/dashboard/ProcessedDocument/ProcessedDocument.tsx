import {<PERSON>List, RefreshControl, Text, View} from 'react-native';
import React, {useCallback, useEffect} from 'react';

import {SearchIcon} from '../../../icons/SearchIcon';
import {CardComponent} from '../../../components/CardComponent/CardComponent';
import {Folder, SendIcon} from '../../../icons';
import {IDanhSachHoSo, useDanhSachHoSo} from '../../../stores';
import {formatDate} from '../ProcesseDocument/NotReceivedComponent/NotReceivedComponent';
import {useAuthStore} from '@ac-mobile/common';
import {useNavigation} from '@react-navigation/native';
import {ActivityIndicator} from 'react-native';
import {CCAppBar} from '../../../components/HeaderComponent/CCAppBar';
const IconSearch = () => {
  return (
    // eslint-disable-next-line react-native/no-inline-styles
    <View style={{position: 'relative', left: -10, top: -10}}>
      <SearchIcon width={40} height={40} />
    </View>
  );
};
const ProcessedDocument = () => {
  const navigation = useNavigation<any>();
  const {
    dsHosoDaXuLy,
    handleGetDanhSachHoSo,
    loadMore,
    hasMore,
    // handleLoadMoreDanhSachHoSo,
    // handleRefreshDanhSachHoSo,
    refresh,
    filter,
    isLoading,
  } = useDanhSachHoSo();

  // const {handleGetHoSoKhongPheDuyet, hoSoKhongPheDuyet} =
  //   HoSoKhongPheDuyetStore();
  // const isFocused = useIsFocused();
  const user = useAuthStore();
  // const handleEndReached = useCallback(() => {
  //   if (hasMore) {
  //     handleLoadMoreDanhSachHoSo({
  //       donviID: user.user?.donViID ?? 0,
  //       phongbanID: user.user?.phongBanID ?? 0,
  //       userID: user.user?.user_PortalID ?? 0,
  //       chucNangHienTai: user.user
  //         ? user.user.metadata.chucNangHienTai
  //             .map((item: any) => item.ma)
  //             .join(',')
  //         : '',
  //     });
  //   }
  // }, [isFocused, loadMore, hasMore, user.user]);

  const renderItem = useCallback(({item}: {item: IDanhSachHoSo}) => {
    const Icons = <Folder width={32} height={32} />;
    return (
      <CardComponent
        Icons={Icons}
        // key={item}
        headerCard={{label: 'Mã hồ sơ', value: item.soBienNhan?.toString()}}
        bodyCard={[
          {
            label: 'Ngày nhận:',
            value: ` ${
              item.ngayNhan && formatDate(new Date(item.ngayNhan?.toString()))
            }`,
          },
          {label: 'Người đứng tên: ', value: item?.nguoiDungTenHoSo},
          {
            label: 'Thủ tục: ',
            value: item?.tenThuTuc,
          },
        ]}
        // timeHandle={item?.lanhDaoXuLy == 1 && ""}
        status={item?.lanhDaoXuLy == 1 ? 'Đã chuyển văn thư' : undefined}
        buttons={
          item?.lanhDaoXuLy == 2
            ? [
                {
                  label: 'Chuyển văn thư',
                  press: () => {
                    handleGetHoSoKhongPheDuyet({
                      DonViID: user?.user?.donViID && user?.user?.donViID,
                      HoSoID: item?.hoSoID,
                      LoaiXuLy: `${item?.loaiXuLy}`,
                      QuaTrinhXuLyID: item?.quaTrinhXuLyHienTaiID,
                    });
                    navigation.navigate('TransDocument', {
                      item: {...item},
                    });
                  },
                  className:
                    'w-[171px] h-[36px] bg-[#366AE2] justify-center items-center flex-row space-x-2',
                  type: 'RIGHT',
                  IconsRightButton: <SendIcon width={25} height={25} />,
                },
              ]
            : undefined
        }
        onPressItem={() => {
          navigation.navigate('ProcessDocumentDetail', {
            DocId: item?.hoSoID,
            handle: item?.lanhDaoXuLy && item?.lanhDaoXuLy.toString(),
            ChucNangKeTiep: '',
            ChucNangHienTai: item?.chucNangHienTai,
            LoaiXuLy: item?.loaiXuLy,
            UserID: user?.user?.user_PortalID,
            DonViID: user?.user?.donViID,
            DonViXuLyID: item?.donViXuLyID
              ? Number.parseInt(item?.donViXuLyID?.toString())
              : 0,
            HoSoID: item?.hoSoID,
            LinhVucID: item?.linhVucID,
            NguoiXuLyID: item?.nguoiXuLyID
              ? Number.parseInt(item?.nguoiXuLyID?.toString())
              : 0,
            PhongBanXuLyID: item?.phongBanXuLyID
              ? Number.parseInt(item?.phongBanXuLyID?.toString())
              : 0,
            QuaTrinhXuLyID: item?.quaTrinhXuLyHienTaiID,
            ThuTucHanhChinhID: item?.thuTucHanhChinhID,
          });
        }}
      />
    );
  }, []);
  useEffect(() => {
    handleGetDanhSachHoSo({
      donviID: user.user ? user.user.donViID : 0,
      phongbanID: user.user ? user.user.phongBanID : 0,
      userID: user.user ? user.user.user_PortalID : 0,
      chucNangHienTai: user.user
        ? user.user.metadata.chucNangHienTai
            .map((item: any) => item.ma)
            .join(',')
        : '',
    });
  }, [filter, handleGetDanhSachHoSo, user.user]);
  return !isLoading ? (
    <>
      <View className="flex-1">
        <CCAppBar
          label="Hồ sơ đã xử lý"
          iconsAction={[{icon: IconSearch, onPress: () => {}}]}
        />
        <FlatList
          className="px-2"
          showsVerticalScrollIndicator={false}
          // keyExtractor={keyExtractor}
          data={dsHosoDaXuLy ? dsHosoDaXuLy : []}
          refreshControl={
            <RefreshControl
              colors={['#9Bd35A', '#689F38']}
              refreshing={refresh}
              // onRefresh={
              //   isFocused
              //     ? () =>
              //         handleRefreshDanhSachHoSo({
              //           donviID: user.user ? user.user.donViID : 0,
              //           phongbanID: user.user ? user.user.phongBanID : 0,
              //           userID: user.user ? user.user.user_PortalID : 0,
              //           chucNangHienTai: user.user
              //             ? user.user.metadata.chucNangHienTai
              //                 .map((item: any) => item.ma)
              //                 .join(',')
              //             : '',
              //         })
              //     : undefined
              // }
            />
          }
          ListEmptyComponent={
            <View className="flex-1 items-center justify-center">
              <Text>Không có data</Text>
            </View>
          }
          onEndReachedThreshold={0.5}
          contentContainerStyle={{
            flexGrow: 1,
          }}
          // onEndReached={handleEndReached}
          ListFooterComponent={
            loadMore && hasMore ? (
              <View className="my-1">
                <ActivityIndicator />
              </View>
            ) : null
          }
          renderItem={renderItem}
        />
      </View>
    </>
  ) : (
    <>
      <View className="flex-1 items-center justify-center">
        <ActivityIndicator size="large" />
      </View>
    </>
  );
};

export default ProcessedDocument;
