import {Federated} from '@callstack/repack/client';
import React from 'react';
import ErrorBoundary from '../../components/ErrorBoundary';
import SplashScreen from '../../components/SplashScreen';
import {MyAccountScreen} from './MyAccount/MyAccountScreen';

const Account = React.lazy(() =>
  Federated.importModule('auth', './AccountScreen'),
);

const DashboardAccountScreen = ({navigation}: any) => {
  React.useEffect(() => {
    navigation.setOptions({title: 'T<PERSON><PERSON> khoản'});
  }, [navigation]);

  return (
    <ErrorBoundary name="DashboardAccountScreen">
      <React.Suspense fallback={<SplashScreen />}>
        <Account />
      </React.Suspense>
    </ErrorBoundary>
  );
};

export default DashboardAccountScreen;
