import {IFilterType} from '../../../stores';

export const viVN = {
  save: '<PERSON><PERSON><PERSON> nhận',
  selectSingle: 'Chọn ngày',
  selectMultiple: 'Chọn nhiều ngày',
  selectRange: 'Chọn khoảng',
  notAccordingToDateFormat: (inputFormat: any) =>
    `Định dạng ngày phải là ${inputFormat}`,
  mustBeHigherThan: (date: any) => `Phải sau ngày ${date}`,
  mustBeLowerThan: (date: any) => `Phải trước ngày ${date}`,
  mustBeBetween: (startDate: any, endDate: any) =>
    `Phải trong khoảng ${startDate} - ${endDate}`,
  dateIsDisabled: '<PERSON><PERSON><PERSON> không hợp lệ',
  previous: 'Trước',
  next: 'Sau',
  typeInDate: 'Nhập ngày',
  pickDateFromCalendar: '<PERSON><PERSON><PERSON> từ lịch',
  close: 'Đóng',
};

export const defaultValueFilter: IFilterType = {
  keyword: '',
  linhVuc: [],
  fromDate: undefined,
  toDate: undefined,
  OrderBy: {label: 'Ngày gần nhất', value: 'desc'},
  isQuaHan: false,
};

export const normalizeText = (text: string) => {
  return text
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '');
};
