import React, {use<PERSON><PERSON>back, useMemo, useRef, useState} from 'react';
import {
  Keyboard,
  LayoutAnimation,
  Platform,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import RowComponent from '../../../components/RowComponent/RowComponent';
import {useDanhSachHoSo, IFilterType} from '../../../stores';
import {useNavigation} from '@react-navigation/native';
import {CCAppBar} from '../../../components/HeaderComponent/CCAppBar';
import {
  Button,
  Divider,
  IconButton,
  List,
  Switch,
  useTheme,
  Chip,
  Menu,
  ActivityIndicator,
  MD3Theme,
} from 'react-native-paper';
import {
  BottomSheetBackdrop,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetSectionList,
  BottomSheetTextInput,
} from '@gorhom/bottom-sheet';
import {BottomSheetDefaultBackdropProps} from '@gorhom/bottom-sheet/lib/typescript/components/bottomSheetBackdrop/types';
import {useBottomSheetBackHandler} from './useBottomSheetBackHandler';
import {
  DatePickerModal,
  enGB,
  registerTranslation,
} from 'react-native-paper-dates';
import {useQueryClient} from 'react-query';
import {defaultValueFilter, normalizeText, viVN} from './searchHelperfn';
import {
  danhMucType,
  UseCongChucAPIDanhMucLinhVuc,
} from '../../../useQueryState/UseCongChucAPIDanhMucLinhVuc';
import {AcTextInput} from '../../../components/AcTextInput/AcTextInput';
import {renderSelected} from '../../../components/AcBottomSheet/BottomSheetHelperFn';

registerTranslation('vi', {
  ...viVN,
  hour: 'Giờ',
  minute: 'Phút',
});
registerTranslation('en-GB', enGB);

const RenderListItem = ({
  item,
  find,
  setFind,
  styles,
}: {
  item: any;
  find: IFilterType;
  setFind: (value: IFilterType) => void;
  styles: any;
}) => {
  const isSelected =
    find.linhVuc?.some(
      (selected: any) => selected.linhVucID === item.linhVucID,
    ) || false;

  return (
    <List.Item
      title={item.tenLinhVuc}
      titleNumberOfLines={0}
      titleStyle={styles.flexWrap}
      onPress={() => {
        if (isSelected) {
          setFind({
            ...find,
            linhVuc: find.linhVuc.filter(
              (i: {linhVucID: any}) => i.linhVucID !== item.linhVucID,
            ),
          });
        } else {
          setFind({...find, linhVuc: [...find.linhVuc, item]});
        }
      }}
      right={props =>
        renderSelected({
          isSelected,
          ...props,
        })
      }
    />
  );
};

export const Search = () => {
  const {handleSetFilter, filter} = useDanhSachHoSo();
  const theme = useTheme();
  const styles = React.useMemo(() => themedStyles(theme), [theme]);
  const nav = useNavigation();
  const bottomSheetRef = useRef<BottomSheetModal>(null);
  const {handleSheetPositionChange} = useBottomSheetBackHandler(bottomSheetRef);
  const snapPoints = useMemo(() => ['50%', '90%'], []);
  const [openDateRangePicker, setOpenDateRangePicker] = React.useState(false);
  const queryClient = useQueryClient();
  const [visible, setVisible] = useState(false);
  const {data: danhMucLinhVuc, isLoading: danhMucLinhVucLoading} =
    UseCongChucAPIDanhMucLinhVuc();
  const danhMucList = danhMucLinhVuc?.data?.data || [];
  const [find, setFind] = useState<IFilterType>(filter);

  const [searchField, setSearchField] = useState('');

  const handleFilter = async () => {
    setFind({...find});
    const filterX: IFilterType = {
      linhVuc: find.linhVuc,
      keyword: find.keyword,
      isQuaHan: find.isQuaHan,
      OrderBy: find.OrderBy,
      fromDate: find.fromDate,
      toDate: find.toDate,
    };

    handleSetFilter(filterX);
    queryClient.invalidateQueries(['daNhan']);
    queryClient.invalidateQueries(['chuaNhan']);
    nav.goBack();
  };

  const openBottomSheet = () => {
    bottomSheetRef.current?.present();
  };

  const renderItem = useCallback(
    ({item}: {item: any}) => {
      return (
        <RenderListItem
          item={item}
          find={find}
          setFind={setFind}
          styles={styles}
        />
      );
    },
    [find, styles],
  );

  const renderBackdrop = useCallback(
    (
      props: React.JSX.IntrinsicAttributes & BottomSheetDefaultBackdropProps,
    ) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
      />
    ),
    [],
  );

  return (
    <View style={styles.container}>
      <CCAppBar isBack label="Tìm kiếm" />
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}>
        <View style={styles.contentContainer}>
          <RowComponent>
            <AcTextInput
              label={'Mã hồ sơ/người đứng tên/tên thủ tục'}
              mode="outlined"
              value={find.keyword}
              onChangeText={e => setFind({...find, keyword: e})}
              style={styles.input}
            />
          </RowComponent>
          <RowComponent>
            <View style={styles.dateContainer}>
              <View style={styles.dateRow}>
                <View style={styles.dateColumn}>
                  <Text style={styles.labelText}>
                    Từ ngày: {find.fromDate?.toLocaleDateString() || '____'}
                  </Text>
                </View>
                <View style={styles.dateColumn}>
                  <Text style={styles.labelText}>
                    Đến ngày: {find.toDate?.toLocaleDateString() || '____'}
                  </Text>
                </View>
                <View>
                  <Chip
                    icon={'calendar-range'}
                    style={styles.primaryChip}
                    textStyle={styles.primaryChipText}
                    onPress={() => setOpenDateRangePicker(true)}>
                    Chọn
                  </Chip>
                </View>
              </View>
            </View>
          </RowComponent>
          <Divider />
          <RowComponent>
            <View style={styles.dateColumn}>
              <Text style={styles.labelText}>Sắp xếp theo ngày</Text>
              <Menu
                visible={visible}
                onDismiss={() => setVisible(false)}
                anchor={
                  <Chip
                    style={styles.primaryChip}
                    textStyle={styles.primaryChipText}
                    onPress={() => setVisible(true)}>
                    {find.OrderBy.value === 'asc' ? 'xa nhất' : 'gần nhất'}
                  </Chip>
                }>
                <Menu.Item
                  onPress={() => {
                    setFind({
                      ...find,
                      OrderBy: {label: 'Ngày gần nhất', value: 'desc'},
                    });
                    setVisible(false);
                  }}
                  title="Gần nhất"
                />
                <Menu.Item
                  onPress={() => {
                    setFind({
                      ...find,
                      OrderBy: {label: 'Ngày xa nhất', value: 'asc'},
                    });
                    setVisible(false);
                  }}
                  title="Xa nhất"
                />
              </Menu>
            </View>
          </RowComponent>
          <Divider />
          <RowComponent>
            <View style={styles.rowBetween}>
              <Text style={styles.labelText}>Hồ sơ hết hạn</Text>
              <Switch
                value={find.isQuaHan}
                onValueChange={() => {
                  setFind({...find, isQuaHan: !find.isQuaHan});
                  LayoutAnimation.configureNext(LayoutAnimation.Presets.spring);
                }}
              />
            </View>
          </RowComponent>
          <Divider />
          <RowComponent>
            <Pressable>
              <Text
                style={styles.labelText}
                onPress={() => bottomSheetRef.current?.present()}>
                Lĩnh vực:
              </Text>
            </Pressable>
            {find.linhVuc && find.linhVuc.length > 0 ? (
              <View className="flex-row flex-wrap gap-2">
                {find.linhVuc.map((item: danhMucType) => (
                  <Chip
                    key={`${item.linhVucID}`}
                    onClose={() => {
                      setFind({
                        ...find,
                        linhVuc: find.linhVuc.filter(
                          (i: {linhVucID: number}) =>
                            i.linhVucID !== item.linhVucID,
                        ),
                      });
                    }}
                    style={styles.primaryChip}
                    textStyle={styles.primaryChipText}>
                    {item.tenLinhVuc}
                  </Chip>
                ))}
              </View>
            ) : (
              <></>
            )}
            <View style={styles.alignEnd}>
              {danhMucLinhVucLoading ? (
                <ActivityIndicator />
              ) : (
                <IconButton
                  mode="contained"
                  icon={'plus'}
                  onPress={openBottomSheet}
                />
              )}
            </View>
          </RowComponent>
        </View>
      </ScrollView>
      <View style={styles.bottomBar}>
        <Button
          mode="outlined"
          icon={'close'}
          onPress={() => {
            setFind(defaultValueFilter);
          }}
          style={{...styles.flex1, borderRadius: 8}}>
          Xoá tất cả
        </Button>
        <Button
          mode="contained"
          onPress={handleFilter}
          style={{...styles.flex1, borderRadius: 8}}>
          Áp dụng
        </Button>
      </View>
      <DatePickerModal
        locale="vi"
        mode="range"
        visible={openDateRangePicker}
        onDismiss={() => setOpenDateRangePicker(false)}
        startDate={find.fromDate}
        endDate={find.toDate}
        presentationStyle={'overFullScreen'}
        onConfirm={({startDate, endDate}) => {
          setFind({
            ...find,
            fromDate: startDate,
            toDate: endDate,
          });
          setOpenDateRangePicker(false);
        }}
        saveLabel="Xác nhận"
        saveLabelDisabled={false}
        uppercase={false}
      />

      <BottomSheetModalProvider>
        <BottomSheetModal
          ref={bottomSheetRef}
          index={0}
          snapPoints={snapPoints}
          enablePanDownToClose
          backdropComponent={renderBackdrop}
          onChange={handleSheetPositionChange}
          android_keyboardInputMode="adjustResize"
          keyboardBehavior={Platform.OS === 'ios' ? 'extend' : 'interactive'}
          keyboardBlurBehavior="restore"
          onDismiss={() => {
            bottomSheetRef.current?.close();
            Keyboard.dismiss();
            setSearchField('');
          }}>
          <View style={styles.bottomSheetContainer}>
            <BottomSheetSectionList
              data={
                danhMucList
                  ? danhMucList.filter((item: {tenLinhVuc: string}) =>
                      normalizeText(item.tenLinhVuc).includes(
                        normalizeText(searchField),
                      ),
                    )
                  : []
              }
              renderSectionHeader={() => {
                return (
                  <View
                    style={[
                      styles.flex1,
                      {backgroundColor: theme.colors.background},
                    ]}>
                    <Text style={styles.bottomSheetTitle}>Chọn lĩnh vực</Text>

                    <BottomSheetTextInput
                      placeholder="Tìm kiếm lĩnh vực..."
                      placeholderTextColor={theme.colors.outline}
                      value={searchField}
                      onChangeText={setSearchField}
                      style={styles.bottomSheetInput}
                      onFocus={() => bottomSheetRef.current?.snapToIndex(1)}
                    />
                  </View>
                );
              }}
              ItemSeparatorComponent={Divider}
              renderItem={renderItem}
              style={styles.flex1}
              keyExtractor={item => item.linhVucID?.toString()}
              bounces={false}
              sections={[
                {
                  title: 'Lĩnh vực',
                  data:
                    searchField.trim() !== ''
                      ? danhMucList.filter((item: {tenLinhVuc: string}) =>
                          normalizeText(item.tenLinhVuc).includes(
                            normalizeText(searchField),
                          ),
                        )
                      : danhMucList,
                },
              ]}
            />
            <Button
              className="my-4"
              mode="contained"
              onPress={() => bottomSheetRef.current?.close()}>
              Xác nhận
            </Button>
          </View>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </View>
  );
};
const themedStyles = (theme: MD3Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollView: {
      flex: 1,
    },
    scrollViewContent: {
      flexGrow: 1,
      paddingBottom: 70,
    },
    contentContainer: {
      paddingHorizontal: 16,
      gap: 10,
    },
    input: {
      marginTop: 4,
    },
    dateContainer: {
      flexDirection: 'column',
      alignItems: 'center',
      width: '100%',
      gap: 4,
    },
    dateRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    dateColumn: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    labelText: {
      fontSize: 14,
      color: theme.colors.outline,
      marginBottom: 4,
    },
    primaryChip: {
      backgroundColor: theme.colors.primaryContainer,
    },
    primaryChipText: {
      color: theme.colors.primary,
    },
    bottomBar: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      flexDirection: 'row',
      padding: 8,
      backgroundColor: theme.colors.background,
      gap: 8,
    },
    rowBetween: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    alignEnd: {
      flex: 1,
      alignItems: 'flex-end',
    },
    flex1: {
      flex: 1,
    },
    bottomSheetContainer: {
      backgroundColor: theme.colors.background,
      flex: 1,
      padding: 16,
    },
    bottomSheetTitle: {
      fontSize: 20,
      marginTop: 10,
      fontWeight: '600',
      textAlign: 'center',
      color: theme.colors.onBackground,
      marginBottom: 16,
    },
    bottomSheetInput: {
      padding: 16,
      borderWidth: 1,
      borderColor: theme.colors.outline,
      borderRadius: 8,
      marginBottom: 8,
      color: theme.colors.onBackground,
    },
    flexWrap: {
      flexWrap: 'wrap',
    },
  });
