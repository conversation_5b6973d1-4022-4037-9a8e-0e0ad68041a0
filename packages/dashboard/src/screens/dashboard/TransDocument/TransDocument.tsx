import {useAuthStore} from '@ac-mobile/common';
import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {Text, View, StyleSheet, ScrollView} from 'react-native';
import {RowComponent} from '../../../components/RowComponent/RowComponent';
import {ArrowSend} from '../../../icons';
import {IDanhSachHoSo} from '../../../stores';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {MainStackParamList} from '../../../navigation/MainNavigator';
import {handleFunction} from '../../../api/dashboard-api';
import Toast from 'react-native-toast-message';
import {CCAppBar} from '../../../components/HeaderComponent/CCAppBar';
import {
  Button,
  Divider,
  IconButton,
  List,
  MD3Theme,
  useTheme,
} from 'react-native-paper';
import {
  BottomSheetBackdrop,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetSectionList,
  BottomSheetTextInput,
} from '@gorhom/bottom-sheet';
import {Chip} from 'react-native-paper';
import {BottomSheetDefaultBackdropProps} from '@gorhom/bottom-sheet/lib/typescript/components/bottomSheetBackdrop/types';
import {useNguoiNhanChuyenHoSo} from '../../../useQueryState/UseNguoiNhanChuyenHoSo';
import {useBottomSheetBackHandler} from '../Search/useBottomSheetBackHandler';
import {useMutation, useQueryClient} from 'react-query';
import {AcTextInput} from '../../../components/AcTextInput/AcTextInput';

const prepareChuyenHoSoBody = ({
  item,
  loaiXuLy,
  user,
  listNguoiNhan,
}: {
  item: IDanhSachHoSo;
  loaiXuLy: number;
  user: any;
  listNguoiNhan: any[];
}) => {
  const chuyenHoSoBody = {
    hoSo: {
      hoSoID: item?.hoSoID,
      quaTrinhXuLyID: item?.quaTrinhXuLyHienTaiID,
      nguoiXuLyID: item?.nguoiXuLyID ? Number.parseInt(item?.nguoiXuLyID) : 0,
      phongBanXuLyID: item?.phongBanXuLyID
        ? Number.parseInt(item?.phongBanXuLyID)
        : 0,
      donViXuLyID: user.user?.donViID,
      phongBanChuyenID: item?.phongBanXuLyID
        ? Number.parseInt(item?.phongBanXuLyID)
        : 0,
      donViChuyenID: user.user?.donViID,
      donViNhanID: 0,
      loaiXuLy,
      strDuongDiTinhTrang: item?.duongDiHoSo,
      listNguoiNhan: listNguoiNhan,
    },
  };
  return chuyenHoSoBody;
};

const TransDocument = () => {
  const bottomSheetRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ['45%', '50%', '95%'], []);
  const queryClient = useQueryClient();
  // const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [searchField, setSearchField] = useState('');
  const [text, setText] = useState('');
  const nav = useNavigation<any>();
  const user = useAuthStore();
  const theme = useTheme();
  const {item, approvePayload, rejectPayload} =
    useRoute<RouteProp<MainStackParamList, 'TransDocument'>>().params;
  const [pick, setPick] = useState<any[]>([]);
  const {data: dsNguoiNhanChuyenHoSo} = useNguoiNhanChuyenHoSo({
    ChucNangHienTai: item?.chucNangHienTai,
    LinhVucID: item?.linhVucID,
    LoaiXuLy: approvePayload ? 1 : rejectPayload ? 4 : 1,
    MaQuyTrinh: item?.maQuyTrinh,
    ThuTucHanhChinhID: item?.thuTucHanhChinhID,
  });
  const {handleSheetPositionChange: handleSortSheetPositionChange} =
    useBottomSheetBackHandler(bottomSheetRef);

  const [dsNguoiNhan, setDsNguoiNhan] = useState([]);
  useEffect(() => {
    if (dsNguoiNhanChuyenHoSo?.data?.data.length) {
      setDsNguoiNhan(
        dsNguoiNhanChuyenHoSo?.data?.data.map((item: {ten: any; ma: any}) => {
          return {label: item.ten, value: item.ma};
        }),
      );
    } else {
      setDsNguoiNhan([]);
    }
  }, [dsNguoiNhanChuyenHoSo]);
  const renderBackdrop = useCallback(
    (
      props: React.JSX.IntrinsicAttributes & BottomSheetDefaultBackdropProps,
    ) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
      />
    ),
    [],
  );
  const {mutate: chuyenHoSoMutation, isLoading: isChuyenHoSoLoading} =
    useMutation((payload: any) => handleFunction.chuyenHoSo(payload), {
      onSuccess: responseData => {
        if (responseData?.data?.statusCode === '00') {
          Toast.show({
            type: 'success',
            text1: 'Chuyển hồ sơ thành công',
          });
          queryClient.invalidateQueries(['daNhan']);
          queryClient.invalidateQueries(['chuaNhan']);
          nav.navigate('Success', {
            name: item?.hoSoID,
            responseData: responseData.data, // Pass response data to next screen
          });
        } else {
          Toast.show({
            type: 'error',
            text1: 'Chuyển hồ sơ thất bại',
          });
        }
      },
      onError: _error => {
        Toast.show({
          type: 'error',
          text1: 'Chuyển hồ sơ thất bại',
        });
      },
    });

  const {mutate: pheDuyetHoSoMutation, isLoading: isPheDuyetLoading} =
    useMutation((payload: any) => handleFunction.pheDuyetHoSo(payload), {
      onSuccess: () => {
        Toast.show({
          type: 'success',
          text1: 'Phê duyệt thành công',
        });
      },
      onError: () => {
        Toast.show({
          type: 'error',
          text1: 'Phê duyệt thất bại',
        });
      },
    });

  const {mutate: tuChoiHoSoMutation, isLoading: isTuChoiLoading} = useMutation(
    (payload: any) => handleFunction.TuChoiHoSo(payload),
    {
      onSuccess: _ => {
        Toast.show({
          type: 'success',
          text1: 'Từ chối hồ sơ thành công',
        });
      },
      onError: () => {
        Toast.show({
          type: 'error',
          text1: 'Từ chối hồ sơ thất bại',
        });
      },
    },
  );

  const renderItem = useCallback(
    ({item}: {item: any}) => {
      const isSelected = pick.some(selected => selected.value === item.value);

      return (
        <List.Item
          title={item.label}
          titleNumberOfLines={0}
          titleStyle={{flexWrap: 'wrap'}}
          onPress={() => {
            if (isSelected) {
              setPick(pick.filter(i => i.value !== item.value));
            } else {
              setPick([...pick, item]);
            }
          }}
          right={props =>
            isSelected ? <List.Icon {...props} icon="check" /> : null
          }
        />
      );
    },
    [pick],
  );
  const handleSendOnPress = async () => {
    const newValue =
      pick.length > 0
        ? pick.map(_ => {
            return _.value;
          })
        : item?.listNguoiNhan?.length
        ? item?.listNguoiNhan.map(_ => {
            return _.value;
          })
        : dsNguoiNhan.map((_: any) => {
            return _?.value;
          });
    try {
      if (rejectPayload) {
        tuChoiHoSoMutation(
          {
            thongTinKhongPheDuyet: {
              ...rejectPayload.thongTinKhongPheDuyet,
              noiDungXuLy: text,
              lyDoKhac: text,
            },
            nguoiXuLyHoSo: {
              ...rejectPayload.nguoiXuLyHoSo,
              noiDungXuLy: text,
            },
          },
          {
            onSuccess: () => {
              const chuyenHoSoBody = prepareChuyenHoSoBody({
                item,
                loaiXuLy: 4, // Reject loaiXuLy
                user: user.user,
                listNguoiNhan: newValue,
              });
              chuyenHoSoMutation(chuyenHoSoBody);
            },
          },
        );
      }
      if (approvePayload) {
        await pheDuyetHoSoMutation(
          {
            quaTrinhXuLyID: item?.quaTrinhXuLyHienTaiID,
            nguoiXuLyHoSo: {
              nguoiXuLyHoSoHienTaiID: item?.nguoiXuLyHoSoHienTaiID,
              noiDungXuLy: text,
              nguoiXuLyID: user.user?.user_PortalID,
              phongBanXuLyID: user.user?.phongBanID,
              donViXuLyID: user.user?.donViID,
              yKienCuaLanhDao: true,
              lyDoTreHanID: null,
              nguyenDoTre: approvePayload.nguyenDoTre,
            },
          },
          {
            onSuccess: () => {
              const chuyenHoSoBody = prepareChuyenHoSoBody({
                item,
                loaiXuLy: approvePayload ? 1 : rejectPayload ? 4 : 1,
                user: user.user,
                listNguoiNhan: newValue,
              });
              chuyenHoSoMutation(chuyenHoSoBody);
            },
          },
        );
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Chuyển hồ sơ thất bại',
      });
    }
  };

  const styles = React.useMemo(() => themedStyles(theme), [theme]);
  return (
    <View style={{flex: 1}}>
      <CCAppBar label="Chuyển hồ sơ tới ..." isBack />
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}>
        <View
          className="flex-1 mt-3.5 space-y-5 p-6 rounded-lg"
          style={{backgroundColor: theme.colors.background}}>
          <RowComponent>
            <AcTextInput
              label={'Nội dung xử lý (Bắt buộc)'}
              mode="outlined"
              value={text}
              onChangeText={e => {
                setText(e);
              }}
              multiline
              dismissKeyboardOnTouch
            />
          </RowComponent>
          <Divider />
          {dsNguoiNhan.length > 0 && (
            <RowComponent>
              <View>
                <Button
                  mode="text"
                  textColor={theme.colors.outline}
                  contentStyle={styles.labelText}
                  onPress={() => bottomSheetRef.current?.present()}>
                  Chọn người nhận
                </Button>
              </View>
              <View className="flex-row flex-wrap gap-2 mb-2">
                {pick.map(item => (
                  <Chip
                    key={item.value}
                    style={styles.primaryChip}
                    textStyle={styles.primaryChipText}
                    onClose={() => {
                      setPick(pick.filter(i => i.value !== item.value));
                    }}>
                    {item.label}
                  </Chip>
                ))}
              </View>
              <View style={styles.alignEnd}>
                <IconButton
                  mode="contained"
                  icon={'plus'}
                  onPress={() => bottomSheetRef.current?.present()}
                />
              </View>
            </RowComponent>
          )}
        </View>
      </ScrollView>
      <View style={[styles.bottomBar, styles.alignEnd]}>
        <Button
          disabled={!text.length}
          onPress={handleSendOnPress}
          mode="contained"
          loading={isPheDuyetLoading || isChuyenHoSoLoading || isTuChoiLoading}
          icon={() => <ArrowSend width={24} height={24} />}>
          Gửi đi
        </Button>
      </View>
      <BottomSheetModalProvider>
        <BottomSheetModal
          ref={bottomSheetRef}
          index={0}
          snapPoints={snapPoints}
          enablePanDownToClose
          enableDismissOnClose
          onChange={handleSortSheetPositionChange}
          backdropComponent={renderBackdrop}
          android_keyboardInputMode="adjustResize">
          <BottomSheetSectionList
            ItemSeparatorComponent={Divider}
            renderSectionHeader={() => {
              return (
                <View style={{flex: 1}}>
                  <Text style={styles.bottomSheetTitle}>Chọn người nhận</Text>
                  <View className="px-4 mb-2">
                    <BottomSheetTextInput
                      placeholder="Tìm kiếm "
                      placeholderTextColor={theme.colors.outline}
                      value={searchField}
                      onChangeText={setSearchField}
                      style={styles.bottomSheetInput}
                      onFocus={() => bottomSheetRef.current?.snapToIndex(2)}
                    />
                  </View>
                </View>
              );
            }}
            renderItem={renderItem}
            contentContainerStyle={{paddingHorizontal: 8}}
            keyExtractor={item => item.value?.toString()}
            sections={[
              {
                title: '1',
                data: dsNguoiNhan?.filter((i: any) =>
                  i.label.toLowerCase().includes(searchField.toLowerCase()),
                ),
              },
            ]}
            bounces={false}
          />
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </View>
  );
};
const themedStyles = (theme: MD3Theme) =>
  StyleSheet.create({
    bottomBar: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      flexDirection: 'row',
      padding: 8,
      backgroundColor: theme.colors.background,
      gap: 8,
      justifyContent: 'flex-end',
      display: 'flex',
    },
    labelText: {
      justifyContent: 'flex-start',
    },
    alignEnd: {
      flex: 1,
      alignItems: 'flex-end',
    },
    primaryChip: {
      backgroundColor: theme.colors.primaryContainer,
    },
    primaryChipText: {
      color: theme.colors.primary,
    },
    bottomSheetTitle: {
      fontSize: 20,
      fontWeight: '600',
      textAlign: 'center',
      color: theme.colors.onBackground,
      marginBottom: 16,
    },
    scrollView: {
      flex: 1,
    },
    scrollViewContent: {
      flexGrow: 1,
      paddingBottom: 70,
    },
    bottomSheetInput: {
      padding: 16,
      borderWidth: 1,
      borderColor: theme.colors.outline,
      borderRadius: 8,
      marginBottom: 8,
      color: theme.colors.onBackground,
    },
  });

export default TransDocument;
