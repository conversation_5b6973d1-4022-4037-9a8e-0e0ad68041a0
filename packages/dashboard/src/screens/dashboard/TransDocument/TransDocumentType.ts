type danhSachNGuoiNhan = {
  LoaiXuLy?: string;
  NguoiNhanID?: number;
  PhongBanNhanID?: number;
  DonViNhanID?: number;
};
type DuongDiHoSo = {
  DuongDiID?: number;
  QuaTrinhXuLyChuaDatID?: number;
  DanhSachNguoiNhan?: danhSachNGuoiNhan[];
};
type HoSo = {
  hoSoID?: number;
  hoSoOnlineID?: number;
  thuTucHanhChinhID?: number;
  linhVucID?: number;
  userID?: number;
  phongBanID?: number;
  toChucID?: number;
  quaTrinhXuLyID?: number;
  tenDuongDi?: string;
  soBienNhan?: string;
  soBienBanBanGiao?: string;
  chucNangHienTai?: string;
  chucNangKeTiep?: string;
  nguoiChuyenID?: number;
  nguoiXuLyID?: number;
  phongBanXuLyID?: number;
  donViXuLyID?: number;
  noiDungXuLy?: string;
  userName?: string;
  phongBanChuyenID?: number;
  donViChuyenID?: number;
  duongDiHoSo?: DuongDiHoSo[];
  thongTinKhac?: string;
  nhanKetQuaQuaDuongBuuDien?: boolean;
  donViNhanID?: number;
  loaiXuLy?: number;
  khongGiaiQuyetHoSo?: boolean;
  strDuongDiTinhTrang?: string;
  listNguoiNhan?: string[];
  NhanKetQuaQuaDuongBuuDien?: boolean;
};
export type ChuyenHoSoType = {
  HoSo?: HoSo;
};
