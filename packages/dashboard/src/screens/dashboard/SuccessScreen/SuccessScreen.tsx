import React from 'react';
import {View, Text, SafeAreaView} from 'react-native';
import {SuccessIcon} from '../../../icons';
import {useNavigation} from '@react-navigation/native';
import {Button, useTheme} from 'react-native-paper';

export default function SuccessScreen() {
  const nav = useNavigation<any>();
  const theme = useTheme();
  return (
    <SafeAreaView
      className="flex-1 "
      style={{backgroundColor: theme.colors.background}}>
      <View className="flex-1 items-center justify-center px-5">
        <View className="w-16 h-16 rounded-full bg-green-100 items-center justify-center mb-6">
          <SuccessIcon width={27} height={27} />
        </View>

        <Text
          className="text-lg text-center mb-6 leading-6 font-bold"
          style={{color: theme.colors.onBackground}}>
          <PERSON><PERSON><PERSON><PERSON> hồ sơ thành công
        </Text>
        <View className={'flex-row items-center justify-between gap-2 mt-4'}>
          <Button
            mode="contained"
            onPress={() => {
              nav.navigate(`Dashboard`, {screen: 'ProcessDocument'});
            }}>
            X<PERSON> lý hồ sơ khác
          </Button>
        </View>
      </View>
    </SafeAreaView>
  );
}
