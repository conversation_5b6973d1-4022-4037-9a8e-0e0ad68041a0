import {Image} from 'react-native';

const _uint8ToBase64 = (u8Arr: any) => {
  const CHUNK_SIZE = 0x8000; //arbitrary number
  let index = 0;
  const length = u8Arr.length;
  let result = '';
  let slice;
  while (index < length) {
    slice = u8Arr.subarray(index, Math.min(index + CHUNK_SIZE, length));
    result += String.fromCharCode.apply(null, slice);
    index += CHUNK_SIZE;
  }
  return btoa(result);
};
const _base64ToArrayBuffer = (base64: any) => {
  const binary_string = atob(base64);
  const len = binary_string.length;
  const bytes = new Uint8Array(len);
  for (let i = 0; i < len; i++) {
    bytes[i] = binary_string.charCodeAt(i);
  }
  return bytes.buffer;
};

const _getImageDimensions = (
  base64String: string,
): Promise<{width: number; height: number}> => {
  return new Promise((resolve, reject) => {
    Image.getSize(
      `data:image/png;base64,${base64String}`,
      (width, height) => {
        resolve({width, height});
      },
      error => {
        reject(error);
      },
    );
  });
};
export {_uint8ToBase64, _base64ToArrayBuffer, _getImageDimensions};
