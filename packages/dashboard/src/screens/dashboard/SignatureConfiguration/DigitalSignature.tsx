import React from 'react';
import {View} from 'react-native';
import RNFS from 'react-native-fs';
import {Button, Icon, Text} from 'react-native-paper';
import {Assets} from '../../../assets';
import {
  LabelButtonItem,
  LabelButton,
} from '../../../components/ButtonComponent';
import {BottomView} from '../../../components/BottomComponent';
import {CCAppBar} from '../../../components/HeaderComponent/CCAppBar';
import {MainStackParamList} from '../../../navigation/MainNavigator';
import {RouteProp, useRoute} from '@react-navigation/native';
import {useAuthStore} from '@ac-mobile/common';
import {useCertStore} from '../../../stores';
const DigitalSignature = () => {
  const params =
    useRoute<RouteProp<MainStackParamList, 'DigitalSignature'>>().params;

  const buttonsLabel: Array<LabelButtonItem> = [
    {
      label: 'Chọn vị trí ký',
      onPress: () => {
        console.log('Chọn vị trí ký');
      },
    },
    {
      label: 'Chọn vị trí ký nháy',
      onPress: () => {
        console.log('Chọn vị trí ký nháy');
      },
    },
    {
      label: 'Xóa chữ ký',
      onPress: () => {
        console.log('Xóa chữ ký');
      },

      labelStyle: 'text-[#FF5630]',
      style: 'items-end',
    },
  ];

  const {user} = useAuthStore();
  const simCert = useCertStore(state => state.cert);
  console.log(params.noiDungGiayTo);

  return (
    <View className="flex-1">
      <CCAppBar label={params.noiDungGiayTo.originalName} isBack />
      <Text>{JSON.stringify(params.noiDungGiayTo)}</Text>
      <BottomView>
        <LabelButton
          buttons={buttonsLabel}
          containerStyle={'pt-[6px] pb-[22px]'}
        />
        <Button
          onPress={async () => {
            if (user && simCert) {
              try {
                const response = await fetch(
                  'https://pdfobject.com/pdf/sample.pdf',
                );
                const data = await response.blob();
                const reader = new FileReader();
                reader.readAsDataURL(data);
                reader.onloadend = async () => {
                  const base64data = reader.result?.toString().split(',')[1];
                  if (base64data) {
                    const path = `${RNFS.DocumentDirectoryPath}/signed.pdf`;
                    await RNFS.writeFile(path, base64data, 'base64');
                    console.log('File saved successfully at:', path);
                    const savedFile = {
                      uri: `file://${path}`,
                      type: 'application/pdf',
                      name: 'signed.pdf',
                      size: base64data.length,
                    };
                    console.log('Saved file details:', savedFile);
                  }
                };
              } catch (error) {
                console.error('Error saving file:', error);
              }
            }
          }}
          mode="contained"
          icon={() => <Icon source={Assets.ic_sim_card} size={24} />}>
          Xác nhận ký
        </Button>
      </BottomView>
    </View>
  );
};

export default DigitalSignature;
