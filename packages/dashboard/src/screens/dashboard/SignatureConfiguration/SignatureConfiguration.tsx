import {Pressable, Text, View} from 'react-native';
import React from 'react';
import {SettingsSignICon} from '../../../icons';
import {NavigationProp, useNavigation} from '@react-navigation/native';
import {SIGNATURE_TYPE} from '../../../utils/constance';
import {CCAppBar} from '../../../components/HeaderComponent/CCAppBar';
import {MainStackParamList} from '../../../navigation/MainNavigator';

interface SignatureOptionProps {
  title: string;
  onPress: () => void;
}

const SignatureOption: React.FC<SignatureOptionProps> = ({title, onPress}) => (
  <Pressable onPress={onPress}>
    <View className="min-h-[74px] border-[0.3px] border-gray-text rounded-lg p-4 flex-row justify-between items-center shadow-[0_4px_4px_rgba(0,0,0,0.25)] bg-white mb-3">
      <View className="items-center flex-row gap-1">
        <SettingsSignICon width={36} height={36} />
        <Text className="text-sm font-semibold">{title}</Text>
      </View>
    </View>
  </Pressable>
);

export const SignatureConfiguration = () => {
  const navigation = useNavigation<NavigationProp<MainStackParamList>>();

  const handleNavigateToUpdate = (type: SIGNATURE_TYPE) => {
    navigation.navigate('UpdateSignature', {type});
  };

  return (
    <View className="flex-1">
      <CCAppBar isBack label="Cấu hình chữ kí điện tử" />
      <View className="justify-center px-5 pt-3">
        <SignatureOption
          title="Chữ ký chính thức"
          onPress={() => handleNavigateToUpdate(SIGNATURE_TYPE.MAIN)}
        />
        <SignatureOption
          title="Chữ ký nháy"
          onPress={() => handleNavigateToUpdate(SIGNATURE_TYPE.DOT)}
        />
      </View>
    </View>
  );
};
