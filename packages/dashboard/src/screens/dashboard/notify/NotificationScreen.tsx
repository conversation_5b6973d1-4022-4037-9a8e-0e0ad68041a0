import {useNavigation} from '@react-navigation/native';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  FlatList,
  LayoutAnimation,
  Pressable,
  Text,
  TextInput,
  View,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {ModalComponent} from '../../../components/ModalComponent/ModalComponent';
import {BackRow, CheckIcon, ConfirmIcon, Delete} from '../../../icons';
import {NotiIcon} from '../../../icons/NotiICon';
import {SearchIcon} from '../../../icons/SearchIcon';
import {textStyles} from '../../../styles';

export const NotificationScreen = () => {
  const [show, setShow] = useState<boolean>(false);
  const [search, setSearch] = useState<string>('');
  const [list, setList] = useState([
    {
      label: '<PERSON><PERSON> hồ sơ HSA003322 đã quá hạn xử lý',
      value: '<PERSON><PERSON> hồ sơ HSA003322 đã quá hạn xử lý',
      time: '2',
    },
    {
      label: '<PERSON>ã hồ sơ HSA003322 đã quá hạn xử lý',
      value: 'Mã hồ sơ HSA003322 đã quá hạn xử lý',
      time: '2',
    },
    {
      label: 'Mã hồ sơ HSA003322 đã quá hạn xử lý',
      value: 'Mã hồ sơ HSA003322 đã quá hạn xử lý',
      time: '2',
    },
    {
      label: 'Mã hồ sơ HSA003322 đã quá hạn xử lý',
      value: 'Mã hồ sơ HSA003322 đã quá hạn xử lý',
      time: '2',
    },
    {
      label: 'Mã hồ sơ HSA003322 đã quá hạn xử lý',
      value: 'Mã hồ sơ HSA003322 đã quá hạn xử lý',
      time: '2',
    },
    {
      label: 'Mã hồ sơ HSA003324 đã quá hạn xử lý',
      value: 'Mã hồ sơ HSA003322 đã quá hạn xử lý',
      time: '2',
    },
  ]);
  const ref = useRef<TextInput>(null);
  const navigation = useNavigation<any>();
  const [modal, isModalShow] = useState<boolean>(false);

  useEffect(() => {
    if (show && ref.current) {
      ref.current.focus();
    }
  }, [show]);

  // const Item =;

  useEffect(() => {
    // const newList = list1.filter(e =>
    //   e.label.match(new RegExp(`.*${search}.*`, 'i')),
    // );
    // setList(newList);
  }, [search]);

  const renderItem = useCallback(({item}: {item: any}) => {
    return (
      <View>
        <View className="p-3">
          <Text className={`${textStyles.text_16px_bold} font-[600]`}>
            Mới nhất
          </Text>
        </View>

        <View className="bg-blue-light min-h-[72px] items-center px-3 flex-row justify-start gap-x-4">
          <View className="w-12 h-12 justify-center items-center bg-blue-light rounded-full relative">
            <NotiIcon width={25} height={25} />
            <View className="bg-red-500 w-2.5 h-2.5  absolute right-0 bottom-1 rounded-full" />
          </View>
          <View className="gap-y-1">
            <Text className="font-bold">{item.label}</Text>
            <Text className="font-[200]">{item.time} phút</Text>
          </View>
        </View>
      </View>
    );
  }, []);
  return (
    <View className="flex-1">
      <SafeAreaView style={{backgroundColor: 'white'}} edges={['top']} />
      <View className="flex-row bg-white p-2 justify-between">
        <View className="flex-row items-center">
          <Pressable onPress={() => navigation.goBack()}>
            <BackRow width={24} height={24} />
          </Pressable>
          {!show && (
            <Text className={`${textStyles.text_20px_bold} ml-3`}>
              Thông báo
            </Text>
          )}
        </View>

        {show ? (
          <View className="bg-white flex-row items-center ml-4 p-2 border border-gray-300 rounded-xl max-h-[42px] w-[85%] mr-2.5">
            <SearchIcon width={40} height={40} />
            <View className="flex-row items-center flex-1 ml-4.1">
              <TextInput
                ref={ref}
                className="flex-1 mh-[40px]"
                onChangeText={e => setSearch(e)}
                value={search}
                placeholder="search"
              />
            </View>
            <Pressable
              onPress={() => {
                setSearch('');
                setShow(!show);
                LayoutAnimation.configureNext(LayoutAnimation.Presets.linear);
              }}>
              <Delete width={40} height={40} />
            </Pressable>
          </View>
        ) : (
          <Pressable
            onPress={() => {
              LayoutAnimation.configureNext(LayoutAnimation.Presets.linear);
              setShow(!show);
            }}>
            <SearchIcon width={40} height={40} />
          </Pressable>
        )}
      </View>
      <View style={{backgroundColor: 'white', flex: 1}}>
        <View className="flex-row justify-between items-center p-4">
          <Pressable
            onPress={() => {}}
            className="bg-gray-light p-3 rounded-lg">
            <Text className={`${textStyles.text_12px_regular} `}>Chưa đọc</Text>
          </Pressable>
          <Pressable
            className="justify-center flex-row items-center"
            onPress={() => {
              isModalShow(true);
            }}>
            <CheckIcon width={23} height={23} />
            <Text className="font-bold text-[#366AE2]">
              Đánh dấu tất cả là đã đọc
            </Text>
          </Pressable>
        </View>
        <FlatList data={list} renderItem={renderItem} />
      </View>
      <ModalComponent
        isShow={modal}
        onPressLeft={() => isModalShow(false)}
        close={() => isModalShow(false)}
        onPressRight={() => {}}
        type="confirm"
        title="Bạn thực sự muốn đăng xuất?"
        titleChild=" Hành động này sẽ yêu cầu bạn đăng nhập lại để tiếp tục sử dụng"
        Icons={<ConfirmIcon width={27} height={27} />}
        pressTextRight="Xác nhận"
        pressTextLeft="Quay lại"
      />
    </View>
  );
};
