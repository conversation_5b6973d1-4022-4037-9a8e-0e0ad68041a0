import {useIsFocused} from '@react-navigation/native';
import React, {memo, useCallback, useEffect, useRef} from 'react';
import {FlatList, StyleSheet, Text, View} from 'react-native';
import {CardComponent} from '../../../../components/CardComponent/CardComponent';
import {Folder} from '../../../../icons';
import {initLoadStore} from '../../../../stores';

type DocType = {
  DocName?: string;
};
export const TransferredComponent = memo(() => {
  const {count} = initLoadStore();
  const isFocused = useIsFocused();

  useEffect(() => {
    if (count > 0) {
      isFocused && ref.current?.scrollToOffset({animated: true, offset: 0});
    }
  }, [count]);
  const ref = useRef<any>();
  const renderItem = useCallback(({item}: {item: DocType}) => {
    const Icons = <Folder width={32} height={32} />;
    return (
      <CardComponent
        Icons={Icons}
        headerCard={{label: 'Mã hồ sơ', value: '1600709500085'}}
        bodyCard={[
          {label: 'Ngày nhận:', value: ' 25/11/2024'},
          {label: 'Người đứng tên:', value: ' Trần Văn Anh'},
          {
            label: 'Thủ tục:',
            value:
              ' Cấp giấy chứng nhận thay đổi nội dung khuyến mại đăng ký kinh doanh hộ cá thể ấn phẩm Cấp giấy chứng nhận thay đổi nội dung khuyến mại đăng ký kinh doanh hộ cá thể ấn phẩm Cấp giấy chứng nhận thay đổi nội dung khuyến mại đăng ký kinh doanh hộ cá thể ấn phẩm',
          },
        ]}
        buttons={[
          {
            label: 'Thu hồi',
            press: () => {
              console.log();
            },
            type: 'RIGHT',
            className:
              'w-[171px] h-[36px] bg-[#366AE2] justify-center items-center',
          },
        ]}
      />
    );
  }, []);
  return (
    <View className="flex-1 flex items-center justify-center">
      <FlatList
        ref={ref}
        className="px-2"
        showsVerticalScrollIndicator={false}
        data={[]}
        onEndReached={() => {
          // console.log('hết ');
        }}
        contentContainerStyle={{
          flexGrow: 1,
        }}
        ListEmptyComponent={
          <View className="flex-1 items-center justify-center">
            <Text>Không có Data</Text>
          </View>
        }
        renderItem={renderItem}
      />
    </View>
  );
});

const styles = StyleSheet.create({});
