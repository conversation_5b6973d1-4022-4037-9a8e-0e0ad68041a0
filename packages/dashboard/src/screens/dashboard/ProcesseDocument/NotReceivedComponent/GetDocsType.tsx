type HoSo = {
  ChucNangHienTai?: string;
  ChucNangKeTiep?: string;
  DonViChuyenID?: number;
  DonViNhanID?: number;
  DonViXuLyID?: number;
  DuongDiHoSo?: string[];
  HoSoID?: number;
  HoSoOnlineID?: number;
  KhongGiaiQuyetHoSo?: boolean;
  LinhVucID?: number;
  LoaiXuLy?: number;
  NguoiChuyenID?: number;
  NguoiXuLyID?: number;
  NhanKetQuaQuaDuongBuuDien?: boolean;
  NoiDungXuLy?: string;
  PhongBanChuyenID?: number;
  PhongBanID?: number;
  PhongBanXuLyID?: number;
  QuaTrinhXuLyID?: number;
  SoBienBanBanGiao?: string;
  SoBienNhan?: string;
  ThongTinKhac?: string;
  TenDuongDi?: string;
  ThuTucHanhChinhID?: number;
  ToChucID?: number;
  UserID?: number;
  UserName?: string;
};
export type IGetHoSo = {
  HoSo?: HoSo;
};
