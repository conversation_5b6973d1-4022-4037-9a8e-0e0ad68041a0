import {useIsFocused, useNavigation} from '@react-navigation/native';
import React, {memo, useCallback, useRef} from 'react';
import {FlatList, RefreshControl, Text, View} from 'react-native';
import {useAuthStore} from '@ac-mobile/common';
import {ActivityIndicator} from 'react-native-paper';
import {CardComponent} from '../../../../components/CardComponent/CardComponent';
import {Folder} from '../../../../icons';
import Toast from 'react-native-toast-message';
// import { handleFunction } from '../../../../api/dashboard-api';
import {handleFunction} from '../../../../api/dashboard-api';
import {
  HoSoKhongPheDuyetStore,
  IDanhSachHoSo,
  useDanhSachHoSo,
} from '../../../../stores';
export const formatDate = (date: Date) => {
  return `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`;
};
export const NotReceivedComponent = memo(() => {
  const isFocused = useIsFocused();
  const navigation = useNavigation<any>();
  const user = useAuthStore();
  const {handleGetHoSoKhongPheDuyet} = HoSoKhongPheDuyetStore();
  // const {dsNguoiNhan, getNguoiNhanChuyenHoSo} = useDanhSachNguoiNhan();
  const {
    dsHoSoChuaNhan,
    refresh,
    // handleRefreshDanhSachHoSo,
    loadMore,
    hasMore,
    // handleLoadMoreDanhSachHoSo,
  } = useDanhSachHoSo();
  // const handleEndReached = useCallback(() => {
  //   if (hasMore) {
  //     handleLoadMoreDanhSachHoSo({
  //       donviID: user.user?.donViID ?? 0,
  //       phongbanID: user.user?.phongBanID ?? 0,
  //       userID: user.user?.user_PortalID ?? 0,
  //       chucNangHienTai: user.user
  //         ? user.user.metadata.chucNangHienTai
  //             .map((item: any) => item.ma)
  //             .join(',')
  //         : '',
  //     });
  //   }
  // }, [hasMore, handleLoadMoreDanhSachHoSo, user]);
  const renderItem = useCallback(({item}: {item: IDanhSachHoSo}) => {
    const Icons = <Folder width={32} height={32} />;
    return (
      <CardComponent
        Icons={Icons}
        isPending
        timeHandle={item?.thoiGian}
        headerCard={{label: 'Mã hồ sơ', value: item.soBienNhan?.toString()}}
        bodyCard={[
          {
            label: 'Ngày nhận:',
            value: ` ${
              item.ngayNhan && formatDate(new Date(item.ngayNhan?.toString()))
            }`,
          },
          {label: 'Người đứng tên: ', value: item?.nguoiDungTenHoSo},
          {
            label: 'Thủ tục: ',
            value: item?.tenThuTuc,
          },
        ]}
        buttons={[
          {
            label: 'Chuyển hồ sơ',
            press: async () => {
              const result = await handleFunction.NhanHoSo({
                HoSo: {
                  ChucNangHienTai: item?.chucNangHienTai,
                  DonViXuLyID: item?.donViXuLyID
                    ? Number.parseInt(item?.donViXuLyID?.toString())
                    : 0,
                  HoSoID: item?.hoSoID,
                  LinhVucID: item?.linhVucID,
                  NguoiXuLyID: item?.nguoiXuLyID
                    ? Number.parseInt(item?.nguoiXuLyID?.toString())
                    : 0,
                  PhongBanXuLyID: item?.phongBanXuLyID
                    ? Number.parseInt(item?.phongBanXuLyID?.toString())
                    : 0,
                  QuaTrinhXuLyID: item?.quaTrinhXuLyHienTaiID,
                  ThuTucHanhChinhID: item?.thuTucHanhChinhID,
                  DuongDiHoSo: [],
                  NoiDungXuLy: '',
                  ThongTinKhac: '',
                  ChucNangKeTiep: '',
                  UserName: '',
                  SoBienNhan: '',
                  TenDuongDi: '',
                  SoBienBanBanGiao: '',
                  ListNguoiNhan: [],
                  StrDuongDiTinhTrang: '',
                },
              });
              if (result.data?.statusCode == '00') {
                handleGetHoSoKhongPheDuyet({
                  DonViID: user?.user?.donViID && user?.user?.donViID,
                  HoSoID: item?.hoSoID,
                  LoaiXuLy: `${item?.loaiXuLy}`,
                  QuaTrinhXuLyID: item?.quaTrinhXuLyHienTaiID,
                });
                navigation.navigate('TransDocument', {
                  item: {...item},
                });
              }
            },
            className:
              'w-[171px] h-[36px] bg-[#366AE214] justify-center items-center',
            type: 'LEFT',
          },
          {
            label: 'Xử lý hồ sơ',
            press: async () => {
              const result = await handleFunction.NhanHoSo({
                HoSo: {
                  ChucNangHienTai: item?.chucNangHienTai,
                  DonViXuLyID: item?.donViXuLyID
                    ? Number.parseInt(item?.donViXuLyID?.toString())
                    : 0,
                  HoSoID: item?.hoSoID,
                  LinhVucID: item?.linhVucID,
                  NguoiXuLyID: item?.nguoiXuLyID
                    ? Number.parseInt(item?.nguoiXuLyID?.toString())
                    : 0,
                  PhongBanXuLyID: item?.phongBanXuLyID
                    ? Number.parseInt(item?.phongBanXuLyID?.toString())
                    : 0,
                  QuaTrinhXuLyID: item?.quaTrinhXuLyHienTaiID,
                  ThuTucHanhChinhID: item?.thuTucHanhChinhID,
                  DuongDiHoSo: [],
                  NoiDungXuLy: '',
                  ThongTinKhac: '',
                  ChucNangKeTiep: '',
                  UserName: '',
                  SoBienNhan: '',
                  TenDuongDi: '',
                  SoBienBanBanGiao: '',
                  ListNguoiNhan: [],
                  StrDuongDiTinhTrang: '',
                },
              });
              if (result.data?.statusCode == '00') {
                // handleRefreshDanhSachHoSo({
                //   donviID: user.user ? user.user.donViID : 0,
                //   phongbanID: user.user ? user.user.phongBanID : 0,
                //   userID: user.user ? user.user.user_PortalID : 0,
                //   chucNangHienTai: user.user
                //     ? user.user.metadata.chucNangHienTai
                //         .map((item: any) => item.ma)
                //         .join(',')
                //     : '',
                // });
                Toast.show({
                  type: 'success',
                  text1: 'Nhận hồ sơ thành công',
                });
              } else {
                Toast.show({
                  type: 'error',
                  text1: 'Nhận hồ sơ thất bại',
                });
              }
            },
            className:
              'w-[171px] h-[36px] bg-[#366AE2] justify-center items-center',
            type: 'RIGHT',
          },
        ]}
        onPressItem={() => {
          // getNguoiNhanChuyenHoSo({
          //   ChucNangHienTai: item?.chucNangHienTai,
          //   LinhVucID: item?.linhVucID,
          //   LoaiXuLy:
          //     hoSoKhongPheDuyet?.khongPheDuyetID == 0 ? item?.loaiXuLy : 4,
          //   MaQuyTrinh: item?.maQuyTrinh,
          //   ThuTucHanhChinhID: item?.thuTucHanhChinhID,
          // });
          navigation.navigate('ProcessDocumentDetail', {
            DocId: item?.hoSoID,
            handle: 'chuaXuLy',
            ChucNangKeTiep: '',
            ChucNangHienTai: item?.chucNangHienTai,
            LoaiXuLy: item?.loaiXuLy,
            UserID: user?.user?.user_PortalID,
            DonViID: user?.user?.donViID,
            DonViXuLyID: item?.donViXuLyID
              ? Number.parseInt(item?.donViXuLyID?.toString())
              : 0,
            HoSoID: item?.hoSoID,
            LinhVucID: item?.linhVucID,
            NguoiXuLyID: item?.nguoiXuLyID
              ? Number.parseInt(item?.nguoiXuLyID?.toString())
              : 0,
            PhongBanXuLyID: item?.phongBanXuLyID
              ? Number.parseInt(item?.phongBanXuLyID?.toString())
              : 0,
            QuaTrinhXuLyID: item?.quaTrinhXuLyHienTaiID,
            ThuTucHanhChinhID: item?.thuTucHanhChinhID,
            ...item,
          });
        }}
      />
    );
  }, []);
  const ref = useRef<any>();
  return (
    <>
      <View className="flex-1 items-center justity-center">
        <FlatList
          ref={ref}
          className="px-2"
          showsVerticalScrollIndicator={false}
          // keyExtractor={keyExtractor}
          data={dsHoSoChuaNhan ? dsHoSoChuaNhan : []}
          refreshControl={
            <RefreshControl
              colors={['#9Bd35A', '#689F38']}
              refreshing={refresh}
              onRefresh={
                isFocused
                  ? () => {
                      const chucNangHienTai = user.user
                        ? user.user.metadata.chucNangHienTai
                            .map((item: any) => item.ma)
                            .join(',')
                        : '';

                      // return handleRefreshDanhSachHoSo({
                      //   donviID: user.user ? user.user.donViID : 0,
                      //   phongbanID: user.user ? user.user.phongBanID : 0,
                      //   userID: user.user ? user.user.user_PortalID : 0,
                      //   chucNangHienTai,
                      // });
                    }
                  : undefined
              }
            />
          }
          ListEmptyComponent={
            <View className="flex-1 items-center justify-center">
              <Text>Không có data</Text>
            </View>
          }
          onEndReachedThreshold={0.5}
          contentContainerStyle={{
            flexGrow: 1,
          }}
          // onEndReached={handleEndReached}
          ListFooterComponent={
            loadMore && hasMore ? (
              <View className="my-1">
                <ActivityIndicator />
              </View>
            ) : null
          }
          renderItem={renderItem}
        />
      </View>
    </>
  );
});
