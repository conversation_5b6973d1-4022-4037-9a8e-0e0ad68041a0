import {createMaterialTopTabNavigator} from '@react-navigation/material-top-tabs';
import {useNavigation} from '@react-navigation/native';
import {MemoExoticComponent, useMemo} from 'react';
import {StyleSheet, View} from 'react-native';
import {SearchIcon} from '../../../icons/SearchIcon';
import {CCAppBar} from '../../../components/HeaderComponent/CCAppBar';
import * as React from 'react';
import {ListHoSo} from './ListHoSo';
import {useDanhSachHoSo} from '../../../stores';

import {isEqual} from '../../../utils';
import {useTheme} from 'react-native-paper';
import {defaultValueFilter} from '../Search/searchHelperfn';

type ItemBottomTabBar = {
  name: string;
  component: React.ComponentType | MemoExoticComponent<() => JSX.Element>;
};
type BTab = {
  [x: string]: ItemBottomTabBar;
};

// Move tab components outside of ProcessDocument
const NotReceivedTab = React.memo(() => <ListHoSo type="chuaNhan" />);
const ReceivedTab = React.memo(() => <ListHoSo type="daNhan" />);

const IconSearch = () => {
  const {filter} = useDanhSachHoSo();
  const theme = useTheme();

  const isFilterChanged = !isEqual(filter, defaultValueFilter);
  return (
    <View style={[style.iconSearch]}>
      <SearchIcon
        width={40}
        height={40}
        color={isFilterChanged ? theme.colors.primary : undefined}
      />
    </View>
  );
};

// Define B_TAB outside of ProcessDocument
const B_TAB: BTab = {
  ['Chưa nhận']: {
    name: 'Chưa nhận',
    component: NotReceivedTab,
  },
  ['Đã nhận']: {
    name: 'Đã nhận',
    component: ReceivedTab,
  },
};

const ProcessDocument = () => {
  const Tab = useMemo(() => createMaterialTopTabNavigator(), []);
  const nav = useNavigation<any>();
  const theme = useTheme();

  return (
    <>
      <CCAppBar
        label="Hồ sơ cần xử lý"
        iconsAction={[
          {
            icon: IconSearch,
            onPress: () => nav.navigate('Search'),
          },
        ]}
      />
      <View className="flex-1">
        <Tab.Navigator
          screenOptions={{
            tabBarActiveTintColor: theme.colors.primary,
            tabBarInactiveTintColor: theme.colors.outline,
            tabBarStyle: {backgroundColor: theme.colors.background},
            tabBarLabelStyle: {
              fontSize: 12,
              fontWeight: '700',
            },
            tabBarIndicatorStyle: {
              backgroundColor: theme.colors.primary,
              height: 2,
            },
          }}>
          {Object.keys(B_TAB).map(element => (
            <Tab.Screen
              key={B_TAB[element].name}
              name={B_TAB[element].name}
              component={B_TAB[element].component}
            />
          ))}
        </Tab.Navigator>
      </View>
    </>
  );
};

const style = StyleSheet.create({
  iconSearch: {position: 'relative', left: -10, top: -10},
});

export default ProcessDocument;
