import {useNavigation} from '@react-navigation/native';
import React, {memo, useCallback, useRef} from 'react';
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  Text,
  View,
} from 'react-native';
import {CardComponent} from '../../../components/CardComponent/CardComponent';
import {Folder} from '../../../icons';
import {IDanhSachHoSo, useDanhSachHoSo} from '../../../stores';
import {formatDate} from './NotReceivedComponent/NotReceivedComponent';
import {useAuthStore} from '@ac-mobile/common';
import {handleFunction} from '../../../api/dashboard-api';
import moment from 'moment';
import {useInfiniteQuery} from 'react-query';
import {BoxEmptyCollapsible} from '../../../components/BoxCollapsible';
import {useTheme} from 'react-native-paper';
interface ListHoSoProps {
  type: 'daNhan' | 'chuaNhan';
}
export const ListHoSo = memo(({type}: ListHoSoProps) => {
  const navigation = useNavigation<any>();
  const {filter} = useDanhSachHoSo();
  const theme = useTheme();
  const ref = useRef<any>();
  const user = useAuthStore();

  const renderItem = useCallback(
    ({item}: {item: IDanhSachHoSo}) => {
      const Icons = <Folder width={32} height={32} />;

      let buttonList = [
        {
          label: 'Xử lý hồ sơ',
          style: {
            borderRadius: 8,
          },
          press: () => {
            navigation.navigate('ProcessDocumentDetail', {
              DocId: item?.hoSoID,
              ChucNangKeTiep: '',
              ChucNangHienTai: item?.chucNangHienTai,
              LoaiXuLy: item?.loaiXuLy,
              UserID: user?.user?.user_PortalID,
              DonViID: user?.user?.donViID,
              ...item,
            });
          },
        },
      ];

      if (type === 'chuaNhan') {
        buttonList = [
          {
            label: 'Chi tiết hồ sơ',
            style: {
              borderRadius: 8,
            },
            press: () => {
              navigation.navigate('ProcessDocumentDetail', {
                DocId: item?.hoSoID,
                ChucNangKeTiep: '',
                ChucNangHienTai: item?.chucNangHienTai,
                LoaiXuLy: item?.loaiXuLy,
                UserID: user?.user?.user_PortalID,
                DonViID: user?.user?.donViID,
                ...item,
              });
            },
          },
        ];
      }

      return (
        <CardComponent
          Icons={Icons}
          headerCard={{label: 'Mã hồ sơ', value: item.soBienNhan?.toString()}}
          bodyCard={[
            {
              label: 'Ngày nhận:',
              value: ` ${
                item.ngayNhan && formatDate(new Date(item.ngayNhan?.toString()))
              }`,
            },
            {label: 'Người đứng tên: ', value: item?.nguoiDungTenHoSo},
            {
              label: 'Thủ tục:',
              value: item?.tenThuTuc,
            },
            {label: 'Tổ chức: ', value: item?.tenToChuc},
          ]}
          timeHandle={type === 'daNhan' ? item?.thoiGian : undefined}
          buttons={buttonList}
          onPressItem={() => {
            navigation.navigate('ProcessDocumentDetail', {
              DocId: item?.hoSoID,
              handle: type,
              ChucNangKeTiep: '',
              ChucNangHienTai: item?.chucNangHienTai,
              LoaiXuLy: item?.loaiXuLy,
              ...item,
            });
          }}
        />
      );
    },
    [navigation, type, user],
  );
  const fetchData = async ({pageParam = 0}: {pageParam?: number}) => {
    const params = {
      daNhan: type,
      page: pageParam + 1,
      fromDate: filter.fromDate
        ? moment(filter.fromDate).format('YYYY-MM-DD')
        : '',
      toDate: filter.toDate ? moment(filter.toDate).format('YYYY-MM-DD') : '',
      isQuaHan: filter.isQuaHan,
      linhVuc: filter.linhVuc,
      OrderBy: filter.OrderBy.value,
      keyword: filter.keyword,
      donviID: user.user?.donViID,
      phongBanID: user.user?.phongBanID,
      userID: user.user?.user_PortalID,
      chucNangHienTai: user.user
        ? user.user.metadata.chucNangHienTai
            .map((item: any) => item.ma)
            .join(',')
        : '',
    };

    const {data} = await handleFunction.danhSachHoSoPheDuyet(params);

    return data.data;
  };

  const {data, isLoading, fetchNextPage, hasNextPage, refetch, isRefetching} =
    useInfiniteQuery([type, filter], fetchData, {
      getNextPageParam: (lastPage, allPages) => {
        return lastPage.length === 10 ? allPages.length : undefined;
      },
    });

  return (
    <View className="flex-1 items-center justify-center">
      <FlatList
        ref={ref}
        showsVerticalScrollIndicator={false}
        data={data?.pages.flat()}
        refreshControl={
          <RefreshControl refreshing={isRefetching} onRefresh={refetch} />
        }
        style={{width: '100%'}}
        ListEmptyComponent={
          <View className="flex-1 items-center justify-center">
            {isLoading ? (
              <Text
                className="text-[16px] font-[500]"
                style={{
                  color: theme.colors.onSurface,
                  width: '100%',
                  textAlign: 'center',
                }}>
                Đang tải
              </Text>
            ) : (
              <BoxEmptyCollapsible title="Không có hồ sơ" />
            )}
          </View>
        }
        onEndReachedThreshold={0.5}
        contentContainerStyle={{
          flexGrow: 1,
          paddingHorizontal: 16,
        }}
        onEndReached={() => {
          if (hasNextPage) {
            fetchNextPage();
          }
        }}
        ListFooterComponent={
          isLoading || hasNextPage ? (
            <View className="my-1">
              <ActivityIndicator />
            </View>
          ) : null
        }
        renderItem={renderItem}
      />
    </View>
  );
});
