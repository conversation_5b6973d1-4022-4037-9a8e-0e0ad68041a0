type IThongTinKhongGiaiQuyet = {
  HoSoKhongID?: number;
  HoSoID?: number;
  LyDoID?: number;
  SoBienNhan?: string | null;
  SoBoHoSo?: string | null;
  LinhVucID?: string | null;
  TenLinhVuc?: string | null;
  TenThuTuc?: string | null;
  NgayNhan?: string | null;
  NgayNhanLaiHoSo?: string | null;
  HoTenNguoiNop?: string | null;
  NgayXuLy?: string | null;
  NoiDungXuLy?: string | null;
  YKienXuLy?: string | null;
  LoaiXuLy?: string | null;
  LyDoKhac?: string | null;
  NguoiKyID?: number;
  NguoiXuLyID?: number;
  DaNhan?: boolean;
  PhongBanID?: number;
  DonViID?: number;
  QuaTrinhXuLyID?: string | null;
  UserID?: string | null;
  NguoiXuLyHoSoHienTaiID?: number;
  LyDoTreHanID?: string | null;
  NguyenDoTre?: string | null;
  SoDienThoaiGoiThongBao?: string | null;
};
type INguoiXuLyHoSo = {
  NguoiXuLyHoSoHienTaiID?: number;
  HoSoID?: number;
  QuaTrinhXuLyID?: string | null;
  NguoiChuyenID?: string | null;
  NguoiNhanID?: string | null;
  NgayChuyen?: string | null;
  NgayNhan?: string | null;
  NgayDenHen?: string | null;
  ThoiGianXuLyChoPhep?: string | null;
  ThoiGianXuLyThucTe?: string | null;
  ThoiGianXuLyTreHan?: string | null;
  ThoiGianXuLyConLai?: string | null;
  NoiDungXuLy?: string | null;
  NgayXuLy?: string | null;
  NguoiXuLyID?: number;
  PhongBanXuLyID?: number;
  DonViXuLyID?: number;
  YKienCuaLanhDao?: boolean;
  TreHan?: string | null;
  LyDoTreHanID?: string | null;
  NguyenDoTre?: string | null;
  DaHoanTat?: string | null;
  CreatedUserID?: string | null;
  CreatedDate?: string | null;
  LastUpdUserID?: string | null;
  LastUpdDate?: string | null;
  QuaTrinhXuLyLoi?: string | null;
  NguoiNhanBanGiaoID?: string | null;
  GoiDonViNgoaiHeThong?: string | null;
  LuongPhu?: string | null;
  PhongBanChuyenID?: string | null;
};
export type IRefuseDocs = {
  ThongTinKhongGiaiQuyet?: IThongTinKhongGiaiQuyet;
  ListTapTinDinhKem?: string[];
  NguoiXuLyHoSo?: INguoiXuLyHoSo;
};
