import {useAuthStore} from '@ac-mobile/common';
import {
  NavigationProp,
  RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import * as React from 'react';
import {KeyboardAvoidingView, ScrollView, StyleSheet, View} from 'react-native';
import {
  ActivityIndicator,
  Button,
  MD3Theme,
  useTheme,
} from 'react-native-paper';
import {
  BoxCollapsible,
  BoxDocument,
  BoxEmptyCollapsible,
} from '../../../components/BoxCollapsible';
import {ContentCollapsibleType} from '../../../components/BoxCollapsible/ContentCollapsible';
import ExpandableText from '../../../components/BoxCollapsible/ExpandableText';
import {MainStackParamList} from '../../../navigation/MainNavigator';
import {textStyles} from '../../../styles';
import {
  boxDocumentType,
  ETypeActionDocument,
} from '../ProcessedDocument/ApproveRejectDocument';
import {formatDate} from './NotReceivedComponent/NotReceivedComponent';
import {CardSnackBar} from '../../../components/SnackBarComponent';
import {CCAppBar} from '../../../components/HeaderComponent/CCAppBar';
import {useQueryClient} from 'react-query';
import {useDocumentDetail} from '../../../useQueryState/UseDocumentDetail';
import {useDanhSachHoSoDinhKem} from '../../../useQueryState/UseDanhSachHoSoDinhKem';
import {useDanhSachNoiDungGiayTo} from '../../../useQueryState/UseDanhSachNoiDungGiayTo';
import {IsPlatformIOS} from '../../../DashboardFn';
import {RefreshControl} from 'react-native';
import {useNhanHoSo} from '../../../useQueryState/UseNhanHoSo';
import {showESign} from '../../../configs/common';

const ProcessDocumentDetail = () => {
  const navigation = useNavigation<NavigationProp<MainStackParamList>>();
  const queryClient = useQueryClient();
  const theme = useTheme();
  const {mutateAsync: nhanHoSo, isLoading: isNhanHoSoLoading} = useNhanHoSo();
  const {user} = useAuthStore();

  const processDocumentDetailParams =
    useRoute<RouteProp<MainStackParamList, 'ProcessDocumentDetail'>>().params;
  const {DocId, handle, ChucNangHienTai, ChucNangKeTiep, LoaiXuLy, ...param} =
    processDocumentDetailParams;

  const {
    data,
    isLoading,
    isFetching: isDetailFetching,
  } = useDocumentDetail({
    ChucNangHienTai: ChucNangHienTai,
    ChucNangKeTiep: ChucNangKeTiep,
    DonViID: user?.donViID,
    HoSoID: Number.parseInt(DocId, 10),
    LoaiXuLy: LoaiXuLy.toString(),
    UserID: user?.user_PortalID,
  });

  const onPressButton = React.useCallback(
    (action: ETypeActionDocument, item: typeof processDocumentDetailParams) => {
      navigation.navigate('ApproveRejectDocument', {
        actionType: action,
        nguoiXuLyHoSoHienTaiID:
          data?.data.data?.thongTinThuLyHoSo?.nguoiXuLyHoSoHienTaiID,
        ...item,
      });
    },
    [data?.data.data?.thongTinThuLyHoSo?.nguoiXuLyHoSoHienTaiID, navigation],
  );

  const {
    data: dataHoSoDinhKem,
    isLoading: isLoadingHoSoDinhKem,
    isFetching: isAttachmentFetching,
  } = useDanhSachHoSoDinhKem({
    HoSoID: Number.parseInt(DocId, 10),
    DonViID: user?.donViID ? Number.parseInt(user?.donViID.toString(), 10) : 0,
  });

  const {
    data: dataNoiDungGiayTo,
    isLoading: isLoadingNoiDungGiayTo,
    isFetching: isContentFetching,
  } = useDanhSachNoiDungGiayTo({
    DonViID: user?.donViID,
    HoSoID: Number.parseInt(DocId, 10),
    UserID: user?.user_PortalID,
    PageNum: 1,
    PageSize: 100,
  });

  const dataInformation = [
    {
      label: 'Mã hồ sơ',
      content: data?.data?.data?.thongTinThuLyHoSo?.soBienNhan,
    },
    {
      label: 'Ngày nhận',
      content:
        data?.data?.data?.thongTinThuLyHoSo?.ngayNhan &&
        formatDate(
          new Date(data?.data?.data?.thongTinThuLyHoSo?.ngayNhan?.toString()),
        ),
    },
    {
      label: 'Ngày hẹn trả',
      content:
        data?.data?.data?.thongTinThuLyHoSo?.ngayHenTra &&
        formatDate(
          new Date(data?.data?.data?.thongTinThuLyHoSo?.ngayHenTra?.toString()),
        ),
    },
    {
      label: 'Thời gian còn lại',
      content: param?.thoiGian,
    },
    {
      label: 'Người đứng tên',
      content: data?.data?.data?.thongTinThuLyHoSo?.hoTenNguoiNop,
    },
    {label: 'Thủ tục', content: data?.data?.data?.thongTinThuLyHoSo?.tenThuTuc},
    {
      label: 'Tình trạng',
      content: data?.data?.data?.quaTrinhXulyHoSo?.find(
        (_: {quaTrinhHienTai: boolean}) => _.quaTrinhHienTai === true,
      )?.buocXuLy,
    },
  ];
  const styles = themedStyles(theme);

  const handleRenderBottomButton = React.useCallback(
    (trangThai: boolean | undefined) => {
      switch (trangThai) {
        case true: {
          return (
            <View style={styles.bottomBar}>
              {!!data?.data.data?.thongTinThuLyHoSo?.nguoiXuLyHoSoHienTaiID && (
                <>
                  <Button
                    mode="outlined"
                    style={{flex: 1, borderRadius: 8}}
                    onPress={() =>
                      onPressButton(
                        ETypeActionDocument.Reject,
                        processDocumentDetailParams,
                      )
                    }>
                    {'Từ chối'}
                  </Button>
                  <Button
                    mode="contained"
                    style={{flex: 1, borderRadius: 8}}
                    onPress={() =>
                      onPressButton(ETypeActionDocument.Approve, {
                        ...processDocumentDetailParams,
                      })
                    }>
                    {'Phê duyệt'}
                  </Button>
                </>
              )}
            </View>
          );
        }
        case false: {
          return (
            <View style={styles.bottomBar}>
              <Button
                mode="contained"
                loading={isNhanHoSoLoading}
                disabled={isNhanHoSoLoading}
                style={{flex: 1, marginRight: 8, marginBottom: 8}}
                onPress={async () => {
                  const result = await nhanHoSo({
                    HoSo: {
                      hoSoID: processDocumentDetailParams.hoSoID,
                      quaTrinhXuLyID: param?.quaTrinhXuLyHienTaiID,
                      chucNangHienTai:
                        processDocumentDetailParams.chucNangHienTai,
                      nguoiXuLyID: processDocumentDetailParams.nguoiXuLyID,
                      phongBanXuLyID:
                        processDocumentDetailParams.phongBanXuLyID,
                      donViXuLyID: processDocumentDetailParams.donViXuLyID,
                    },
                  });
                  if (result.data?.statusCode === '00') {
                    navigation.goBack();
                  }
                }}>
                {'Nhận hồ sơ'}
              </Button>
            </View>
          );
        }
        default:
          return <></>;
      }
    },
    [
      styles.bottomBar,
      data?.data.data?.thongTinThuLyHoSo?.nguoiXuLyHoSoHienTaiID,
      onPressButton,
      processDocumentDetailParams,
      isNhanHoSoLoading,
      nhanHoSo,
      param?.quaTrinhXuLyHienTaiID,
      navigation,
    ],
  );
  const onRefresh = React.useCallback(async () => {
    queryClient.invalidateQueries(['useDocumentDetail']);
    queryClient.invalidateQueries(['useDanhSachHoSoDinhKem']);
    queryClient.invalidateQueries(['useDanhSachNoiDungGiayTo']);
  }, [queryClient]);

  return (
    <View className="flex-1">
      <CCAppBar label="Chi tiết hồ sơ" isBack />
      {isLoading ? (
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator />
        </View>
      ) : (
        <KeyboardAvoidingView
          className="flex-1"
          behavior={IsPlatformIOS ? 'padding' : undefined}>
          <ScrollView
            contentContainerStyle={{
              flexGrow: 1,
              padding: 16,
              paddingBottom: 70,
            }}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={
                  isDetailFetching || isAttachmentFetching || isContentFetching
                }
                onRefresh={onRefresh}
              />
            }>
            {handle === '1' && (
              <CardSnackBar
                message="Hồ sơ đã được chuyển cho bộ phận văn thư"
                containerStyle={'mb-4'}
              />
            )}

            <BoxCollapsible
              title="Thông tin chung"
              subTitle="Thông tin tổng quát về hồ sơ"
              containerStyle={'mb-4'}
              data={dataInformation}
              type={ContentCollapsibleType.TEXT_ROW}
            />
            <BoxCollapsible
              title="Quá trình xử lý"
              subTitle="Tiến trình mà hồ sơ được xử lý"
              containerStyle={'mb-4'}
              data={data?.data?.data?.quaTrinhXulyHoSo?.reverse()}
              type={ContentCollapsibleType.PROCESS_BAR}
            />
            {isLoadingHoSoDinhKem ? (
              <ActivityIndicator />
            ) : (
              <BoxCollapsible
                title="Thành phần hồ sơ"
                subTitle="Những tài liệu giấy tờ của chủ hồ sơ"
                containerStyle={'mb-4'}
                emptyComponent={
                  <BoxEmptyCollapsible title="Không có tài liệu đính kèm" />
                }
                data={dataHoSoDinhKem?.data?.data}
                type={ContentCollapsibleType.DOCUMENT}
              />
            )}
            <BoxCollapsible
              title={
                (showESign && 'Văn bản cần ký') || 'Thông tin chuyển xử lý'
              }
              containerStyle={'mb-4'}
              subTitle="Tài liệu giấy tờ được trình lên bởi cấp dưới"
              emptyComponent={
                <BoxEmptyCollapsible title="Không có tài liệu cần ký" />
              }
              renderItem={
                <View>
                  {isLoadingNoiDungGiayTo ? (
                    <ActivityIndicator />
                  ) : (
                    dataNoiDungGiayTo?.data.data
                      ?.filter((e: any) => {
                        const rs =
                          parseInt(e.createdUserID, 10) !== user?.user_MasterID;

                        return rs;
                      })
                      .map(
                        (
                          item: boxDocumentType | undefined,
                          i: React.Key | null | undefined,
                        ) => (
                          <View className="mt-3" key={i}>
                            <BoxDocument
                              noiDungGiayTo={item}
                              styleLabel={textStyles.text_14px_regular}
                            />
                          </View>
                        ),
                      )
                  )}
                  {!!data?.data?.data?.thongTinThuLyHoSo?.noiDungXuLyTongHop
                    ?.length && (
                    <View className="mt-6">
                      <ExpandableText
                        label="Nội dung trình phê duyệt"
                        content={
                          data?.data?.data?.thongTinThuLyHoSo
                            ?.noiDungXuLyTongHop || ''
                        }
                      />
                    </View>
                  )}
                </View>
              }
            />
          </ScrollView>
        </KeyboardAvoidingView>
      )}
      {data?.data.data?.thongTinThuLyHoSo ? (
        <>{handleRenderBottomButton(processDocumentDetailParams.daNhan)}</>
      ) : (
        <></>
      )}
    </View>
  );
};

const themedStyles = (theme: MD3Theme) =>
  StyleSheet.create({
    bottomBar: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      flexDirection: 'row',
      padding: 8,
      backgroundColor: theme.colors.background,
      gap: 8,
    },
  });

export default ProcessDocumentDetail;
