import {useNavigation} from '@react-navigation/native';
import React, {useState} from 'react';
import {ImageBackground, Text, View} from 'react-native';
import {ModalComponent} from '../../../components/ModalComponent/ModalComponent';
import {OptionsComponent} from '../../../components/OptionsComponent/OptionsComponent';
import {
  ArrowRight,
  CloseCircleBold,
  Exit,
  Profile,
  Setting,
} from '../../../icons';
import {useAuthStore, useConfigStore} from '@ac-mobile/common';
import {CCAppBar} from '../../../components/HeaderComponent/CCAppBar';
import {useTheme} from 'react-native-paper';
import {Assets} from '../../../assets';
import {showESign} from '../../../configs/common';
import DeviceInfo from 'react-native-device-info';

const IconExit = () => {
  return <Exit width={35} height={35} />;
};

export const MyAccountScreen = () => {
  const {logout, user} = useAuthStore();
  const {setConfig} = useConfigStore();
  const [show, isShow] = useState<boolean>(false);
  const nav = useNavigation<any>();
  const theme = useTheme();

  return (
    <View>
      <CCAppBar
        iconsAction={[
          {
            icon: IconExit,
            onPress: () => isShow(true),
          },
        ]}
        label="Tài khoản của tôi"
      />
      <ImageBackground
        source={Assets.bgHeader}
        style={{
          width: '100%',
          backgroundColor: theme.colors.primary, // Add solid background color

          bottom: 0,
          zIndex: 0,
        }}
        imageStyle={{
          width: '100%', // Make image full width
          height: 'auto', // Let height adjust automatically
          resizeMode: 'cover', // Change to cover to fill the width
        }}>
        <View
          style={{
            alignItems: 'center',
            justifyContent: 'center',
            paddingVertical: 20,
            zIndex: 1,
          }}>
          <View
            style={{
              width: 90,
              height: 90,
              borderRadius: 45,
              borderWidth: 3,
              borderColor: 'white',
              backgroundColor: theme.colors.surfaceVariant,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Text
              style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: theme.colors.onSurfaceVariant,
              }}>
              {user?.hoTen?.[0]?.toUpperCase() || 'U'}
            </Text>
          </View>

          <View
            style={{
              alignItems: 'center',
              marginTop: 20,
            }}>
            <Text
              style={{
                fontWeight: 'bold',
                fontSize: 20,
                color: theme.colors.onPrimary,
              }}>
              {user?.hoTen}
            </Text>
            <Text
              style={{
                fontSize: 14,
                color: theme.colors.onPrimary,
                opacity: 0.8,
                marginTop: 6,
              }}>
              {user?.tenDonVi}
            </Text>
          </View>
        </View>
      </ImageBackground>

      <OptionsComponent
        options={[
          {
            label: 'Hồ sơ cá nhân',
            IconsLeft: <Profile width={50} height={50} />,
            IconsRight: <ArrowRight width={40} height={40} />,
            press: () => nav.navigate('MyProfile'),
            show: true,
          },
          {
            label: 'Cấu hình chữ kí điện tử',
            IconsLeft: <Setting width={50} height={50} />,
            IconsRight: <ArrowRight width={40} height={40} />,
            press: () => nav.navigate('SignatureConfiguration'),
            show: showESign,
          },
        ]}
      />
      <ModalComponent
        isShow={show}
        onPressLeft={() => isShow(false)}
        close={() => isShow(false)}
        onPressRight={() => {
          // Clear auth data
          logout();

          // Set login mode based on bundle ID
          const bundleId = DeviceInfo.getBundleId();
          if (bundleId === 'vn.gov.quangninh.dichvucong') {
            setConfig('loginMode', 'citizen');
          } else if (bundleId === 'vn.gov.quangninh.dichvucong.congchuc') {
            setConfig('loginMode', 'officer');
          }
        }}
        type="exit"
        title="Bạn thực sự muốn đăng xuất?"
        titleChild=" Hành động này sẽ yêu cầu bạn đăng nhập lại để tiếp tục sử dụng"
        Icons={<CloseCircleBold width={27} height={27} />}
        pressTextRight="Đăng xuất"
        pressTextLeft="Quay lại"
      />
    </View>
  );
};
