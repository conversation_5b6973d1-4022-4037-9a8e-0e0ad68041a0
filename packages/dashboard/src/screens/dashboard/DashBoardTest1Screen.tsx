import React from 'react';
import ErrorBoundary from '../../components/ErrorBoundary';
import SplashScreen from '../../components/SplashScreen';
import {Button, Text} from 'react-native-paper';
import {View} from 'react-native';

const DashBoardTest1Screen = ({navigation}: any) => {
  return (
    <ErrorBoundary name="TestScreen">
      <React.Suspense fallback={<SplashScreen />}>
        <View className="flex-1 items-center p-4 gap-4">
          <Text>DashBoardTest1Screen</Text>
          <Button
            mode="contained"
            onPress={() => {
              navigation.navigate('Test2Screen');
            }}>
            Go to DashBoard Test2Screen
          </Button>
        </View>
      </React.Suspense>
    </ErrorBoundary>
  );
};

export default DashBoardTest1Screen;
