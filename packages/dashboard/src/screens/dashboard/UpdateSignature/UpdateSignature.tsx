import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {Pressable, Text, View, Image} from 'react-native';
import DocumentPicker, {
  DocumentPickerResponse,
} from 'react-native-document-picker';
import {Button} from 'react-native-paper';
import {AttachFileIcon} from '../../../icons';
import {MainStackParamList} from '../../../navigation/MainNavigator';

import {SIGNATURE_TYPE} from '../../../utils/constance';
import {EditIcon} from '../../../icons/EditIcon';

import Toast from 'react-native-toast-message';
import {useAuthStore} from '@ac-mobile/common';
import {handleFunction} from '../../../api/dashboard-api';

import {CCAppBar} from '../../../components/HeaderComponent/CCAppBar';
import {AxiosResponse} from 'axios';

export const UpdateSignature = () => {
  const navigation = useNavigation();
  const [fileSelected, setFileSelected] = useState<
    DocumentPickerResponse | undefined
  >(undefined);
  const {user} = useAuthStore();
  const pickDocument = async () => {
    const result = await DocumentPicker.pick({
      type: [DocumentPicker.types.pdf, DocumentPicker.types.images],
    });
    try {
      if (result && result[0]?.type === 'image/png') {
        setFileSelected(result[0]);
      } else {
        Toast.show({
          type: 'error',
          text1: 'Định dạng file phải là png',
        });
      }
    } catch (error) {}
  };
  const saveToLocalStorage = async () => {
    if (fileSelected && fileSelected.type) {
      try {
        const {name, size, type, uri} = fileSelected;
        const formData = new FormData();

        formData.append('FileData', {
          name: name,
          size: size,
          type: type,
          uri: uri,
        });
        formData.append('UserID', user?.user_PortalID);
        formData.append(
          'LoaiChuKy',
          params?.type === SIGNATURE_TYPE.MAIN ? 1 : 2,
        );

        await handleFunction.uploadSign(formData);

        Toast.show({
          type: 'success',
          text1: 'Lưu chữ ký thành công',
        });
        navigation.goBack();
      } catch (error: any) {
        console.log(JSON.stringify(error?.response?.data));
      }
    } else {
      Toast.show({
        type: 'error',
        text1: 'Vui lòng chọn file chữ ký',
      });
    }
  };

  const {params} = useRoute<RouteProp<MainStackParamList, 'UpdateSignature'>>();

  useEffect(() => {
    const getData = async () => {
      let res: AxiosResponse<any, any> | undefined;
      try {
        res = await handleFunction.getSign({
          loaiChuKy: params?.type === SIGNATURE_TYPE.MAIN ? 1 : 2,
        });
      } catch (error) {
        Toast.show({
          type: 'info',
          text1: 'Không tìm thấy chữ ký loại này, tải lên chữ ký của bạn',
        });
      }

      if (res?.data?.data?.fileID) {
        const res2 = await handleFunction.DownloadFile({
          fileName: res?.data?.data?.fileID,
        });
        setFileSelected({
          uri: 'data:image/jpeg;base64,' + res2.data?.data,
          name: res.data.data?.fileName,
          fileCopyUri: '',
          type: '',
          size: 0,
        });
      } else {
        Toast.show({
          type: 'error',
          text1: 'Không tìm thấy chữ ký',
        });
      }
    };
    getData();
  }, [params?.type]);

  return (
    <View style={{flex: 1}}>
      <CCAppBar
        label={`Cập nhật chữ ký ${
          params?.type === SIGNATURE_TYPE.MAIN ? 'chính thức' : 'nháy'
        }`}
        isBack
      />
      <View className="bg-white mt-2 space-y-5 p-6 rounded-xl flex-1">
        <View className="gap-2">
          <Text className="font-[600]">
            Thêm mẫu chữ ký {<Text className="text-red-500">*</Text>}
          </Text>
          <View className="gap-1 flex-row items-center">
            <Pressable
              onPress={!fileSelected ? pickDocument : () => {}}
              className="border flex-1 border-gray-300 rounded-lg py-2.5 gap-x-1 pr-10 pl-1 flex-row items-center justify-start">
              <AttachFileIcon width={24} height={24} />
              {fileSelected?.name ? (
                <Text className={`text-blue-600 truncate font-[400]`}>
                  {fileSelected?.name}
                </Text>
              ) : (
                <Text className={`text-gray-text font-[400]`}>
                  {'Thêm mẫu chữ ký'}
                </Text>
              )}
            </Pressable>
            {fileSelected && (
              <Button onPress={pickDocument}>
                <EditIcon width={24} height={24} />
              </Button>
            )}
          </View>
        </View>
        {fileSelected?.uri && (
          <View className="flex-1 justify-center px-1">
            <View className="border border-gray-200 rounded-md p-2">
              <Image
                className="w-full aspect-square rounded-md"
                resizeMode="contain"
                source={{uri: fileSelected?.uri}}
              />
            </View>
          </View>
        )}
      </View>
      {/* <Text>{fileSelected?.uri}</Text> */}
      <View className="bg-[#ffffff] px-4 py-1.5 flex-row ">
        <Button
          labelStyle={[{color: '#1F40AD', fontWeight: 700}]}
          onPress={() => {
            navigation.goBack();
          }}
          style={{
            backgroundColor: '#ffffff',
            borderColor: 'blue',
            borderWidth: 0.3,
            flex: 1,
            borderRadius: 8,
            marginRight: 4,
            padding: 4,
          }}>
          {'Huỷ bỏ'}
        </Button>
        <Button
          onPress={saveToLocalStorage}
          disabled={!fileSelected}
          labelStyle={[{color: '#FFF', fontWeight: 700}]}
          style={{
            backgroundColor: fileSelected ? '#366AE2' : '#919EABCC',
            flex: 1,
            marginLeft: 4,
            borderRadius: 8,
            padding: 4,
          }}>
          <Text>Lưu</Text>
        </Button>
      </View>
    </View>
  );
};
