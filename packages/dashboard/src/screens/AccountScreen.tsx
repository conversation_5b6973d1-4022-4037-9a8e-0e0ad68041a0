import {Federated} from '@callstack/repack/client';
import React from 'react';
import ErrorBoundary from '../components/ErrorBoundary';
import SplashScreen from '../components/SplashScreen';

const Account = React.lazy(() =>
  Federated.importModule('auth', './AccountScreen'),
);

const AccountScreen = () => {
  return (
    <ErrorBoundary name="AccountScreen">
      <React.Suspense fallback={<SplashScreen />}>
        <Account />
      </React.Suspense>
    </ErrorBoundary>
  );
};

export default AccountScreen;
