import {AnyObj} from '@ac-mobile/common';
import moment from 'moment';

export const formatDateTime = (dateTime: string, defaultValue = '-') => {
  return (
    moment(dateTime)
      .format('HH:mm DD/MM/YYYY')
      .replace(`/${new Date().getFullYear()}`, '') ||
    defaultValue ||
    dateTime ||
    ''
  ).replace(`/${new Date().getFullYear()}`, '');
};
export const convertDate = (date: string) => {
  return `${date.split('-')[2]}-${date.split('-')[1]}-${date.split('-')[0]}`;
};
export const isEqual = (obj1: any, obj2: any): boolean => {
  if (obj1 === obj2) return true;
  if (typeof obj1 !== typeof obj2) return false;
  if (typeof obj1 !== 'object') return false;
  if (Array.isArray(obj1) !== Array.isArray(obj2)) return false;

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) return false;

  return keys1.every(key => isEqual(obj1[key], obj2[key]));
};

export const generateDataLoading = ({
  count,
  payload,
}: {
  count: number;
  payload: AnyObj;
}): AnyObj[] => {
  return Array.from({length: count}, (_, __) => payload);
};

export enum E_DOCUMENT_SHARE_PROGRESS_NAME {
  NEW = 'new',
  SYNCED = 'SYNCED', // đã chuyển tiếp sang dvc thành công
  ACCEPTED = '2',
  REJECTED = '3',
  PROCESSING = '4',
  ADDITIONALS = '5',
  PROCESSED = '9',
  RETURNED = '10',
}

export const getStatusText = (
  status: string,
): {label: string; color: string; background: string} => {
  switch (status) {
    case E_DOCUMENT_SHARE_PROGRESS_NAME.ACCEPTED:
      return {
        label: 'Đã chấp nhận',
        color: '#FFC107',
        background: '#FFC107',
      };
    case E_DOCUMENT_SHARE_PROGRESS_NAME.REJECTED:
      return {
        label: 'Bị từ chối',
        color: '#FFC107',
        background: '#FFC107',
      };
    case E_DOCUMENT_SHARE_PROGRESS_NAME.PROCESSING:
      return {
        label: 'Đang xử lý',
        color: '#FFC107',
        background: 'rgba(255, 171, 0, 0.08)',
      };

    case E_DOCUMENT_SHARE_PROGRESS_NAME.ADDITIONALS:
      return {
        label: 'Đang bổ sung',
        color: '#FFC107',
        background: '#FFC107',
      };
    case E_DOCUMENT_SHARE_PROGRESS_NAME.PROCESSED:
      return {
        label: 'Đã xử lý',
        color: '#FFC107',
        background: '#FFC107',
      };
    case E_DOCUMENT_SHARE_PROGRESS_NAME.RETURNED:
      return {
        label: 'Bị trả về',
        color: '#FFC107',
        background: '#FFC107',
      };
    default:
      return {
        label: 'Đã nộp',
        color: '#FFC107',
        background: '#FFF8EB',
      };
  }
};

export const handleFormatDate = ({
  date,
  format = 'DD/mm/yyyy',
}: {
  date: string;
  format?: string;
}) => {
  return moment(date).format(format);
};
