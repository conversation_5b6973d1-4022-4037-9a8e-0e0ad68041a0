import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import AccountScreen from '../screens/dashboard/DashboardAccountScreen';

export type AccountStackParamList = {
  Account: undefined;
};

const Account = createNativeStackNavigator<AccountStackParamList>();

const AccountNavigator = () => {
  return (
    <Account.Navigator screenOptions={{headerShown: false}}>
      <Account.Screen name="Account" component={AccountScreen} />
    </Account.Navigator>
  );
};

export default AccountNavigator;
