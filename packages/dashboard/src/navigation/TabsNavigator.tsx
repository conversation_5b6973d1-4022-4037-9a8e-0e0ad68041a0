/* eslint-disable react/no-unstable-nested-components */
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import React from 'react';
import {Text, useTheme} from 'react-native-paper';
import {HandleDoc, User} from '../icons';
import {HomeIcon} from '../icons/HomeIcon';
import {MyAccountScreen} from '../screens/dashboard/MyAccount/MyAccountScreen';
import ProcessDocument from '../screens/dashboard/ProcesseDocument/ProcessDocuments';
import HomeNavigator from './HomeNavigator';
import {HomeIconOutLine} from '../icons/HomeIconOutline';
import {HandleDocOutLine} from '../icons/HandleDocIconOutLine';
import {UserOutLine} from '../icons/UserIconOutline';
export type TabsParamList = {
  HomeNavigator: undefined;
  DevSignScreen: undefined;
  ServicesNavigator: undefined;
  AccountNavigator: undefined;
  TestScreen: undefined;
  SplashScreen: undefined;
  ProcessDocument: undefined;
  ProcessedDocument: undefined;
};

const Tabs = createBottomTabNavigator<TabsParamList>();

const TabsNavigator = () => {
  const theme = useTheme();
  return (
    <Tabs.Navigator
      screenOptions={{
        headerShown: false,
        tabBarStyle: {
          height: 60,
          justifyContent: 'center',
          alignItems: 'center',
          paddingBottom: 5,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          color: '#aaaaaa',
        },
        tabBarIconStyle: {
          justifyContent: 'center',
          alignItems: 'center',
        },
      }}>
      <Tabs.Screen
        name="HomeNavigator"
        component={HomeNavigator}
        options={{
          title: 'Trang chủ',
          tabBarIcon: ({focused}: {focused: boolean}) =>
            focused ? (
              <HomeIcon width={24} height={24} color={theme.colors.primary} />
            ) : (
              <HomeIconOutLine
                width={24}
                height={24}
                color={theme.colors.outline}
              />
            ),
          tabBarLabel: ({focused}: {focused: boolean}) => (
            <Text
              style={{
                fontSize: 12,
                fontWeight: 400,
                color: focused ? theme.colors.primary : theme.colors.outline,
              }}>
              Trang chủ
            </Text>
          ),
        }}
      />
      <Tabs.Screen
        name="ProcessDocument"
        component={ProcessDocument}
        options={{
          headerTitleAlign: 'left',
          title: 'Cần xử lý',
          headerShown: false,
          tabBarIcon: ({focused}: {focused: boolean; color: string}) =>
            focused ? (
              <HandleDoc width={24} height={24} color={theme.colors.primary} />
            ) : (
              <HandleDocOutLine
                width={24}
                height={24}
                color={theme.colors.outline}
              />
            ),
          tabBarLabel: ({focused}: {focused: boolean}) => (
            <Text
              style={{
                fontSize: 12,
                fontWeight: 400,
                color: focused ? theme.colors.primary : theme.colors.outline,
              }}>
              Cần xử lý
            </Text>
          ),
        }}
      />
      {/* <Tabs.Screen
        name="TestScreen"
        component={TestScreen}
        options={{
          headerTitleAlign: 'center',
          title: 'Màn Test',
          tabBarIcon: ({color, focused}: {focused: boolean; color: string}) => (
            <Icon
              name="profile"
              size={24}
              color={focused ? theme.colors.primary : color}
            />
          ),
        }}
      /> */}
      {/* <Tabs.Screen
        name="ProcessedDocument"
        component={ProcessedDocument}
        options={{
          title: 'Đã xử lý',
          headerShown: false,
          headerTitleAlign: 'center',
          tabBarIcon: ({color, focused}: {focused: boolean; color: string}) => (
            <DoneTaskIcon
              width={24}
              height={24}
              color={focused ? '' : '#366AE2'}
            />
          ),
          tabBarLabel: ({focused}: {focused: boolean}) => (
            <Text
              style={{
                fontSize: 12,
                fontWeight: 400,
                color: focused ? '#366AE2' : 'rgba(99, 115, 129, 1)',
              }}>
              Đã xử lý
            </Text>
          ),
        }}
      /> */}
      <Tabs.Screen
        name="AccountNavigator"
        component={MyAccountScreen}
        options={{
          title: 'Tài khoản',
          headerShown: false,
          headerTitleAlign: 'center',
          tabBarIcon: ({focused}: {focused: boolean}) =>
            focused ? (
              <User width={24} height={24} color={theme.colors.primary} />
            ) : (
              <UserOutLine
                width={24}
                height={24}
                color={theme.colors.outline}
              />
            ),
          tabBarLabel: ({focused}: {focused: boolean}) => (
            <Text
              style={{
                fontSize: 12,
                fontWeight: 400,
                color: focused ? theme.colors.primary : theme.colors.outline,
              }}>
              Tài khoản
            </Text>
          ),
        }}
      />
    </Tabs.Navigator>
  );
};

export default TabsNavigator;
