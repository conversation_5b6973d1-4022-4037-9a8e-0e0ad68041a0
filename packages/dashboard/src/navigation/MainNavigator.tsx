import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import TabsNavigator from './TabsNavigator';
// import DashboardPdfViewer from '../screens/dashboard/DashboardPdfViewer';
import DashBoardTest1Screen from '../screens/dashboard/DashBoardTest1Screen';
import DashBoardTest2Screen from '../screens/dashboard/DashBoardTest2Screen';
import DashboardWebView from '../screens/dashboard/DashboardWebView';
import {MyProfile} from '../screens/dashboard/MyProfile/MyProfile';
import {SignatureConfiguration} from '../screens/dashboard/SignatureConfiguration/SignatureConfiguration';
import ProcessDocumentDetail from '../screens/dashboard/ProcesseDocument/ProcessDocumentDetail';
import {
  ApproveRejectDocument,
  ETypeActionDocument,
} from '../screens/dashboard/ProcessedDocument/ApproveRejectDocument';
import {SafeAreaView} from 'react-native-safe-area-context';
import TransDocument from '../screens/dashboard/TransDocument/TransDocument';
import DigitalSignature from '../screens/dashboard/SignatureConfiguration/DigitalSignature';
import {View} from 'react-native';
import {Search} from '../screens/dashboard/Search/Search';
import {UpdateSignature} from '../screens/dashboard/UpdateSignature/UpdateSignature';
import {GlobalStateView} from '../components/AcView/GlobalStateView';
import {IDanhSachHoSo} from '../stores';
import SuccessScreen from '../screens/dashboard/SuccessScreen/SuccessScreen';
// import DevSignScreen from '../screens/dashboard/DevSign/DevSignScreen';
import {noiDungGiayTo} from '../components/BoxCollapsible/BoxDocument';
import {typeUploadFileBody} from '../api/dashboard-api';
export type ProcessDocumentDetail = {
  DocId: string;
  handle: string;
  LoaiXuLy: string;
  UserID: number;
  DonViID: number;
  ChucNangHienTai: string;
  ChucNangKeTiep: string;
  DonViXuLyID: number;
  DuongDiHoSo: string[];
  HoSoID: number;
  LinhVucID: number;
  ListNguoiNhan: string[];
  NguoiXuLyID: number;
  NoiDungXuLy: string;
  PhongBanXuLyID: number;
  QuaTrinhXuLyID: number;
  SoBienBanBanGiao: string;
  SoBienNhan: string;
  StrDuongDiTinhTrang: string;
  TenDuongDi: string;
  ThongTinKhac: string;
  ThuTucHanhChinhID: number;
  UserName: string;
} & IDanhSachHoSo;
import {NotificationScreen} from '../screens/dashboard/notify/NotificationScreen';
import PdfViewer from '../screens/dashboard/DashboardPdfViewer';

export type MainStackParamList = {
  Dashboard: undefined;
  PdfViewer: {
    url?: string;
    title?: string;
    uploadParams?: typeUploadFileBody;
    deleteFunction?: () => void;
    signStatus: boolean;
  };
  PDFExample: undefined;
  WebView: undefined;
  Test1Screen: undefined;
  Test2Screen: undefined;
  NotificationScreen: undefined;
  // DevSignScreen: undefined;
  MyProfile: undefined;
  SignatureConfiguration: undefined;
  ProcessDocumentDetail: ProcessDocumentDetail;
  UpdateSignature: {type: string} | undefined;
  TransDocument: {
    item: IDanhSachHoSo;
    rejectPayload?: any;
    approvePayload?: any;
  };
  Search: undefined;
  ApproveRejectDocument: {
    actionType: ETypeActionDocument;
    nguoiXuLyHoSoHienTaiID?: number;
  } & ProcessDocumentDetail;
  DigitalSignature: {
    noiDungGiayTo: noiDungGiayTo;
  };
  Success?: {name: string};
  CommonProcedure: undefined;
  CommonProcedureDetail: undefined;
  InformationCommonScreen: undefined;
  ProfileCommonScreen: undefined;
  StepCommonScreen: undefined;
};

const Main = createNativeStackNavigator<MainStackParamList>();

const MainNavigator = () => {
  return (
    <GlobalStateView>
      <View style={{flex: 1}}>
        <Main.Navigator
          screenOptions={{
            headerShown: false,
            headerTitleAlign: 'center',
            headerBackButtonDisplayMode: 'minimal',
          }}>
          <Main.Screen
            name="Dashboard"
            component={TabsNavigator}
            options={{headerShown: false, title: ''}}
          />
          <Main.Screen
            name="Success"
            component={SuccessScreen}
            options={{
              headerShown: false,
            }}
          />
          <Main.Screen name="PdfViewer" component={PdfViewer} />
          <Main.Screen
            name="Test1Screen"
            component={DashBoardTest1Screen}
            options={{title: 'Test 1 screen'}}
          />
          <Main.Screen
            name="Test2Screen"
            component={DashBoardTest2Screen}
            options={{title: 'Test 2 screen'}}
          />
          <Main.Screen
            name="WebView"
            component={DashboardWebView}
            options={{title: 'Test Webview'}}
          />
          <Main.Screen
            name="NotificationScreen"
            component={NotificationScreen}
            options={{
              title: 'Thông báo',
            }}
          />
          {/* <Main.Screen
            name="DevSignScreen"
            component={DevSignScreen}
            options={{
              title: 'Thông báo',
            }}
          /> */}
          <Main.Screen
            name="MyProfile"
            component={MyProfile}
            options={{
              title: 'Hồ sơ cá nhân',
            }}
          />
          <Main.Screen
            name="SignatureConfiguration"
            component={SignatureConfiguration}
            options={{
              title: 'Cấu hình chữ kí điện tử',
            }}
          />
          <Main.Screen
            name="ProcessDocumentDetail"
            component={ProcessDocumentDetail}
            options={{}}
          />
          <Main.Screen
            name="ApproveRejectDocument"
            component={ApproveRejectDocument}
            options={{}}
          />
          <Main.Screen
            name="UpdateSignature"
            component={UpdateSignature}
            options={{
              headerShown: false,
            }}
          />
          <Main.Screen
            name="DigitalSignature"
            component={DigitalSignature}
            options={{
              headerShown: false,
            }}
          />
          <Main.Screen
            name="TransDocument"
            component={TransDocument}
            options={{
              headerShown: false,
            }}
          />
          <Main.Screen
            name="Search"
            component={Search}
            options={{
              headerShown: false,
            }}
          />

          {/* COMMENT: Navigator Công dân Quảng Ninh*/}
        </Main.Navigator>

        <SafeAreaView edges={['bottom']} style={{backgroundColor: '#ffffff'}} />
      </View>
    </GlobalStateView>
  );
};

export default MainNavigator;
