import AsyncStorage from '@react-native-async-storage/async-storage';
import {StateStorage} from 'zustand/middleware';

export const stateStorage: StateStorage = {
  getItem: name => {
    return AsyncStorage.getItem(name) || null;
  },
  setItem: (name: string, value: string) => {
    return AsyncStorage.setItem(name, value);
  },
  removeItem: (name: string) => {
    return AsyncStorage.removeItem(name);
  },
};
