import {useQuery} from 'react-query';
import {handleFunction} from '../api/dashboard-api';

export const useDocumentDetail = (params: {
  HoSoID?: number;
  ChucNangKeTiep?: string;
  ChucNangHienTai?: string;
  LoaiXuLy?: string;
  UserID?: number;
  DonViID?: number;
}) => {
  return useQuery({
    queryKey: ['useDocumentDetail', params],
    queryFn: () => handleFunction.getChiTietHoSo(params),
    enabled: !!params.HoSoID,
  });
};
