import {useQuery} from 'react-query';
import {handleFunction} from '../api/dashboard-api';

export const useNguoiNhanChuyenHoSo = (params: {
  LinhVucID?: number;
  ThuTucHanhChinhID?: number;
  LoaiXuLy?: number;
  ChucNangHienTai?: string;
  MaQuyTrinh?: string;
}) => {
  return useQuery({
    queryKey: ['useNguoiNhanChuyenHoSo', params],
    queryFn: () => handleFunction.GetNguoiNhanChuyenHoSo(params),
  });
};
