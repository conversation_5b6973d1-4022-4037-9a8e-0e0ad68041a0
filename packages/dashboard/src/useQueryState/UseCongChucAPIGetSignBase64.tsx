import {useQuery} from 'react-query';
import {handleFunction} from '../api/dashboard-api';
import {AxiosResponse} from 'axios';
import Toast from 'react-native-toast-message';

export const useCongchucAPIGetSignBase64 = (params: {
  loaiChuKy: number;
  existToast?: boolean;
}) => {
  return useQuery({
    queryKey: ['useCongchucAPISign', params],
    queryFn: async () => {
      let resGetSign: AxiosResponse<any, any> | undefined;
      try {
        resGetSign = await handleFunction.getSign({
          loaiChuKy: params.loaiChuKy,
        });
      } catch (error) {
        console.log('error', error);
        Toast.show({
          type: 'error',
          text1: 'Không thể lấy chữ ký',
        });
      }
      if (resGetSign?.data?.data?.fileID) {
        const resDownloadFile = await handleFunction.DownloadFile({
          fileName: resGetSign?.data?.data?.fileID,
        });
        if (resDownloadFile.data.data.length) {
          return resDownloadFile.data.data;
        } else {
          Toast.show({
            type: 'error',
            text1: 'Không tìm thấy chữ ký',
          });
          return null;
        }
      } else {
        if (params.existToast) {
          Toast.show({
            type: 'error',
            text1: 'Chưa có loại chữ ký này',
          });
        }
        return null;
      }
    },
  });
};
