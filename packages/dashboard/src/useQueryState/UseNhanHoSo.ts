import {useMutation, useQueryClient} from 'react-query';
import {handleFunction} from '../api/dashboard-api';
import Toast from 'react-native-toast-message';

export const useNhanHoSo = () => {
  const queryClient = useQueryClient();

  return useMutation(handleFunction.NhanHoSo, {
    onSuccess: async response => {
      if (response.data?.statusCode === '00') {
        await queryClient.invalidateQueries(['da<PERSON>han']);
        await queryClient.invalidateQueries(['chua<PERSON>han']);
        Toast.show({
          type: 'success',
          text1: '<PERSON><PERSON><PERSON><PERSON> hồ sơ thành công',
        });
      } else {
        Toast.show({
          type: 'error',
          text1: '<PERSON><PERSON><PERSON><PERSON> hồ sơ thất bại',
        });
      }
    },
    onError: () => {
      Toast.show({
        type: 'error',
        props: {
          title: '<PERSON><PERSON><PERSON><PERSON> hồ sơ thất bại',
        },
      });
    },
  });
};
