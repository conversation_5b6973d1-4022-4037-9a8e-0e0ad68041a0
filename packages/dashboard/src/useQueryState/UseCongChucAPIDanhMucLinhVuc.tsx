import {useQuery} from 'react-query';
import {handleFunction} from '../api/dashboard-api';

export const UseCongChucAPIDanhMucLinhVuc = () => {
  return useQuery({
    queryKey: ['useCongChucAPIDanhMucLinhVuc'],
    queryFn: () => handleFunction.GetDanhMucLinhVuc(),
    staleTime: 1000 * 60 * 60 * 24 * 3,
  });
};
export type danhMucType = {
  linhVucID: number;
  maLinhVuc: string;
  tenLinhVuc: string;
};
