# Mini App Dashboard

- [Mini App Dashboard](#mini-app-dashboard)
  - [Install](#install)
  - [Development](#development)
    - [Start server](#start-server)
    - [Start mobile](#start-mobile)
  - [Coding](#coding)
    - [Show toast](#show-toast)

## Install

```sh
npm i -g pnpm@9.5.0
pnpm install
bundle install
cd ios bundle exec pod install
```

## Development

### Start server

```sh
pnpm start:standalone
```

### Start mobile

```sh
pnpm ios
# Or
pnpm android
```

## Coding

### Show toast

```tsx
// Custom
Toast.show({
  type: 'success',
  props: {
    title: 'Hello props.title',
    content: 'This is some something props.content 👋',
  },
});
Toast.show({
  type: 'error',
  props: {
    title: 'Hello props.title',
    content: 'This is some something props.content 👋',
  },
});

// Base
Toast.show({
  type: 'success',
  text1: 'Hello',
  text2: 'This is some something 👋',
});
Toast.show({
  type: 'error',
  text1: 'Hello error',
  text2: 'This is some something error 👋',
});
```
