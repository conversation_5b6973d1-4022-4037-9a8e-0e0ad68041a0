/**
 * @format
 */
import 'jwt-decode';
import 'react-native-svg';
import 'react-native-webview';
import 'react-native-tab-view';
import 'react-native-pager-view';
import 'lottie-react-native';
import {ScriptManager, Script, Federated} from '@callstack/repack/client';
import {AppRegistry, Platform} from 'react-native';
import App from './src/App';
import {name as appName} from './app.json';
import {version as appVersion} from './package.json';
import configKeys from './configKeys';
import {getContainers, useConfigStore} from '@ac-mobile/common';
import AsyncStorage from '@react-native-async-storage/async-storage';

useConfigStore.getState().setConfig(configKeys.CURRENT_APP_VERSION, appVersion);
ScriptManager.shared.setStorage(AsyncStorage);

ScriptManager.shared.addResolver(async (scriptId, caller) => {
  const params = {
    mainAppName: 'congchuchno',
    hostname:
      'https://dichvucong.hanoi.gov.vn/app-store-developer/api/app/congchuchno',
    version: appVersion,
    platform: Platform.OS,
    appName,
    environment: 'development',
  };

  const containers = await getContainers(params);

  console.log('xxx:containers', containers);

  const resolveURL = Federated.createURLResolver({
    containers,
  });

  let url;
  if (__DEV__ && caller === 'main') {
    url = Script.getDevServerURL(scriptId);
  } else {
    url = resolveURL(scriptId, caller);
  }

  if (!url) {
    return undefined;
  }

  return {
    url,
    cache: !__DEV__,
    query: {
      platform: Platform.OS, // only needed in development
    },
    verifyScriptSignature: __DEV__ ? 'off' : 'strict',
  };
});

AppRegistry.registerComponent(appName, () => App);
