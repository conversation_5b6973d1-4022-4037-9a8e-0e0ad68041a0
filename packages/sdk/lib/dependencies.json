{"react-native": {"name": "react-native", "version": "0.74.5"}, "@react-native-async-storage/async-storage": {"name": "@react-native-async-storage/async-storage", "version": "1.24.0"}, "@react-native-community/netinfo": {"name": "@react-native-community/netinfo", "version": "11.3.1"}, "@react-navigation/bottom-tabs": {"name": "@react-navigation/bottom-tabs", "version": "7.1.3"}, "@react-navigation/native": {"name": "@react-navigation/native", "version": "7.0.14"}, "@react-navigation/native-stack": {"name": "@react-navigation/native-stack", "version": "7.1.14"}, "@ac-mobile/common": {"name": "@ac-mobile/common", "version": "0.3.0"}, "axios": {"name": "axios", "version": "0.27.2"}, "jwt-decode": {"name": "jwt-decode", "version": "3.1.2"}, "lottie-react-native": {"name": "lottie-react-native", "version": "6.7.2"}, "nativewind": {"name": "nativewind", "version": "2.0.11"}, "react": {"name": "react", "version": "18.2.0"}, "react-native-pager-view": {"name": "react-native-pager-view", "version": "6.3.0"}, "react-native-paper": {"name": "react-native-paper", "version": "5.12.5"}, "react-native-safe-area-context": {"name": "react-native-safe-area-context", "version": "5.1.0"}, "react-native-screens": {"name": "react-native-screens", "version": "4.3.0"}, "react-native-svg": {"name": "react-native-svg", "version": "14.1.0"}, "react-native-tab-view": {"name": "react-native-tab-view", "version": "4.0.5"}, "react-native-vector-icons": {"name": "react-native-vector-icons", "version": "10.2.0"}, "react-native-webview": {"name": "react-native-webview", "version": "13.12.5"}, "react-native-restart": {"name": "react-native-restart", "version": "0.0.27"}, "@react-navigation/material-top-tabs": {"name": "@react-navigation/material-top-tabs", "version": "7.1.0"}, "react-native-fs": {"name": "react-native-fs", "version": "2.20.0"}, "react-native-gesture-handler": {"name": "react-native-gesture-handler", "version": "2.21.2"}, "react-native-permissions": {"name": "react-native-permissions", "version": "5.2.1"}, "react-native-modal": {"name": "react-native-modal", "version": "13.0.1"}, "react-native-image-crop-picker": {"name": "react-native-image-crop-picker", "version": "0.41.6"}, "react-native-reanimated": {"name": "react-native-reanimated", "version": "3.16.6"}, "react-native-document-picker": {"name": "react-native-document-picker", "version": "9.3.1"}, "react-native-wheel-scrollview-picker": {"name": "react-native-wheel-scrollview-picker", "version": "2.0.6"}, "react-native-blob-util": {"name": "react-native-blob-util", "version": "0.21.2"}, "react-native-device-info": {"name": "react-native-device-info", "version": "14.0.2"}, "react-native-compressor": {"name": "react-native-compressor", "version": "1.10.3"}, "react-native-raw-bottom-sheet": {"name": "react-native-raw-bottom-sheet", "version": "3.0.0"}, "react-native-toast-message": {"name": "react-native-toast-message", "version": "2.2.1"}}