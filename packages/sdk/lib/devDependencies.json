{"prettier": {"name": "prettier", "version": "2.8.8", "devOnly": true}, "jest": {"name": "jest", "version": "^29.6.3", "devOnly": true}, "@babel/core": {"name": "@babel/core", "version": "7.25.9", "devOnly": true}, "@babel/preset-env": {"name": "@babel/preset-env", "version": "7.25.9", "devOnly": true}, "@babel/runtime": {"name": "@babel/runtime", "version": "7.25.9", "devOnly": true}, "@callstack/repack": {"name": "@callstack/repack", "version": "^4.3.3", "devOnly": true}, "@react-native/babel-preset": {"name": "@react-native/babel-preset", "version": "0.74.87", "devOnly": true}, "@react-native/eslint-config": {"name": "@react-native/eslint-config", "version": "0.74.87", "devOnly": true}, "@react-native/metro-config": {"name": "@react-native/metro-config", "version": "0.74.87", "devOnly": true}, "@react-native/typescript-config": {"name": "@react-native/typescript-config", "version": "0.74.87", "devOnly": true}, "@rnx-kit/align-deps": {"name": "@rnx-kit/align-deps", "version": "3.0.1", "devOnly": true}, "@types/react": {"name": "@types/react", "version": "^18.2.6", "devOnly": true}, "@types/react-native-vector-icons": {"name": "@types/react-native-vector-icons", "version": "^6.4.18", "devOnly": true}, "@types/react-test-renderer": {"name": "@types/react-test-renderer", "version": "^18.0.0", "devOnly": true}, "babel-jest": {"name": "babel-jest", "version": "^29.6.3", "devOnly": true}, "babel-loader": {"name": "babel-loader", "version": "^9.2.1", "devOnly": true}, "eslint": {"name": "eslint", "version": "^8.19.0", "devOnly": true}, "react-test-renderer": {"name": "react-test-renderer", "version": "18.2.0", "devOnly": true}, "@ac-mobile/sdk": {"name": "@ac-mobile/sdk", "version": "0.0.11", "devOnly": true}, "tailwindcss": {"name": "tailwindcss", "version": "3.3.2", "devOnly": true}, "terser-webpack-plugin": {"name": "terser-webpack-plugin", "version": "^5.3.10", "devOnly": true}, "typescript": {"name": "typescript", "version": "5.0.4", "devOnly": true}, "webpack": {"name": "webpack", "version": "^5.95.0", "devOnly": true}, "@react-native-community/cli": {"name": "@react-native-community/cli", "version": "13.6.9", "devOnly": true}, "@react-native-community/cli-platform-android": {"name": "@react-native-community/cli-platform-android", "version": "13.6.9", "devOnly": true}, "@react-native/gradle-plugin": {"name": "@react-native/gradle-plugin", "version": "0.74.87", "devOnly": true}, "@tsconfig/react-native": {"name": "@tsconfig/react-native", "version": "^2.0.3", "devOnly": true}, "@types/semver": {"name": "@types/semver", "version": "7.5.8", "devOnly": true}, "babel-plugin-transform-inline-environment-variables": {"name": "babel-plugin-transform-inline-environment-variables", "version": "^0.4.4", "devOnly": true}}