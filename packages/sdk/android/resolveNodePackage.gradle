// source: https://github.com/facebook/react-native/pull/39520
// TODO remove in the future when this lands in RN

ext.resolveNodePackage = { String packageName, File startDir ->
    def candidate = new File(startDir, "node_modules/$packageName")

    if (candidate.exists()) {
        return candidate.canonicalFile
    }

    def parentDir = startDir.parentFile
    if (parentDir == null || startDir.canonicalPath == parentDir.canonicalPath) {
        throw new GradleException("Failed to find the package '$packageName'. Ensure you have installed node_modules.")
    }

    return resolveNodePackage(packageName, parentDir)
}