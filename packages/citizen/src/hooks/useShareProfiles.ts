import {useInfiniteQuery} from 'react-query';
import {share} from '../requester-biz-service/apis/share-api';

type ShareProfilesParams = {
  enabled?: boolean;
};

export const USE_REQUESTER_BIZ_SHARES_KEY = ['shareProfiles'];

export const useShareProfiles = (params: ShareProfilesParams = {}) => {
  const {enabled = true} = params;

  return useInfiniteQuery(
    USE_REQUESTER_BIZ_SHARES_KEY,
    async ({pageParam = 1}) => {
      const response = await share.getShare(pageParam);
      return response;
    },
    {
      enabled,
      getNextPageParam: (lastPage, allPages) => {
        const currentPage = allPages.length;
        const totalItems = lastPage.data.totalItems;
        const itemsPerPage = 20;
        const totalPages = Math.ceil(totalItems / itemsPerPage);

        return currentPage < totalPages ? currentPage + 1 : undefined;
      },
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    },
  );
};
