import {create} from 'zustand';
import uuid from 'react-native-uuid';

export type SubmitFlowItem = {
  code: string;
  name: string;
  dataType: string;
  required: boolean;
  fileExampleUrl?: string;
  fileTemplateUrl?: string;
  uri?: string;
  value?: string | any;
  isAttachmentUri?: boolean;
  physicalFilePath?: string;
  isExternalData?: boolean;
  uriMetaData?: {name?: string};
};

export type SubmitFlowState = {
  currentUuid: string | null;
  selectedLevel: string | null;
  selectedOrganization: Record<string, any> | null;
  stepOneFormData: Record<string, any> | null;
  selectedRole: string | null;
  pdfData: Uint8Array | null;
  isDocumentSigned: boolean;
  htmlData: string;
  submitDocumentInstantResponse: Record<string, any> | null;
  submitDocumentResponse: any | null;
  paymentInfo: any | null;
  receiptInfo: Record<string, any> | null;
  representativeInfo: Record<string, string> | null;
  businessInfo: Record<string, string> | null;
  metadata: Record<string, any> | null;
  itemsArray?: SubmitFlowItem[];
};

export type SubmitFlowActions = {
  generateUuid: () => string;
  setSelectedLevel: (level: string | null) => void;
  setSelectedOrganization: (org: Record<string, any> | null) => void;
  setStepOneFormData: (formData: Record<string, any> | null) => void;
  setSelectedRole: (role: string | null) => void;
  setPdfData: (data: Uint8Array | null) => void;
  setDocumentSigned: (value: boolean) => void;
  setHtmlData: (data: string) => void;
  setSubmitDocumentInstantResponse: (response: Record<string, any>) => void;
  setSubmitDocumentResponse: (response: any) => void;
  setPaymentInfo: (info: any) => void;
  setReceiptInfo: (info: Record<string, any>) => void;
  setRepresentativeInfo: (info: Record<string, string> | null) => void;
  setBusinessInfo: (info: Record<string, string> | null) => void;
  setMetadata: (data: Record<string, any>) => void;
  clearStepOneData: () => void;
  addItemToArray: (item: SubmitFlowItem) => void;
  editItemInArrayByCodeAndUri: (
    code: string,
    uri: string,
    obj?: {name?: string},
  ) => void;
  removeItemFromArray: (code: string) => void;
  clearUriAndMetaDataByCode: (code: string) => void;
  getSubmitItem: () => Array<{
    code: string;
    name: string;
    uri?: string;
    dataType: string;
    isAttachmentUri?: boolean;
  }>;
};

const initState: SubmitFlowState = {
  currentUuid: null,
  selectedLevel: null,
  selectedOrganization: null,
  stepOneFormData: null,
  selectedRole: null,
  pdfData: null,
  isDocumentSigned: false,
  htmlData: '',
  submitDocumentInstantResponse: null,
  submitDocumentResponse: null,
  paymentInfo: null,
  receiptInfo: null,
  representativeInfo: null,
  businessInfo: null,
  metadata: null,
  itemsArray: [],
};

export const useSubmitFlow = create<SubmitFlowState & SubmitFlowActions>(
  (set, get) => ({
    ...initState,

    generateUuid: () => {
      const id = uuid.v4() as string;
      set({currentUuid: id});
      return id;
    },
    setSelectedLevel: (level: any) => set({selectedLevel: level}),
    setSelectedOrganization: (org: any) => set({selectedOrganization: org}),
    setStepOneFormData: (formData: any) => {
      return set({stepOneFormData: formData});
    },
    setSelectedRole: (role: any) => set({selectedRole: role}),
    setPdfData: (data: any) => set({pdfData: data}),
    setDocumentSigned: (value: any) => set({isDocumentSigned: value}),
    setHtmlData: (data: any) => set({htmlData: data}),
    setSubmitDocumentInstantResponse: (response: any) =>
      set({submitDocumentInstantResponse: response}),
    setSubmitDocumentResponse: (response: any) =>
      set({submitDocumentResponse: response}),
    setPaymentInfo: (info: any) => set({paymentInfo: info}),
    setReceiptInfo: (info: any) => set({receiptInfo: info}),
    setRepresentativeInfo: (info: any) => set({representativeInfo: info}),
    setBusinessInfo: (info: any) => set({businessInfo: info}),
    setMetadata: (data: any) => set({metadata: data}),
    clearStepOneData: () =>
      set({
        stepOneFormData: null,
        selectedRole: null,
        selectedLevel: null,
        selectedOrganization: null,
        pdfData: null,
        isDocumentSigned: false,
        htmlData: '',
        submitDocumentInstantResponse: null,
        submitDocumentResponse: null,
        paymentInfo: null,
        receiptInfo: null,
        representativeInfo: null,
        businessInfo: null,
        metadata: null,
        currentUuid: null,
        itemsArray: [],
      }),

    addItemToArray: (item: SubmitFlowItem) => {
      console.log('Adding item to array:', item);
      return set(state => {
        const exists = (state.itemsArray || []).some(i => i.code === item.code);
        if (exists) {
          return {};
        }
        return {itemsArray: [...(state.itemsArray || []), item]};
      });
    },
    editItemInArrayByCodeAndUri: (
      code: string,
      uri: string,
      obj?: {name?: string},
    ) =>
      set(state => ({
        itemsArray: (state.itemsArray || []).map(item =>
          item.code === code ? {...item, uri, uriMetaData: obj} : item,
        ),
      })),

    removeItemFromArray: (code: string) =>
      set(state => ({
        itemsArray: (state.itemsArray || []).filter(item => item.code !== code),
      })),
    clearUriAndMetaDataByCode: (code: string) =>
      set((state: SubmitFlowState) => ({
        itemsArray: (state.itemsArray || []).map((item: SubmitFlowItem) =>
          item.code === code
            ? {...item, uri: undefined, uriMetaData: undefined}
            : item,
        ),
      })),

    getSubmitItem: (): Array<{
      code: string;
      name: string;
      uri?: string;
      dataType: string;
      isAttachmentUri?: boolean;
    }> => {
      const state = get();
      return (state.itemsArray || []).map((item: SubmitFlowItem) => ({
        code: item.code,
        name: item.name,
        uri: item.uri,
        dataType: item.dataType,
        isAttachmentUri: item.isAttachmentUri,
      }));
    },
  }),
);
