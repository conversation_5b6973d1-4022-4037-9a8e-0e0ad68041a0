import {useInfiniteQuery} from 'react-query';
import {
  document,
  GetDocsParams,
} from '../requester-biz-service/apis/documents-api';

type UseDocumentsParams = {
  enabled?: boolean;
  uploadBy?: string;
};

export const USE_REQUESTER_BIZ_DOCUMENTS_KEY = ['documents'];

export const useDocuments = (params: UseDocumentsParams = {}) => {
  const {enabled = true, uploadBy} = params;

  return useInfiniteQuery(
    [...USE_REQUESTER_BIZ_DOCUMENTS_KEY, uploadBy],
    async ({pageParam = 1}) => {
      const queryParams: GetDocsParams = {
        page: pageParam,
        pageSize: 10,
        orderBy: 'createdAt',
        orderDir: 'desc',
      };

      // Add uploadBy parameter to API call
      if (uploadBy) {
        queryParams.uploadBy = uploadBy;
      }

      const response = await document.getDocs(queryParams);
      return response;
    },
    {
      enabled,
      getNextPageParam: (lastPage: any, allPages: any[]) => {
        const currentPage = allPages.length;
        const hasMore = lastPage?.data?.data?.items?.length === 10; // pageSize
        return hasMore ? currentPage + 1 : undefined;
      },
      staleTime: 2 * 60 * 1000, // 2 minutes - reasonable cache time
      cacheTime: 10 * 60 * 1000, // 10 minutes - keep data cached longer
      refetchOnMount: false,
      refetchOnWindowFocus: false,
    },
  );
};
