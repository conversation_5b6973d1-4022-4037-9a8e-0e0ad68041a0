import {useQuery} from 'react-query';
// import {handleFunction} from '../../../api/HN/cong-dan/dashboard-api';
import {AnyObj} from '@ac-mobile/common';
import {template} from '../requester-biz-service/apis/template-api';

export const USE_REQUESTER_BIZ_TEMPLATES_KEY = ['UseRequesterBizTemplates'];

export const UseRequesterBizTemplates = ({
  params,
  textSearchProcedureItem,
}: {
  params: AnyObj;
  textSearchProcedureItem?: string;
}) => {
  return useQuery({
    queryKey: [...USE_REQUESTER_BIZ_TEMPLATES_KEY, textSearchProcedureItem],
    queryFn: () => template.getTemplates({params}),
    staleTime: 1000 * 60 * 60 * 24 * 3,
  });
};

export type ICommonProcedureLoading = {
  id: string;
  name: string;
  statusLoading: boolean;
};
