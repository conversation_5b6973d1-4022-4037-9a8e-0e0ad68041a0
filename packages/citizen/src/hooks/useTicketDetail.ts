import {useQuery} from 'react-query';
import {
  drawNumber,
  TicketDetailResponse,
} from '../requester-biz-service/apis/draw-number-api';

interface UseTicketDetailParams {
  ticketId: string;
  enabled?: boolean;
}

/**
 * Custom hook for fetching ticket details using React Query
 * Provides caching, error handling, and automatic refetching
 */
export const useTicketDetail = ({
  ticketId,
  enabled = true,
}: UseTicketDetailParams) => {
  return useQuery<TicketDetailResponse, Error>(
    ['ticketDetail', ticketId],
    () => {
      console.log(
        '🔍 [useTicketDetail] Fetching ticket details for ID:',
        ticketId,
      );
      return drawNumber.getTicketDetail(ticketId);
    },
    {
      enabled: enabled && !!ticketId,
      staleTime: 30 * 0, // 0 seconds
      cacheTime: 5 * 60 * 1000, // 5 minutes
      retry: 2,
      onSuccess: data => {
        console.log(
          '✅ [useTicketDetail] Successfully fetched ticket details:',
          {
            ticketId,
            status: data?.status,
            ticketNumber: data?.ticketNumber,
            branchName: data?.services?.qmsBranch?.name,
            serviceName: data?.services?.name,
            isOnline: data?.isOnline,
            servingTicket: data?.servingTicket?.ticket_number,
            counterServing: data?.counterServing?.map(c => c.name),
            metadata: data?.metadata,
          },
        );
      },
      onError: err => {
        console.error('❌ [useTicketDetail] Error fetching ticket details:', {
          ticketId,
          error: err.message,
          stack: err.stack,
        });
      },
    },
  );
};
