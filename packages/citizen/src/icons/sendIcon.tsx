import * as React from 'react';
import Svg, {SvgProps, Path} from 'react-native-svg';
export const SendIcon = (props: SvgProps) => (
  <Svg fill={props.fill ?? 'none'} {...props}>
    {props.fill ? (
      <Path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M12.142 5.392a.625.625 0 0 1 .883 0l4.167 4.166a.625.625 0 0 1 0 .884l-4.167 4.166a.624.624 0 0 1-1.082-.437.626.626 0 0 1 .199-.446l3.1-3.1H8.417c-.595 0-1.5.183-2.24.716-.706.508-1.302 1.362-1.302 2.826a.625.625 0 1 1-1.25 0c0-1.87.793-3.1 1.822-3.841.997-.718 2.175-.951 2.97-.951h6.825l-3.1-3.1a.625.625 0 0 1 0-.883Z"
        fill="#118D57"
      />
    ) : (
      <Path
        fill="#fff"
        fillRule="evenodd"
        d="M12.142 5.392a.625.625 0 0 1 .883 0l4.167 4.166a.625.625 0 0 1 0 .884l-4.167 4.166a.624.624 0 0 1-1.082-.437.626.626 0 0 1 .199-.446l3.1-3.1H8.417c-.595 0-1.5.183-2.24.716-.706.508-1.302 1.362-1.302 2.826a.625.625 0 1 1-1.25 0c0-1.87.793-3.1 1.822-3.841.997-.718 2.175-.951 2.97-.951h6.825l-3.1-3.1a.625.625 0 0 1 0-.883Z"
        clipRule="evenodd"
      />
    )}
  </Svg>
);
