import * as React from 'react';
import Svg, {Path, SvgProps} from 'react-native-svg';

// Thêm kiểu mở rộng cho props
interface CustomSvgProps extends SvgProps {
  fillColor?: string; // Thêm thuộc tính fillColor để tuỳ chỉnh màu
}

export const CloseCircleBold = ({
  fillColor = '#FF5630', // Gi<PERSON> trị mặc định
  ...props
}: CustomSvgProps) => {
  return (
    <Svg width={32} height={32} viewBox="0 0 32 32" fill="none" {...props}>
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M29.333 16c0 7.363-5.97 13.333-13.333 13.333-7.364 0-13.334-5.97-13.334-13.334S8.636 2.666 16 2.666s13.333 5.97 13.333 13.333zM11.96 11.96a1 1 0 011.413 0L16 14.585l2.627-2.627a1 1 0 011.413 1.414l-2.627 2.626 2.627 2.627a1 1 0 01-1.413 1.413L16 17.413l-2.627 2.626a1 1 0 01-1.413-1.413l2.626-2.627-2.626-2.626a1 1 0 010-1.414z"
        fill={fillColor} // Sử dụng fillColor cho màu
      />
    </Svg>
  );
};
