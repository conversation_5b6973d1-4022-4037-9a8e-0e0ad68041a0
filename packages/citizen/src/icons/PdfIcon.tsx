import * as React from 'react';
import Svg, {SvgProps, Path, G, Defs} from 'react-native-svg';
/* SVGR has dropped some elements not supported by react-native-svg: filter */
export const PdfIcon = (props: SvgProps) => (
  <Svg fill="none" {...props}>
    <Path
      fill="#E94848"
      d="M14.072 0a2 2 0 0 1 1.414.585l6.627 6.623a2 2 0 0 1 .587 1.415V21.2c0 1.546-1.279 2.8-2.856 2.8H5.156C3.578 24 2.3 22.746 2.3 21.2V2.8C2.3 1.254 3.578 0 5.156 0h8.916Z"
    />
    <G filter="url(#a)">
      <Path
        fill="#fff"
        fillOpacity={0.24}
        d="M21.254 6.942a.5.5 0 0 1-.355.852h-3c-1.546 0-2.94-1.36-2.94-2.868V1.808a.5.5 0 0 1 .854-.352l5.441 5.486Z"
      />
    </G>
    <Path
      fill="#fff"
      d="M10.14 9.594a.938.938 0 0 0-.933.934c0 .636.353 1.427.726 2.17-.291.911-.622 1.888-1.044 2.712-.865.34-1.637.592-2.1.965a.945.945 0 0 0-.289.685.938.938 0 0 0 1.598.663.27.27 0 0 0 .018-.015c.341-.407.744-1.147 1.102-1.822.827-.326 1.693-.656 2.53-.855.61.492 1.492.817 2.219.817a.938.938 0 0 0 .933-.934.938.938 0 0 0-.933-.933c-.583 0-1.43.208-2.077.426a6.912 6.912 0 0 1-1.353-1.762c.247-.763.536-1.526.536-2.117a.938.938 0 0 0-.933-.934Zm0 .56a.37.37 0 0 1 .373.374c0 .28-.15.795-.323 1.35-.233-.54-.423-1.057-.423-1.35a.37.37 0 0 1 .373-.374Zm.16 3.194c.281.446.61.862.969 1.23-.554.152-1.094.346-1.628.55.258-.582.464-1.186.66-1.78Zm3.667 1.193a.37.37 0 0 1 .373.373.37.37 0 0 1-.373.374c-.42 0-1.019-.19-1.497-.455.549-.155 1.147-.292 1.497-.292Zm-5.571 1.671c-.263.469-.524.906-.706 1.126a.355.355 0 0 1-.257.096.37.37 0 0 1-.373-.373c0-.099.042-.197.1-.26.218-.17.694-.372 1.236-.589Z"
    />
    <Defs />
  </Svg>
);
