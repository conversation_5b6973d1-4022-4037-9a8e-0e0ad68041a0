import * as React from 'react';
import Svg, {SvgProps, Path, Rect, ClipPath} from 'react-native-svg';

export const SearchIcon = (props: SvgProps) => (
  <Svg width={'1rem'} height={'1rem'} viewBox="0 0 42 42" {...props}>
    <ClipPath id="clip0_642_6380">
      <Rect
        width="32"
        height="32"
        fill="white"
        transform="translate(16.5 16)"
      />
    </ClipPath>
    <Path
      d="M20.4653 30.6735C14.8153 30.6735 10.2153 26.0735 10.2153 20.4235C10.2153 14.7735 14.8153 10.1735 20.4653 10.1735C26.1153 10.1735 30.7153 14.7735 30.7153 20.4235C30.7153 26.0735 26.1153 30.6735 20.4653 30.6735ZM20.4653 11.6735C15.6353 11.6735 11.7153 15.6035 11.7153 20.4235C11.7153 25.2435 15.6353 29.1735 20.4653 29.1735C25.2953 29.1735 29.2153 25.2435 29.2153 20.4235C29.2153 15.6035 25.2953 11.6735 20.4653 11.6735Z"
      fill={props.color ?? '#212B36'}
    />
    <Path
      d="M30.9653 31.6734C30.7753 31.6734 30.5853 31.6034 30.4353 31.4534L28.4353 29.4534C28.1453 29.1634 28.1453 28.6834 28.4353 28.3934C28.7253 28.1034 29.2053 28.1034 29.4953 28.3934L31.4953 30.3934C31.7853 30.6834 31.7853 31.1634 31.4953 31.4534C31.3453 31.6034 31.1553 31.6734 30.9653 31.6734Z"
      fill={props.color ?? '#212B36'}
    />
  </Svg>
);
