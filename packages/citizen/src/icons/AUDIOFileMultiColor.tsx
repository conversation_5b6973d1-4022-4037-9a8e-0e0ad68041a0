import * as React from 'react';
import Svg, {<PERSON>lip<PERSON><PERSON>, Defs, G, Path} from 'react-native-svg';

const AUDIOFileMultiColor = ({
  color = '#EAC993',
  color2 = '#fff',
  size = 24,
  ...props
}: any) => {
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox={`0 0 40 40`}
      fill="none"
      {...props}>
      <G clipPath="url(#clip0_1255_158073)">
        <Path
          d="M23.172 0a2 2 0 011.414.585l11.828 11.82A2 2 0 0137 13.818v21.514C37 37.911 34.869 40 32.24 40H7.76C5.131 40 3 37.91 3 35.333V4.667C3 2.089 5.131 0 7.76 0h15.412z"
          fill={color}
        />
        <G filter="url(#filter0_d_1255_158073)">
          <Path
            d="M35.155 12.138a.5.5 0 01-.355.852H29c-2.577 0-4.902-2.267-4.902-4.78V2.204a.5.5 0 01.855-.352l10.202 10.286z"
            fill={color2}
            fillOpacity={0.24}
            shapeRendering="crispEdges"
          />
        </G>
        <Path
          d="M22.923 16.798v9.423c0 .28-.095.53-.286.749-.19.219-.432.388-.723.509-.292.12-.582.21-.871.269-.289.059-.56.088-.812.088s-.523-.03-.812-.088a4.767 4.767 0 01-.87-.27 1.91 1.91 0 01-.724-.508 1.107 1.107 0 01-.286-.749c0-.28.095-.53.286-.749.19-.219.432-.388.723-.509.292-.12.582-.21.871-.27.289-.058.56-.087.812-.087.589 0 1.127.109 1.615.328v-4.518l-6.461 1.994v5.965c0 .28-.096.53-.286.749a1.91 1.91 0 01-.724.509c-.292.12-.582.21-.87.269-.29.059-.56.088-.813.088-.252 0-.523-.03-.812-.088a4.772 4.772 0 01-.87-.27 1.912 1.912 0 01-.724-.508 1.107 1.107 0 01-.286-.75c0-.28.095-.53.286-.748a1.91 1.91 0 01.724-.509c.291-.12.582-.21.87-.27.29-.058.56-.088.812-.088.59 0 1.128.11 1.616.329v-8.136c0-.174.053-.333.16-.476a.83.83 0 01.412-.298l7-2.154a.747.747 0 01.236-.034c.224 0 .415.079.572.236a.78.78 0 01.235.572z"
          fill={color2}
        />
      </G>
      <Defs>
        <ClipPath id="clip0_1255_158073">
          <Path fill={color2} d="M0 0H40V40H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
};
export {AUDIOFileMultiColor};
