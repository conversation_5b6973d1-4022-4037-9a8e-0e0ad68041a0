import * as React from 'react';
import Svg, {Defs, G, Path} from 'react-native-svg';

const VIDEOFileMultiColor = ({
  color = '#FF5630',
  color2 = '#fff',
  size = 24,
  ...props
}: any) => {
  return (
    <Svg
      width={size}
      height={size}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}>
      <Path
        d="M23.172 0a2 2 0 011.414.585l11.828 11.82A2 2 0 0137 13.818v21.514C37 37.911 34.869 40 32.24 40H7.76C5.131 40 3 37.91 3 35.333V4.667C3 2.089 5.131 0 7.76 0h15.412z"
        fill={color}
      />
      <G filter="url(#filter0_d_1255_158044)">
        <Path
          d="M35.155 12.138a.5.5 0 01-.355.852H29c-2.577 0-4.902-2.267-4.902-4.78V2.204a.5.5 0 01.855-.352l10.202 10.286z"
          fill={color2}
          fillOpacity={0.24}
          shapeRendering="crispEdges"
        />
      </G>
      <Path
        opacity={0.4}
        d="M20.92 21.891v2.743l5.08 3.03v-8.795l-5.08 3.022z"
        fill={color2}
      />
      <Path
        d="M21.544 17.99H10.936a.937.937 0 00-.936.936v8.69c0 .512.416.935.936.935h10.616a.937.937 0 00.936-.935v-8.698a.942.942 0 00-.944-.928zm-7.496 8.555v-6.556l5.344 3.278-5.344 3.278z"
        fill={color2}
      />
      <Defs></Defs>
    </Svg>
  );
};
export {VIDEOFileMultiColor};
