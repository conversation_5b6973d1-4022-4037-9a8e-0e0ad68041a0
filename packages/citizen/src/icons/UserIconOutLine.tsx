import * as React from 'react';
import Svg, {SvgProps, Path, G} from 'react-native-svg';

export const UserOutLine = (props: SvgProps) => (
  <Svg {...props}>
    <G clipPath="url(#clip0_642_6380)">
      <Path
        d="M12.25 12.75C9.08 12.75 6.5 10.17 6.5 7s2.58-5.75 5.75-5.75S18 3.83 18 7s-2.58 5.75-5.75 5.75zm0-10A4.26 4.26 0 008 7a4.26 4.26 0 004.25 4.25A4.26 4.26 0 0016.5 7a4.26 4.26 0 00-4.25-4.25zM20.84 22.75c-.41 0-.75-.34-.75-.75 0-3.45-3.52-6.25-7.84-6.25S4.41 18.55 4.41 22c0 .41-.34.75-.75.75s-.75-.34-.75-.75c0-4.27 4.19-7.75 9.34-7.75 5.15 0 9.34 3.48 9.34 7.75 0 .41-.34.75-.75.75z"
        fill="#637381"
      />
    </G>
  </Svg>
);
