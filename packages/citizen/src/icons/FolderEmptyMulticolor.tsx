import * as React from 'react';
import Svg, {Defs, LinearGradient, Path, Stop} from 'react-native-svg';

const FolderEmptyMultiColor = ({size = 24, ...props}: any) => {
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox={`0 0 160 160`}
      fill="none"
      {...props}>
      <Path
        fill="#C4CDD5"
        d="M80 105.265c-.108 19.169-.326 48.501 0 48.558.484 0 .97-.07 1.438-.21l45-13.5a5 5 0 0 0 3.563-4.79v-46.28l-50 16.222Z"
        opacity={0.48}
      />
      <Path
        fill="#DFE3E8"
        d="M79.895 105.232 30 89.043v46.28a5.001 5.001 0 0 0 3.563 4.79l45 13.5c.468.14.952.21 1.436.21-.326-.029-.212-29.413-.103-48.591Z"
        opacity={0.48}
      />
      <Path
        fill="url(#a)"
        d="M80 105.265 30 89.043l-12.687 18.81a2.499 2.499 0 0 0 1.3 3.777l44.988 14.615a2.496 2.496 0 0 0 2.842-.975l13.558-20.005Z"
        opacity={0.8}
      />
      <Path
        fill="url(#b)"
        d="m130 89.043-50 16.222 13.558 20.005a2.5 2.5 0 0 0 2.842.975l44.988-14.615a2.497 2.497 0 0 0 1.706-2.705 2.5 2.5 0 0 0-.406-1.072L130 89.043Z"
        opacity={0.8}
      />
      <Path
        fill="url(#c)"
        d="M67.894 55.76a2.5 2.5 0 0 0-2.84-.97l-44.99 14.617a2.495 2.495 0 0 0-1.3 3.775l11.232 16.655 50-16.277-12.102-17.8Z"
        opacity={0.64}
      />
      <Path
        fill="url(#d)"
        d="M141.232 72.388a2.487 2.487 0 0 0 .291-2.211 2.5 2.5 0 0 0-1.591-1.564l-44.99-14.617a2.5 2.5 0 0 0-2.84.97L80 72.766l50 16.277 11.232-16.655Z"
        opacity={0.64}
      />
      <Path
        fill="#C4CDD5"
        d="M130 89.043 80 72.766 30 89.043l50 16.223 50-16.223Z"
        opacity={0.48}
      />
      <Path
        fill="#C4CDD5"
        d="M80 88.902V73.334L30 89.612l50 16.222V88.902Z"
        opacity={0.48}
      />
      <Path
        fill="#919EAB"
        fillOpacity={0.12}
        d="M77.334 17.078c2.705-15.318 26.147-14.328 12.5 0h-12.5ZM77.334 20.958c2.705 15.315 26.147 14.328 12.5 0h-12.5Z"
      />
      <Path
        fill="#919EAB"
        fillOpacity={0.24}
        d="M69.956 79.945a21.904 21.904 0 0 1-2.81-2.17 2.5 2.5 0 0 0-3.38 3.683 26.681 26.681 0 0 0 3.455 2.672 2.5 2.5 0 0 0 2.734-4.184Zm-7.79-7.68a21.739 21.739 0 0 1-1.885-3.032 2.5 2.5 0 1 0-4.458 2.267 26.85 26.85 0 0 0 2.318 3.73 2.5 2.5 0 0 0 4.025-2.965ZM58.51 62.064a11.57 11.57 0 0 1 .442-2.922 2.498 2.498 0 0 0-2.687-3.174 2.5 2.5 0 0 0-2.12 1.796c-.387 1.36-.6 2.762-.636 4.175a2.501 2.501 0 0 0 5 .125Zm3.844-8.35a14.771 14.771 0 0 1 2.15-1.642 2.501 2.501 0 0 0-2.667-4.23 19.638 19.638 0 0 0-2.88 2.204 2.5 2.5 0 0 0 3.398 3.668Zm9.298-4.313a28.34 28.34 0 0 1 3.995-.44 2.503 2.503 0 0 0-.212-4.997c-1.65.07-3.215.248-4.693.52a2.5 2.5 0 1 0 .91 4.918Zm13.03-.147c2.132.025 4.117-.05 5.96-.212a2.5 2.5 0 0 0-.442-4.98 54.688 54.688 0 0 1-5.46.192 2.5 2.5 0 1 0-.058 5Zm14.177-1.745c2.476-.758 4.566-1.727 6.316-2.84a2.507 2.507 0 0 0 1.118-2.543 2.506 2.506 0 0 0-1.028-1.614 2.506 2.506 0 0 0-1.869-.414 2.5 2.5 0 0 0-.909.353 20.762 20.762 0 0 1-5.09 2.275 2.5 2.5 0 1 0 1.463 4.783Zm12.726-9.838a15.536 15.536 0 0 0 1.517-7.507 2.488 2.488 0 0 0-.814-1.732 2.492 2.492 0 0 0-1.8-.65 2.505 2.505 0 0 0-1.732.814 2.493 2.493 0 0 0-.649 1.8 10.531 10.531 0 0 1-1.025 5.1 2.5 2.5 0 0 0 1.21 3.244 2.502 2.502 0 0 0 3.293-1.069Zm-1.593-16.347c-1.822-2.463-4.295-4.178-6.99-4.73a2.5 2.5 0 0 0-2.869 1.965 2.503 2.503 0 0 0 1.864 2.935c1.555.318 2.925 1.385 3.978 2.808a2.499 2.499 0 0 0 4.434-2.096 2.498 2.498 0 0 0-.417-.882ZM77.108 21.543h16.74a2.5 2.5 0 1 0 0-5h-16.74a2.503 2.503 0 0 0 0 5Z"
      />
      <Defs>
        <LinearGradient
          id="a"
          x1={30.333}
          x2={37.321}
          y1={89.023}
          y2={135.256}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor="#F4F6F8" />
          <Stop offset={1} stopColor="#C4CDD5" />
        </LinearGradient>
        <LinearGradient
          id="b"
          x1={80.203}
          x2={132.912}
          y1={105.095}
          y2={135.41}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor="#F4F6F8" />
          <Stop offset={1} stopColor="#C4CDD5" />
        </LinearGradient>
        <LinearGradient
          id="c"
          x1={23.451}
          x2={30.994}
          y1={80.135}
          y2={44.579}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor="#F4F6F8" />
          <Stop offset={1} stopColor="#C4CDD5" />
        </LinearGradient>
        <LinearGradient
          id="d"
          x1={93.385}
          x2={96.082}
          y1={53.393}
          y2={97.28}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor="#F4F6F8" />
          <Stop offset={1} stopColor="#C4CDD5" />
        </LinearGradient>
      </Defs>
    </Svg>
  );
};
export {FolderEmptyMultiColor};
