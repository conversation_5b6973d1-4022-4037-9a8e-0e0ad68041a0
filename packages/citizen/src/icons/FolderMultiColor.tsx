import * as React from 'react';
import Svg, {Path} from 'react-native-svg';

const FolderMultiColor = ({
  color = '#FFCF5C',
  color2 = '#F9B552',
  size = 24,
  ...props
}: any) => {
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox={`0 0 40 35`}
      fill="none"
      {...props}>
      <Path
        fill={color2}
        d="M33.6 6.99H4.4a2 2 0 0 0-2 2v13.6a2 2 0 0 0 2 2h29.2a2 2 0 0 0 2-2V8.99a2 2 0 0 0-2-2Z"
      />
      <Path
        fill={color}
        d="M35.6 10.19H4.4a4.4 4.4 0 0 0-4.4 4.4v18a4.4 4.4 0 0 0 4.4 4.4h31.2a4.4 4.4 0 0 0 4.4-4.4v-18a4.4 4.4 0 0 0-4.4-4.4Z"
      />
      <Path
        fill={color}
        d="M15.6 2.99H4.4A4.4 4.4 0 0 0 0 7.39v6.4a4.4 4.4 0 0 0 4.4 4.4h11.2a4.4 4.4 0 0 0 4.4-4.4v-6.4a4.4 4.4 0 0 0-4.4-4.4Z"
      />
    </Svg>
  );
};
export {FolderMultiColor};
