import * as React from 'react';
import Svg, {Path} from 'react-native-svg';

const SVGCalendarAdd = ({color = 'white', size = 24, ...props}) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      d="M16.7502 3.56V2C16.7502 1.59 16.4102 1.25 16.0002 1.25C15.5902 1.25 15.2502 1.59 15.2502 2V3.5H8.75023V2C8.75023 1.59 8.41023 1.25 8.00023 1.25C7.59023 1.25 7.25023 1.59 7.25023 2V3.56C4.55023 3.81 3.24023 5.42 3.04023 7.81C3.02023 8.1 3.26023 8.34 3.54023 8.34H20.4602C20.7502 8.34 20.9902 8.09 20.9602 7.81C20.7602 5.42 19.4502 3.81 16.7502 3.56Z"
      fill={color}
    />
    <Path
      d="M20 9.84H4C3.45 9.84 3 10.29 3 10.84V17C3 20 4.5 22 8 22H12.93C13.62 22 14.1 21.33 13.88 20.68C13.68 20.1 13.51 19.46 13.51 19C13.51 15.97 15.98 13.5 19.01 13.5C19.3 13.5 19.59 13.52 19.87 13.57C20.47 13.66 21.01 13.19 21.01 12.59V10.85C21 10.29 20.55 9.84 20 9.84ZM9.21 17.71C9.11 17.8 9 17.87 8.88 17.92C8.76 17.97 8.63 18 8.5 18C8.37 18 8.24 17.97 8.12 17.92C8 17.87 7.89 17.8 7.79 17.71C7.61 17.52 7.5 17.26 7.5 17C7.5 16.94 7.51 16.87 7.52 16.81C7.53 16.74 7.55 16.68 7.58 16.62C7.6 16.56 7.63 16.5 7.67 16.44C7.7 16.39 7.75 16.34 7.79 16.29C7.89 16.2 8 16.13 8.12 16.08C8.36 15.98 8.64 15.98 8.88 16.08C9 16.13 9.11 16.2 9.21 16.29C9.25 16.34 9.3 16.39 9.33 16.44C9.37 16.5 9.4 16.56 9.42 16.62C9.45 16.68 9.47 16.74 9.48 16.81C9.49 16.87 9.5 16.94 9.5 17C9.5 17.26 9.39 17.52 9.21 17.71ZM9.21 14.21C9.11 14.3 9 14.37 8.88 14.42C8.76 14.47 8.63 14.5 8.5 14.5C8.37 14.5 8.24 14.47 8.12 14.42C7.99 14.37 7.89 14.3 7.79 14.21C7.61 14.02 7.5 13.76 7.5 13.5C7.5 13.24 7.61 12.98 7.79 12.79C7.89 12.7 8 12.63 8.12 12.58C8.36 12.48 8.64 12.48 8.88 12.58C9 12.63 9.11 12.7 9.21 12.79C9.25 12.84 9.3 12.89 9.33 12.94C9.37 13 9.4 13.06 9.42 13.12C9.45 13.18 9.47 13.24 9.48 13.3C9.49 13.37 9.5 13.44 9.5 13.5C9.5 13.76 9.39 14.02 9.21 14.21ZM12.71 14.21C12.52 14.39 12.27 14.5 12 14.5C11.87 14.5 11.74 14.47 11.62 14.42C11.49 14.37 11.39 14.3 11.29 14.21C11.11 14.02 11 13.76 11 13.5C11 13.44 11.01 13.37 11.02 13.3C11.03 13.24 11.05 13.18 11.08 13.12C11.1 13.06 11.13 13 11.17 12.94C11.21 12.89 11.25 12.84 11.29 12.79C11.66 12.42 12.33 12.42 12.71 12.79C12.75 12.84 12.79 12.89 12.83 12.94C12.87 13 12.9 13.06 12.92 13.12C12.95 13.18 12.97 13.24 12.98 13.3C12.99 13.37 13 13.44 13 13.5C13 13.76 12.89 14.02 12.71 14.21Z"
      fill={color}
    />
    <Path
      d="M21.83 16.17C20.27 14.61 17.73 14.61 16.17 16.17C14.61 17.73 14.61 20.27 16.17 21.83C17.73 23.39 20.27 23.39 21.83 21.83C23.39 20.27 23.39 17.73 21.83 16.17ZM21.07 19.56C20.94 19.7 20.75 19.78 20.54 19.78H19.8V20.56C19.8 20.77 19.72 20.95 19.58 21.09C19.44 21.23 19.26 21.31 19.05 21.31C18.64 21.31 18.3 20.97 18.3 20.56V19.78H17.55C17.14 19.78 16.8 19.45 16.8 19.03C16.8 18.62 17.14 18.28 17.55 18.28H18.3V17.57C18.3 17.16 18.63 16.82 19.05 16.82C19.46 16.82 19.8 17.16 19.8 17.57V18.28H20.54C20.96 18.28 21.29 18.62 21.29 19.03C21.29 19.24 21.21 19.43 21.07 19.56Z"
      fill={color}
    />
  </Svg>
);

export default SVGCalendarAdd;
