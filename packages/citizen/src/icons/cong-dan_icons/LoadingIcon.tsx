import React from 'react';
import Svg, {G, <PERSON>, Defs, ClipPath} from 'react-native-svg';

export const LoadingIconComponent: React.FC<{
  colors?: string[];
  width?: number;
  height?: number;
}> = ({colors = ['#366AE2'], width = 30, height = 31, ...props}) => {
  return (
    <Svg
      width={width}
      height={height}
      viewBox="0 0 30 31"
      fill="none"
      {...props}>
      <G clipPath="url(#clip0_1984_75498)">
        <Path
          d="M12.336.712c.558-.065 1.11-.19 1.67-.243h1.986c.581.059 1.152.183 1.733.255.823.207 1.658.379 2.445.716.594.166 1.123.492 1.675.759.7.42 1.411.83 2.052 1.339.71.51 1.31 1.144 1.922 1.766 1.116 1.256 2.01 2.702 2.703 4.236.188.593.453 1.168.588 1.778.094.415.217.83.3 1.244.064.563.188 1.12.24 1.683 0 .764.006 1.535-.005 2.299-.13.883-.23 1.766-.482 2.625-.135.693-.43 1.345-.647 2.014-.693 1.529-1.587 2.98-2.703 4.237-.318.32-.641.64-.953.96-1.257 1.137-2.709 2.05-4.255 2.75-.47.135-.905.366-1.38.467-.013-2.844 0-5.688-.007-8.532h-8.445c-.006 2.844.005 5.688-.006 8.532-.482-.1-.917-.332-1.381-.468-1.54-.7-2.992-1.612-4.256-2.75-.44-.467-.934-.888-1.322-1.404-.694-.811-1.252-1.724-1.78-2.654-.236-.504-.536-.984-.689-1.529-.176-.474-.364-.948-.47-1.451-.111-.51-.288-1.002-.329-1.523-.07-.45-.153-.895-.2-1.351 0-.764-.006-1.535.006-2.3.13-.882.23-1.765.482-2.624.135-.693.43-1.345.647-2.015C2.168 8 3.062 6.548 4.178 5.292c.559-.545 1.093-1.126 1.716-1.6A17.033 17.033 0 019.34 1.606c.587-.19 1.157-.456 1.763-.592.411-.095.817-.22 1.234-.302zm-1.793 2.334c-.61.172-1.163.498-1.728.776-.476.279-.952.551-1.375.907-.77.545-1.422 1.232-2.08 1.902a23.16 23.16 0 00-1.1 1.41c-.446.664-.799 1.38-1.146 2.104-.105.248-.164.515-.27.764-.417 1.037-.582 2.157-.729 3.259-.011.764 0 1.535-.006 2.299.141 1.167.324 2.346.776 3.437.3.954.8 1.83 1.31 2.678.489.663.97 1.339 1.564 1.902.247.243.482.503.735.74.682.563 1.358 1.144 2.145 1.547v-7.632c.688-.006 1.37 0 2.057 0v-3.626c-1.434-.006-2.874.006-4.308-.006-.976-1.013-1.986-1.997-2.962-3.004.523-.486 1.005-1.02 1.522-1.511.735.776 1.51 1.517 2.251 2.293 1.875.012 3.756 0 5.63.006v5.777h4.332v-5.777c1.881-.006 3.756.006 5.63-.006.694-.717 1.405-1.41 2.099-2.127.059-.047.106-.136.188-.142.488.491.976.983 1.463 1.481-.017.03-.052.083-.064.113-.964.965-1.922 1.925-2.874 2.903-1.44.012-2.874 0-4.308.006v3.626c.687.006 1.37 0 2.057 0v7.632c.4-.208.776-.445 1.122-.723a11.322 11.322 0 001.652-1.446c.64-.598 1.164-1.31 1.675-2.015.517-.847 1.01-1.724 1.316-2.678.447-1.066.618-2.222.77-3.365.006-.788.006-1.576 0-2.37-.111-.753-.2-1.517-.405-2.252-.135-.592-.394-1.143-.576-1.718-.341-.74-.711-1.476-1.164-2.157-.482-.646-.952-1.304-1.54-1.855a12.544 12.544 0 00-3.068-2.387c-.535-.261-1.052-.575-1.628-.735-1.116-.474-2.321-.676-3.514-.824-.688 0-1.37-.006-2.057.006-1.146.148-2.31.332-3.392.788z"
          fill={colors[0]}
        />
        <Path
          d="M11.166 5.582c.606-.006 1.217 0 1.822 0l-.158.593h4.343l-.159-.593c.606 0 1.217-.006 1.822 0 .465 1.15.9 2.323 1.358 3.472-.758.006-1.517.006-2.275 0-.076-.296-.123-.604-.24-.889-1.787.006-3.574.006-5.36 0-.112.285-.16.593-.242.89-.758 0-1.516.005-2.274 0 .464-1.156.893-2.323 1.363-3.473z"
          fill={colors[1] || colors[0]}
        />
      </G>
      <Defs>
        <ClipPath id="clip0_1984_75498">
          <Path fill="#fff" d="M0 0H30V30.3965H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
};
