import * as React from 'react';
import Svg, {
  Path,
  Defs,
  LinearGradient,
  Stop,
  SvgProps,
} from 'react-native-svg';

interface IconProps extends SvgProps {
  width?: number;
  height?: number;
  gradientColors?: {
    paint0?: string[];
    paint1?: string[];
    paint2?: string[];
    paint3?: string[];
  };
  opacity?: number;
}

export const CustomIcon: React.FC<IconProps> = ({
  width = 40,
  height = 40,
  gradientColors = {
    paint0: ['#8DB6FC', '#366AE2'], // Màu gradient mặc định cho icon đầu tiên
    paint1: ['#8DB6FC', '#366AE2'],
    paint2: ['#8DB6FC', '#366AE2'],
    paint3: ['#8DB6FC', '#366AE2'],
  },
  opacity = 0.4,
  ...props
}) => {
  return (
    <Svg
      width={width}
      height={height}
      viewBox="0 0 40 40"
      fill="none"
      {...props}>
      <Path
        opacity={opacity}
        d="M35 11.667v16.666c0 5-2.5 8.334-8.333 8.334H13.333C7.5 36.667 5 33.333 5 28.333V11.667c0-5 2.5-8.334 8.333-8.334h13.334C32.5 3.333 35 6.667 35 11.667z"
        fill="url(#paint0_linear)"
      />
      <Path
        d="M25.833 3.333v13.1c0 .734-.866 1.1-1.4.617l-3.866-3.567a.826.826 0 00-1.134 0l-3.866 3.567c-.534.5-1.4.117-1.4-.617v-13.1h11.666z"
        fill="url(#paint1_linear)"
      />
      <Path
        d="M29.167 24.583h-7.084a1.26 1.26 0 01-1.25-1.25c0-.683.567-1.25 1.25-1.25h7.084c.683 0 1.25.567 1.25 1.25a1.26 1.26 0 01-1.25 1.25z"
        fill="url(#paint2_linear)"
      />
      <Path
        d="M29.167 31.25H15A1.26 1.26 0 0113.75 30c0-.683.567-1.25 1.25-1.25h14.167c.683 0 1.25.567 1.25 1.25a1.26 1.26 0 01-1.25 1.25z"
        fill="url(#paint3_linear)"
      />
      <Defs>
        <LinearGradient
          id="paint0_linear"
          x1={5}
          y1={3.33333}
          x2={38.1492}
          y2={33.1676}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor={gradientColors.paint0?.[0]} />
          <Stop offset={1} stopColor={gradientColors.paint0?.[1]} />
        </LinearGradient>
        <LinearGradient
          id="paint1_linear"
          x1={14.1667}
          y1={3.33333}
          x2={27.891}
          y2={14.8171}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor={gradientColors.paint1?.[0]} />
          <Stop offset={1} stopColor={gradientColors.paint1?.[1]} />
        </LinearGradient>
        <LinearGradient
          id="paint2_linear"
          x1={20.8333}
          y1={22.0833}
          x2={22.0546}
          y2={26.7648}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor={gradientColors.paint2?.[0]} />
          <Stop offset={1} stopColor={gradientColors.paint2?.[1]} />
        </LinearGradient>
        <LinearGradient
          id="paint3_linear"
          x1={13.75}
          y1={28.75}
          x2={14.4835}
          y2={33.64}
          gradientUnits="userSpaceOnUse">
          <Stop stopColor={gradientColors.paint3?.[0]} />
          <Stop offset={1} stopColor={gradientColors.paint3?.[1]} />
        </LinearGradient>
      </Defs>
    </Svg>
  );
};
