import * as React from 'react';
import Svg, {Path} from 'react-native-svg';

type Props = {
  size?: number;
  color?: string;
} & React.ComponentProps<typeof Svg>;

function SvgDocumentDownload({size = 40, color = '#22C55E', ...props}: Props) {
  return (
    <Svg width={size} height={size} viewBox="0 0 40 40" fill="none" {...props}>
      <Path
        d="M34.166 16.983H29.35c-3.95 0-7.167-3.216-7.167-7.166V5c0-.917-.75-1.667-1.666-1.667H13.45c-5.133 0-9.284 3.334-9.284 9.284v14.766c0 5.95 4.15 9.284 9.284 9.284h13.1c5.133 0 9.283-3.334 9.283-9.284V18.65c0-.917-.75-1.667-1.666-1.667zm-13.7 9.317l-3.333 3.333a1.115 1.115 0 01-.416.267c-.15.067-.3.1-.467.1-.167 0-.317-.033-.467-.1a1.103 1.103 0 01-.367-.25c-.016-.017-.033-.017-.033-.033l-3.333-3.334a1.258 1.258 0 010-1.766 1.258 1.258 0 011.766 0L15 25.733V18.75c0-.683.566-1.25 1.25-1.25.683 0 1.25.567 1.25 1.25v6.983l1.2-1.2a1.257 1.257 0 011.767 0 1.257 1.257 0 010 1.767z"
        fill={color}
      />
      <Path
        d="M29.05 14.683c1.584.017 3.784.017 5.667.017.95 0 1.45-1.117.783-1.783-2.4-2.417-6.7-6.767-9.166-9.234-.684-.683-1.867-.216-1.867.734v5.816c0 2.434 2.067 4.45 4.583 4.45z"
        fill={color}
      />
    </Svg>
  );
}

export default SvgDocumentDownload;
