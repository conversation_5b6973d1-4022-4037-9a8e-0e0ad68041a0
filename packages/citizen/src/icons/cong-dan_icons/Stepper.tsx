import React from 'react';
import Svg, {Path, Defs, LinearGradient, Stop} from 'react-native-svg';

export const Stepper = ({size = 80, color = '#FF5630'}) => (
  <Svg width={size} height={size} viewBox="0 0 80 80" fill="none">
    <Path
      opacity={0.48}
      fillRule="evenodd"
      clipRule="evenodd"
      d="M25.6 40C25.6 44.4183 22.0183 48 17.6 48C13.1817 48 9.60001 44.4183 9.60001 40C9.60001 35.5817 13.1817 32 17.6 32C22.0183 32 25.6 35.5817 25.6 40ZM20 42.532C20 42.9773 19.7403 43.2 19.2209 43.2H16.1134C15.594 43.2 15.3343 42.9773 15.3343 42.532C15.3343 42.3154 15.397 42.1529 15.5224 42.0446C15.6478 41.9362 15.8448 41.8821 16.1134 41.8821H16.8388V38.6234L16.1224 39.0477C16.0209 39.1079 15.9164 39.1379 15.809 39.1379C15.6418 39.1379 15.4985 39.0642 15.3791 38.9168C15.2597 38.7693 15.2 38.6054 15.2 38.4248C15.2 38.1781 15.3045 37.9915 15.5134 37.8652L16.9463 37.0257C17.209 36.8752 17.4567 36.8 17.6896 36.8C17.9284 36.8 18.1209 36.8722 18.2672 37.0166C18.4134 37.1611 18.4866 37.3567 18.4866 37.6034V41.8821H19.2209C19.4896 41.8821 19.6866 41.9362 19.8119 42.0446C19.9373 42.1529 20 42.3154 20 42.532Z"
      fill="url(#paint0_linear)"
    />
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M70.4 40C70.4 45.5228 65.9228 50 60.4 50C54.8771 50 50.4 45.5228 50.4 40C50.4 34.4772 54.8771 30 60.4 30C65.9228 30 70.4 34.4772 70.4 40ZM63.7971 44.558C63.6025 44.7193 63.3062 44.8 62.908 44.8H57.9537C57.628 44.8 57.3701 44.7036 57.18 44.5109C56.99 44.3182 56.895 44.074 56.895 43.7782C56.895 43.3927 57.0488 43.0431 57.3565 42.7294L60.2341 39.758C60.8313 39.1395 61.1299 38.5703 61.1299 38.0504C61.1299 37.7457 61.0417 37.5126 60.8652 37.3513C60.6888 37.1899 60.4377 37.1092 60.1119 37.1092C59.8495 37.1092 59.6074 37.1473 59.3857 37.2235C59.164 37.2997 58.9084 37.4095 58.6188 37.5529L58.3474 37.6874C58.3112 37.7053 58.241 37.7434 58.137 37.8017C58.0329 37.8599 57.9402 37.9025 57.8587 37.9294C57.7773 37.9563 57.6913 37.9697 57.6008 37.9697C57.3746 37.9697 57.1846 37.8689 57.0307 37.6672C56.8769 37.4655 56.8 37.2258 56.8 36.9479C56.8 36.7507 56.8294 36.5894 56.8882 36.4639C56.947 36.3384 57.0579 36.2218 57.2208 36.1143C57.6461 35.8275 58.1234 35.6034 58.6528 35.442C59.1821 35.2807 59.7047 35.2 60.2205 35.2C60.8901 35.2 61.4783 35.3098 61.985 35.5294C62.4918 35.749 62.8831 36.0627 63.1591 36.4706C63.4351 36.8784 63.5731 37.3468 63.5731 37.8756C63.5731 38.4224 63.4622 38.9221 63.2405 39.3748C63.0189 39.8275 62.6411 40.3317 62.1072 40.8874L60.1798 42.837H62.908C63.3062 42.837 63.6025 42.9154 63.7971 43.0723C63.9916 43.2291 64.0889 43.4734 64.0889 43.805C64.0889 44.1457 63.9916 44.3966 63.7971 44.558Z"
      fill={color}
    />
    <Defs>
      <LinearGradient id="paint0_linear" x1="9.6" y1="32" x2="25.6" y2="48">
        <Stop stopColor={color} />
        <Stop offset={1} stopColor={color} />
      </LinearGradient>
    </Defs>
  </Svg>
);
