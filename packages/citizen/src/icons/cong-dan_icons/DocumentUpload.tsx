import * as React from 'react';
import Svg, {Path} from 'react-native-svg';

type SvgComponentProps = {
  size?: number;
  color?: string;
} & React.ComponentProps<typeof Svg>;

function SvgDocumentUpload({
  size = 40,
  color = '#366AE2',
  ...props
}: SvgComponentProps) {
  return (
    <Svg width={size} height={size} viewBox="0 0 40 40" fill="none" {...props}>
      <Path
        d="M34.166 16.983H29.35c-3.95 0-7.167-3.216-7.167-7.166V5c0-.917-.75-1.667-1.666-1.667H13.45c-5.133 0-9.284 3.334-9.284 9.284v14.766c0 5.95 4.15 9.284 9.284 9.284h13.1c5.133 0 9.283-3.334 9.283-9.284V18.65c0-.917-.75-1.667-1.666-1.667zm-14.95 5.567c-.25.25-.566.367-.883.367-.316 0-.633-.117-.883-.367l-1.2-1.2v6.983a1.26 1.26 0 01-1.25 1.25 1.26 1.26 0 01-1.25-1.25V21.35l-1.2 1.2a1.257 1.257 0 01-1.767 0 1.257 1.257 0 010-1.767l3.334-3.333c.116-.1.233-.183.366-.25.034-.017.083-.033.117-.05.1-.033.2-.05.316-.067h.134c.133 0 .266.034.4.084h.033c.133.05.267.15.367.25.017.016.033.016.033.033l3.334 3.333a1.257 1.257 0 010 1.767z"
        fill={color}
      />
      <Path
        d="M29.05 14.683c1.584.017 3.784.017 5.667.017.95 0 1.45-1.117.783-1.783-2.4-2.417-6.7-6.767-9.166-9.234-.684-.683-1.867-.216-1.867.734v5.816c0 2.434 2.067 4.45 4.583 4.45z"
        fill={color}
      />
    </Svg>
  );
}

export default SvgDocumentUpload;
