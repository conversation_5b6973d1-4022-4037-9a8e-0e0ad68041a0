import * as React from 'react';
import Svg, {<PERSON>lip<PERSON><PERSON>, Defs, G, Path} from 'react-native-svg';

const IMGFileMultiColor = ({
  color = '#22C55E',
  color2 = '#fff',
  size = 24,
  ...props
}: any) => {
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox={`0 0 40 40`}
      fill="none"
      {...props}>
      <G clipPath="url(#a)">
        <Path
          fill={color}
          d="M23.172 0a2 2 0 0 1 1.414.585l11.828 11.82A2 2 0 0 1 37 13.818v21.514C37 37.911 34.869 40 32.24 40H7.76C5.131 40 3 37.91 3 35.333V4.667C3 2.089 5.131 0 7.76 0h15.412Z"
        />
        <G filter="url(#b)">
          <Path
            fill={color2}
            fillOpacity={0.24}
            d="M35.155 12.138a.5.5 0 0 1-.355.852H29c-2.577 0-4.902-2.267-4.902-4.78V2.204a.5.5 0 0 1 .855-.352l10.202 10.286Z"
            shapeRendering="crispEdges"
          />
        </G>
        <Path
          fill={color2}
          fillRule="evenodd"
          d="M16.963 16h.074c1.504 0 2.682 0 3.601.124.94.126 1.683.39 2.265.973.583.582.847 1.325.973 2.266.124.918.124 2.096.124 3.6v.057c0 1.243 0 2.26-.068 3.089-.067.833-.206 1.528-.517 2.106a2.915 2.915 0 0 1-.512.688c-.582.583-1.325.847-2.266.973-.918.124-2.096.124-3.6.124h-.074c-1.504 0-2.682 0-3.601-.124-.94-.126-1.683-.39-2.265-.973-.517-.516-.784-1.159-.925-1.957-.14-.783-.165-1.758-.17-2.968C10 23.67 10 23.344 10 23.001v-.038c0-1.504 0-2.682.124-3.601.126-.94.39-1.683.973-2.265.582-.583 1.325-.847 2.266-.973.918-.124 2.096-.124 3.6-.124Zm-3.47 1.091c-.833.112-1.335.326-1.706.696-.37.372-.584.873-.696 1.706-.113.846-.114 1.959-.114 3.507l.001.974c.005 1.22.033 2.113.155 2.801.12.674.324 1.107.654 1.438.372.37.873.584 1.706.696.846.113 1.958.114 3.507.114 1.549 0 2.66-.001 3.507-.114.833-.112 1.335-.326 1.706-.696.137-.137.248-.288.341-.46.217-.402.341-.939.405-1.723.064-.785.064-1.763.064-3.03 0-1.549-.001-2.66-.114-3.507-.112-.833-.326-1.335-.696-1.706-.372-.37-.873-.584-1.706-.696-.846-.113-1.959-.114-3.507-.114-1.549 0-2.661.001-3.508.114Z"
          clipRule="evenodd"
        />
        <Path
          fill={color2}
          d="m23.023 23.833-.145-.02c-1.852-.256-3.547.707-4.41 2.158-1.113-2.816-4.054-4.737-7.34-4.265l-.148.02c-.003.39-.003.812-.003 1.274l.001.974c.005 1.22.033 2.113.155 2.801.12.674.324 1.107.654 1.438.372.37.873.584 1.706.696.846.113 1.958.114 3.507.114 1.549 0 2.66-.001 3.507-.114.833-.112 1.335-.326 1.706-.696.137-.137.248-.288.341-.46.217-.402.341-.939.405-1.723.05-.603.06-1.32.064-2.197ZM20.907 20.395a1.302 1.302 0 1 1-2.604 0 1.302 1.302 0 0 1 2.604 0Z"
        />
      </G>
      <Defs>
        <ClipPath id="a">
          <Path fill={color2} d="M0 0h40v40H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
};
export {IMGFileMultiColor};
