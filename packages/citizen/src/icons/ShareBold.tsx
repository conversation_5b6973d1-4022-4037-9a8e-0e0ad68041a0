import * as React from 'react';
import Svg, {Path} from 'react-native-svg';
const ShareBold = ({color = '#637381', size = 24, ...props}: any) => {
  return (
    <Svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      fill="none"
      viewBox="0 0 24 24"
      {...props}>
      <Path
        fill={color}
        fillRule="evenodd"
        d="M13.803 5.333c0-1.84 1.5-3.333 3.348-3.333A3.342 3.342 0 0 1 20.5 5.333c0 1.841-1.5 3.334-3.349 3.334a3.346 3.346 0 0 1-2.384-.994l-4.635 3.156c.13.643.066 1.31-.182 1.917l5.082 3.34a3.346 3.346 0 0 1 2.12-.753 3.343 3.343 0 0 1 3.348 3.334C20.5 20.507 19 22 17.151 22a3.34 3.34 0 0 1-3.348-3.333c0-.467.098-.93.289-1.356L9.05 14c-.61.53-1.393.823-2.202.821A3.34 3.34 0 0 1 3.5 11.487a3.34 3.34 0 0 1 3.348-3.333c1.064 0 2.01.493 2.623 1.261l4.493-3.059a3.318 3.318 0 0 1-.161-1.023Z"
        clipRule="evenodd"
      />
    </Svg>
  );
};
export {ShareBold};
