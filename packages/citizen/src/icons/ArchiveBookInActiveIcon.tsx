import * as React from 'react';
import Svg, {SvgProps, Path} from 'react-native-svg';
export const ArchiveBookInActiveIcon = (props: SvgProps) => (
  <Svg fill="none" {...props}>
    <Path
      stroke="#919EAB"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeMiterlimit={10}
      strokeWidth={1.5}
      d="M17.5 5.833v8.334c0 2.5-1.25 4.166-4.167 4.166H6.667c-2.917 0-4.167-1.666-4.167-4.166V5.833c0-2.5 1.25-4.166 4.167-4.166h6.666c2.917 0 4.167 1.666 4.167 4.166Z"
    />
    <Path
      stroke="#919EAB"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeMiterlimit={10}
      strokeWidth={1.5}
      d="M12.917 1.667v6.55c0 .366-.434.55-.7.308l-1.934-1.783a.413.413 0 0 0-.566 0L7.783 8.525a.418.418 0 0 1-.7-.308v-6.55h5.834ZM11.042 11.667h3.541M7.5 15h7.083"
    />
  </Svg>
);
