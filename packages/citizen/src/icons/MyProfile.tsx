import * as React from 'react';
import Svg, {SvgProps, Path} from 'react-native-svg';

// Tạo kiểu mở rộng cho props
interface CustomSvgProps extends SvgProps {
  fillColor?: string; // Thêm thuộc tính fillColor tùy chỉnh
}

export const Profile = ({
  fillColor = '#637381', // Gi<PERSON> trị mặc định
  ...props
}: CustomSvgProps) => (
  <Svg width={'1rem'} height={'1rem'} viewBox="-10 -8 42 42" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10 4h4c3.771 0 5.657 0 6.828 1.172C22 6.343 22 8.229 22 12c0 3.771 0 5.657-1.172 6.828C19.657 20 17.771 20 14 20h-4c-3.771 0-5.657 0-6.828-1.172C2 17.657 2 15.771 2 12c0-3.771 0-5.657 1.172-6.828C4.343 4 6.229 4 10 4zm3.25 5a.75.75 0 01.75-.75h5a.75.75 0 110 1.5h-5a.75.75 0 01-.75-.75zm1 3a.75.75 0 01.75-.75h4a.75.75 0 110 1.5h-4a.75.75 0 01-.75-.75zm1 3a.75.75 0 01.75-.75h3a.75.75 0 110 1.5h-3a.75.75 0 01-.75-.75zM11 9a2 2 0 11-4 0 2 2 0 014 0zm-2 8c4 0 4-.895 4-2s-1.79-2-4-2-4 .895-4 2 0 2 4 2z"
      fill={fillColor}
    />
  </Svg>
);
