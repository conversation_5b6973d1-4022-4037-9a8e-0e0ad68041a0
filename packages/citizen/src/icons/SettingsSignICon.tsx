import * as React from 'react';
import Svg, {SvgProps, G, Path, Defs, ClipPath} from 'react-native-svg';
/* SVGR has dropped some elements not supported by react-native-svg: filter */
export const SettingsSignICon = (props: SvgProps) => (
  <Svg fill="none" {...props}>
    <G clipPath="url(#a)">
      <Path
        fill="#22C55E"
        d="M20.772 0a2 2 0 0 1 1.414.585l10.528 10.52a2 2 0 0 1 .586 1.415V31.8c0 2.32-1.918 4.2-4.284 4.2H6.984C4.618 36 2.7 34.12 2.7 31.8V4.2C2.7 1.88 4.618 0 6.984 0h13.788Z"
      />
      <G filter="url(#b)">
        <Path
          fill="#fff"
          fillOpacity={0.24}
          d="M31.555 10.84a.5.5 0 0 1-.355.851h-5.1c-2.32 0-4.412-2.04-4.412-4.302V2.105a.5.5 0 0 1 .855-.352l9.012 9.086Z"
        />
      </G>
      <Path
        fill="#fff"
        fillRule="evenodd"
        d="M15.267 14.4h.066c1.354 0 2.414 0 3.241.111.846.114 1.515.352 2.04.876.524.524.761 1.192.875 2.04.111.826.111 1.886.111 3.24v.051c0 1.119 0 2.034-.06 2.78-.062.75-.187 1.375-.467 1.896-.123.229-.275.434-.46.62-.524.524-1.192.761-2.04.875-.826.111-1.886.111-3.24.111h-.066c-1.354 0-2.414 0-3.241-.111-.846-.114-1.515-.352-2.04-.876-.464-.465-.704-1.043-.831-1.762-.126-.704-.15-1.582-.154-2.67L9 20.7v-.034c0-1.354 0-2.414.111-3.241.114-.846.352-1.515.876-2.04.524-.524 1.193-.76 2.04-.875.826-.111 1.886-.111 3.24-.111Zm-3.124.982c-.749.101-1.2.293-1.534.627-.334.334-.526.785-.627 1.535-.102.761-.103 1.762-.103 3.156l.001.876c.005 1.098.03 1.902.14 2.522.108.607.292.996.589 1.293.334.334.785.526 1.535.627.761.102 1.762.103 3.156.103 1.394 0 2.395-.001 3.156-.103.75-.101 1.202-.293 1.536-.627.123-.123.223-.259.307-.414.195-.361.306-.844.364-1.55.057-.706.058-1.587.058-2.727 0-1.394-.001-2.395-.103-3.156-.101-.75-.293-1.202-.627-1.536-.334-.333-.785-.525-1.535-.626-.761-.102-1.762-.103-3.156-.103-1.394 0-2.395.001-3.157.103Z"
        clipRule="evenodd"
      />
      <Path
        fill="#fff"
        d="m20.72 21.45-.13-.017c-1.666-.231-3.192.635-3.968 1.941-1.003-2.535-3.65-4.263-6.608-3.838l-.132.018c-.003.35-.003.73-.003 1.146l.001.876c.005 1.098.03 1.902.14 2.522.108.607.292.996.589 1.293.334.334.785.526 1.535.627.761.102 1.762.103 3.156.103 1.394 0 2.395-.001 3.156-.103.75-.101 1.202-.293 1.536-.627.123-.123.223-.259.307-.414.195-.361.306-.844.364-1.55.044-.543.054-1.189.057-1.977ZM18.816 18.356a1.172 1.172 0 1 1-2.344 0 1.172 1.172 0 0 1 2.344 0Z"
      />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path fill="#fff" d="M0 0h36v36H0z" />
      </ClipPath>
    </Defs>
  </Svg>
);
