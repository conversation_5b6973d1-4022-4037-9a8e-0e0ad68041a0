import {useContext, useEffect, useState} from 'react';
import {NotificationContext} from '@ac-mobile/common';
import {useNavigation} from '@react-navigation/native';

// Custom hook to encapsulate noti dialog logic
export function useNotiDialog() {
  const {notiData, setNotiData} = useContext(NotificationContext);
  const [visible, setVisible] = useState(false);
  const navigation = useNavigation();

  useEffect(() => {
    if (
      notiData &&
      notiData.data &&
      notiData.data.notiData &&
      typeof notiData.data.notiData === 'string'
    ) {
      try {
        const parsed = JSON.parse(notiData.data.notiData);
        if (parsed.miniappAlias === 'CITIZEN') {
          setVisible(true);
        }
      } catch (e) {
        // ignore parse error
      }
    }
  }, [notiData]);

  const handleClose = () => {
    setVisible(false);
    setNotiData(null);
  };

  const handleConfirm = () => {
    setVisible(false);
    setNotiData(null);
    if (
      notiData &&
      notiData.data &&
      notiData.data.notiData &&
      typeof notiData.data.notiData === 'string'
    ) {
      try {
        const parsed = JSON.parse(notiData.data.notiData);
        const targetScreen =
          parsed.navigate || parsed.screenCode || 'DrawNumberBranchSelect';
        // @ts-ignore
        navigation.navigate(targetScreen);
      } catch (e) {
        // @ts-ignore
        navigation.navigate('DrawNumberBranchSelect');
      }
    }
  };

  return {
    visible,
    notiData,
    onClose: handleClose,
    onConfirm: handleConfirm,
  };
}
import React from 'react';
import {Dialog, Portal, Button, Text} from 'react-native-paper';

export type NotiDialogProps = {
  visible: boolean;
  notiData: any;
  onClose: () => void;
  onConfirm: () => void;
};

const NotiDialog = () => {
  const {visible, notiData, onClose, onConfirm} = useNotiDialog();
  return (
    <Portal>
      <Dialog visible={visible} onDismiss={onClose}>
        <Dialog.Title>
          {notiData?.notification?.title || 'Thông báo'}
        </Dialog.Title>
        <Dialog.Content>
          <Text style={{fontWeight: 'bold', marginBottom: 8}}>
            {notiData?.notification?.body || ''}
          </Text>
          <Text selectable style={{marginBottom: 8}}>
            {(() => {
              if (
                notiData &&
                notiData.data &&
                notiData.data.notiData &&
                typeof notiData.data.notiData === 'string'
              ) {
                try {
                  const parsed = JSON.parse(notiData.data.notiData);
                  return parsed.body || '';
                } catch (e) {
                  return notiData.data.notiData;
                }
              }
              return '';
            })()}
          </Text>
          <Text style={{fontSize: 12, color: '#888'}}>
            Raw: {notiData?.data?.notiData || ''}
          </Text>
        </Dialog.Content>
        <Dialog.Actions>
          <Button onPress={onClose}>Đóng</Button>
          <Button onPress={onConfirm}>Đi tới</Button>
        </Dialog.Actions>
      </Dialog>
    </Portal>
  );
};

export default NotiDialog;
