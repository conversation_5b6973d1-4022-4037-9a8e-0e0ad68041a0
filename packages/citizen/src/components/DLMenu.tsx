import {ReactNode, useState} from 'react';
import {TouchableOpacity} from 'react-native';
import {Menu} from 'react-native-paper';
import {MoreIos} from '../icons';
import React from 'react';
export type MenuItem = {
  name: string;
  icon: ReactNode;
  onPress: () => void;
  title: string;
  disable?: boolean;
};

const DLMenu = ({
  menu,
  icon,
  size = 24,
}: {
  menu: MenuItem[];
  icon?: ReactNode;
  size?: number;
}) => {
  const [visible, setVisible] = useState(false);
  const openMenu = () => setVisible(true);

  const closeMenu = () => setVisible(false);

  return (
    <Menu
      visible={visible}
      onDismiss={() => {
        closeMenu();
      }}
      style={{borderRadius: 20}}
      anchor={
        <TouchableOpacity onPress={openMenu}>
          {icon ? icon : <MoreIos size={size} />}
        </TouchableOpacity>
      }>
      {menu?.map(menuItem => (
        <Menu.Item
          disabled={menuItem.disable}
          key={menuItem.name}
          leadingIcon={() => {
            return menuItem.icon;
          }}
          onPress={() => {
            setVisible(false);
            menuItem.onPress();
          }}
          title={menuItem.title}
        />
      ))}
    </Menu>
  );
};
export default DLMenu;
