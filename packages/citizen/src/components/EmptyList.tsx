import {View, Text, StyleSheet} from 'react-native';

import {FolderEmptyMultiColor} from '../icons';
import React from 'react';
import {useTheme} from 'react-native-paper';

type FlexDirection = 'column' | 'row' | 'row-reverse' | 'column-reverse';

interface EmptyListProps {
  title?: string;
  subtitle?: string;
  iconSize?: number;
  flexDirection?: FlexDirection;
}

const EmptyList = ({
  title = 'Không có kết quả tìm kiếm',
  subtitle = 'Vui lòng thử lại với từ khoá khác',
  iconSize = 160,
  flexDirection = 'column',
}: EmptyListProps) => {
  const theme = useTheme();
  return (
    <View
      style={[
        styles.container,
        // {flexDirection, backgroundColor: theme.colors.background},
      ]}>
      <FolderEmptyMultiColor size={iconSize} />
      <View>
        <Text
          style={[
            styles.title,
            flexDirection === 'column'
              ? {marginTop: iconSize / 4, textAlign: 'center'}
              : {},
            {color: theme.colors.onBackground},
          ]}>
          {title}
        </Text>
        {subtitle.length ? (
          <Text
            style={[
              styles.subtitle,
              flexDirection === 'column' ? {textAlign: 'center'} : {},
              {color: theme.colors.onBackground},
            ]}>
            {subtitle}
          </Text>
        ) : null}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  title: {
    fontSize: 17,
    lineHeight: 26,
    fontWeight: '700',
  },
  subtitle: {
    marginTop: 4,
    fontSize: 14,
    lineHeight: 22,
    fontWeight: '400',
  },
});

export default EmptyList;
