export const viVN = {
  save: '<PERSON><PERSON><PERSON> nhận',
  selectSingle: 'Chọn ngày',
  selectMultiple: 'Chọn nhiều ngày',
  selectRange: 'Chọn khoảng',
  notAccordingToDateFormat: (inputFormat: any) =>
    `<PERSON>ịnh dạng ngày phải là ${inputFormat}`,
  mustBeHigherThan: (date: any) => `Phải sau ngày ${date}`,
  mustBeLowerThan: (date: any) => `Phải trước ngày ${date}`,
  mustBeBetween: (startDate: any, endDate: any) =>
    `Phải trong khoảng ${startDate} - ${endDate}`,
  dateIsDisabled: '<PERSON>à<PERSON> không hợp lệ',
  previous: 'Trước',
  next: 'Sau',
  typeInDate: 'Nhập ngày',
  pickDateFromCalendar: 'Chọn từ lịch',
  close: '<PERSON>ón<PERSON>',
};
