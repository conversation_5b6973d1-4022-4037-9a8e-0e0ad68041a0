import React, {useState, useMemo} from 'react';
import {Controller, useFormContext} from 'react-hook-form';
import {
  StyleSheet,
  View,
  TouchableOpacity,
  ScrollView,
  TextInput,
} from 'react-native';
import {Text} from 'react-native-paper';
import {Modal, Portal, Button, useTheme} from 'react-native-paper';
import {Dimensions} from 'react-native';

import {AnyObj} from '@ac-mobile/common';

import {normalizeVietnameseText} from '../../utils/textUtils';

export type SelectCustomOption<T = AnyObj> = {
  id: string | number;
  label: string;
  value: T;
};

type Props = {
  name: string;
  placeholder?: string;
  label?: string;
  required?: boolean;
  options: SelectCustomOption[];
  showSearch?: boolean;
};

export const RHFSelected = React.forwardRef<any, Props>(
  (
    {
      name,
      placeholder = 'Chọn dữ liệu',
      label,
      required = false,
      options,
      showSearch = true,
    },
    ref,
  ) => {
    const {
      control,
      formState: {errors},
      setValue,
    } = useFormContext();
    const theme = useTheme();
    const [visible, setVisible] = useState(false);
    const [search, setSearch] = useState('');

    React.useImperativeHandle(ref, () => ({
      open: () => setVisible(true),
    }));

    // Filter options by search
    const filteredOptions = useMemo(() => {
      if (!search.trim()) {
        return options;
      }
      const normalizedQuery = normalizeVietnameseText(search.toLowerCase());
      return options.filter(option => {
        const normalizedLabel = normalizeVietnameseText(
          (option.label || '').toLowerCase(),
        );
        return normalizedLabel.includes(normalizedQuery);
      });
    }, [search, options]);

    return (
      <Controller
        name={name}
        control={control}
        render={({field: {value}}) => (
          <View>
            {label && (
              <View style={styles.label}>
                <Text
                  variant="labelLarge"
                  style={{color: theme.colors.onSurface}}>
                  {label}
                  {required && (
                    <Text
                      variant="labelLarge"
                      style={[styles.required, {color: theme.colors.error}]}>
                      *
                    </Text>
                  )}
                </Text>
              </View>
            )}
            <TouchableOpacity
              style={[
                styles.selector,
                {
                  borderColor:
                    errors[name] && !visible
                      ? theme.colors.error
                      : theme.colors.outline || '#ccc',
                  backgroundColor: theme.colors.surface,
                },
              ]}
              onPress={() => setVisible(true)}>
              <Text
                variant="bodyLarge"
                style={[
                  styles.selectorText,
                  {
                    color: errors[name]
                      ? theme.colors.error
                      : theme.colors.onSurface,
                  },
                ]}>
                {errors[name]
                  ? typeof errors[name]?.message === 'string'
                    ? errors[name]?.message
                    : placeholder
                  : value && value.label !== ''
                  ? value.label
                  : placeholder}
              </Text>
            </TouchableOpacity>
            <Portal>
              <Modal
                visible={visible}
                onDismiss={() => setVisible(false)}
                contentContainerStyle={[
                  styles.modal,
                  {
                    backgroundColor: theme.colors.background,
                    maxHeight: Dimensions.get('window').height * 0.8,
                  },
                ]}>
                {showSearch && (
                  <View style={styles.searchContainer}>
                    <TextInput
                      style={[
                        styles.searchInput,
                        {
                          backgroundColor: theme.colors.surfaceVariant,
                          color: theme.colors.onSurface,
                          borderColor: theme.colors.outline || '#ccc',
                        },
                      ]}
                      placeholder="Tìm kiếm..."
                      placeholderTextColor={theme.colors.onSurfaceVariant}
                      value={search}
                      onChangeText={setSearch}
                    />
                  </View>
                )}
                <ScrollView>
                  {filteredOptions.map(option => (
                    <Button
                      key={option.id}
                      onPress={() => {
                        setValue(name, option, {
                          shouldValidate: true,
                          shouldDirty: true,
                        });
                        setVisible(false);
                        setSearch('');
                      }}
                      style={[
                        styles.optionBtn,
                        {backgroundColor: theme.colors.surfaceVariant},
                      ]}
                      textColor={theme.colors.onSurface}>
                      {option.label}
                    </Button>
                  ))}
                  {filteredOptions.length === 0 && (
                    <Text
                      variant="bodyLarge"
                      style={[
                        styles.noResult,
                        {color: theme.colors.onSurfaceVariant},
                      ]}>
                      Không tìm thấy kết quả
                    </Text>
                  )}
                </ScrollView>
                <Button
                  onPress={() => {
                    setVisible(false);
                    setSearch('');
                  }}
                  mode="outlined"
                  style={[
                    styles.cancelBtn,
                    {backgroundColor: theme.colors.surfaceVariant},
                  ]}
                  textColor={theme.colors.onSurface}>
                  Đóng
                </Button>
              </Modal>
            </Portal>
            {errors[name] && (
              <Text
                variant="bodySmall"
                style={[styles.errorText, {color: theme.colors.error}]}>
                {typeof errors[name]?.message === 'string'
                  ? errors[name]?.message
                  : ''}
              </Text>
            )}
          </View>
        )}
      />
    );
  },
);

const styles = StyleSheet.create({
  label: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selector: {
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    marginVertical: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  selectorText: {
    fontSize: 16,
  },
  required: {},
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  modal: {
    margin: 24,
    borderRadius: 12,
    padding: 16,
    elevation: 5,
  },
  searchContainer: {
    marginBottom: 8,
  },
  searchInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 10,
    fontSize: 16,
  },
  noResult: {
    textAlign: 'center',
    marginVertical: 16,
    fontSize: 16,
  },
  optionBtn: {
    marginVertical: 4,
    borderRadius: 8,
  },
  cancelBtn: {
    marginTop: 12,
    borderRadius: 8,
  },
});
