import React from 'react';
import {StyleSheet, Text, View, TouchableOpacity} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {HomeStackParamList} from '../navigation/HomeNavigator';
import {getStatusText} from '../utils/fn';
import {
  getTitle,
  getShortCode,
  getSubmissionDate,
  ShareItem as ShareItemType,
} from '../utils/shareItemUtils';
import {useTheme} from 'react-native-paper';

type NavigationProp = NativeStackNavigationProp<HomeStackParamList>;

interface ShareItemProps {
  item: ShareItemType;
}

export const ShareItem: React.FC<ShareItemProps> = ({item}) => {
  const navigation = useNavigation<NavigationProp>();
  const theme = useTheme();
  const statusInfo = getStatusText(String(item.progress), theme);

  const handlePress = () => {
    navigation.navigate('ShareDetailScreen', {
      id: item.id,
      detail: item,
    });
  };

  return (
    <TouchableOpacity
      style={[
        styles.cardContainer,
        {
          backgroundColor: theme.colors.surface,
          shadowColor: theme.colors.outline,
        },
      ]}
      onPress={handlePress}>
      <View style={styles.cardContent}>
        <View style={styles.cardHeader}>
          <Text
            style={[styles.titleText, {color: theme.colors.onSurface}]}
            numberOfLines={2}>
            {getTitle(item)}
          </Text>
        </View>
        <View style={styles.spaceBetween}>
          <View style={styles.cardDetails}>
            {getShortCode(item) ? (
              <Text
                style={[
                  styles.detailText,
                  {color: theme.colors.onSurfaceVariant},
                ]}>
                Mã hồ sơ: {getShortCode(item)}
              </Text>
            ) : null}

            {getSubmissionDate(item) ? (
              <Text
                style={[
                  styles.detailText,
                  {color: theme.colors.onSurfaceVariant},
                ]}>
                Ngày nộp: {getSubmissionDate(item)}
              </Text>
            ) : null}
          </View>
          <View>
            <View
              style={[
                styles.statusBadge,
                {backgroundColor: statusInfo.background},
              ]}>
              <Text style={[styles.statusText, {color: statusInfo.color}]}>
                {statusInfo.label}
              </Text>
            </View>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    borderRadius: 8,
    marginVertical: 4,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  cardContent: {
    padding: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  titleText: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    marginRight: 12,
    lineHeight: 20,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    minWidth: 80,
    alignItems: 'center',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
  cardDetails: {
    gap: 4,
  },
  detailText: {
    fontSize: 14,
    lineHeight: 18,
  },
  spaceBetween: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
});
