import React from 'react';
import {StyleSheet, SafeAreaView} from 'react-native';
import {MD3Colors, Text} from 'react-native-paper';
import {LoadingAnimation} from './LoadingAnimation';

const SplashScreen = () => {
  return (
    <SafeAreaView style={styles.container}>
      <LoadingAnimation />
      <Text style={styles.text}>Đang tải...</Text>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    paddingHorizontal: 32,
    fontSize: 18,
    color: MD3Colors.primary20,
    textAlign: 'center',
    marginTop: -12,
  },
});

export default SplashScreen;
