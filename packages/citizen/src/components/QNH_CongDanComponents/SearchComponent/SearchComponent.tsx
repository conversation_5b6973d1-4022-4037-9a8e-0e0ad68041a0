import React, {useState} from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {CardComponent} from '../CardComponent/CardComponent';
import {SearchInputComponent} from '../SearchInputComponent/SearchInputComponent';
import {textStyles} from '../../../styles/QNH_textStyle';

export const SearchComponent = () => {
  const [search, setSearch] = useState<string>('');

  const handleChangeSearch = (e: string) => {
    setSearch(e);
  };

  return (
    <View>
      <CardComponent
        title="Tì<PERSON> kiếm thủ tục hành chính"
        cardActions={
          <View className="w-full flex flex-row justify-start px-[16px] pt-[12px] pb-[16px]">
            <Text className={`${textStyles.chipLabel}`}>
              G<PERSON><PERSON> <PERSON> từ khóa: Thủ tục Trích lục hộ tịch
            </Text>
          </View>
        }>
        <View style={styles.content}>
          <SearchInputComponent
            // search={search}
            // setSearch={handleChangeSearch}
            placeholder="Thủ tục bạn muốn thực hiện?"
            onPress={() => console.log('')}
          />
        </View>
      </CardComponent>
    </View>
  );
};

const styles = StyleSheet.create({
  content: {
    paddingTop: 16,
  },
});
