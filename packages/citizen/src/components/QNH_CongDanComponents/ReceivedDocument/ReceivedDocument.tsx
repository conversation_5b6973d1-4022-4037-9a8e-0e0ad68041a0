import React, {useCallback} from 'react';
import {View} from 'react-native';
import {CardComponent} from '../CardComponent/CardComponent';
import {LayoutComponent} from '../LayoutComponent/LayoutComponent';
import {useNavigation} from '@react-navigation/native';
import {useDocuments} from '../../../hooks/useDocuments';
import DocumentItemComponent from '../../../screens/document/components/DocumentItem';
import {DocumentItem} from '../../../requester-biz-service/apis/documents-api';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {HomeStackParamList} from '../../../navigation/HomeNavigator';

type NavigationProp = NativeStackNavigationProp<HomeStackParamList>;

export const ReceivedDocument = React.memo(() => {
  const nav = useNavigation<NavigationProp>();

  // Fetch only 3 received documents (uploadBy: 'OTHER')
  const {data} = useDocuments({
    uploadBy: 'OTHER',
    enabled: true,
  });

  // Flatten and get 3 items
  const items = React.useMemo(() => {
    if (!data?.pages) return [];
    const flattened = data.pages.flatMap((page: any) => {
      if (page?.data?.data?.items) return page.data.data.items;
      if (page?.data?.items) return page.data.items;
      return [];
    });
    return flattened.slice(0, 3);
  }, [data]);

  const handleOnPress = () => {
    nav.navigate('DocumentsScreen');
  };

  const handleDocumentPress = useCallback(
    (item: DocumentItem) => {
      nav.navigate('DocumentDetailScreen', {uri: item.uri, item});
    },
    [nav],
  );

  return (
    <View>
      <CardComponent title="Giấy tờ được cấp gần đây" onPress={handleOnPress}>
        <LayoutComponent type="vertical">
          {items.map((item, index) => (
            <DocumentItemComponent
              key={item.id || index}
              item={item}
              onPress={() => handleDocumentPress(item)}
            />
          ))}
        </LayoutComponent>
      </CardComponent>
    </View>
  );
});
