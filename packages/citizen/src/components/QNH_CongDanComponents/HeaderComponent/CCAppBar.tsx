import React, {memo} from 'react';
import {Appbar, Searchbar, Text, useTheme, Menu} from 'react-native-paper';
import {IconSource} from 'react-native-paper/lib/typescript/components/Icon';

import {useNavigation} from '@react-navigation/native';
import {StyleSheet, View} from 'react-native';
import {useDeviceStore} from '../../../stores/device.store';
export type CCAppBarMenuItem = {
  icon: IconSource | React.ReactNode;
  text: string;
  onPress: () => void;
};
export type CCAppBarProp = {
  isBack?: boolean;
  label?: React.ReactNode;
  iconsAction?: {
    icon: IconSource;
    onPress: () => void;
  }[];
  backgroundColor?: string;
  icons?: React.ReactNode;
  showSearch?: boolean;
  searchPlaceholder?: string;
  searchValue?: string;
  onSearchChange?: (query: string) => void;
  menuConfig?: CCAppBarMenuItem[];
  onBackPressed?: () => void;
};
export const CCAppBar = memo(
  ({
    isBack,
    label,
    iconsAction,
    backgroundColor,
    showSearch,
    searchPlaceholder,
    searchValue,
    onSearchChange,
    menuConfig,
    onBackPressed,
  }: CCAppBarProp) => {
    // COMMENT: store
    const {disableBack} = useDeviceStore();

    // COMMENT: hook
    const nav = useNavigation<any>();
    const theme = useTheme();
    const styles = themedStyles();
    // Use secondaryContainer as default background, onSecondaryContainer for text
    const bgColor = backgroundColor ?? theme.colors.secondaryContainer;
    let textColor = theme.colors.onSecondaryContainer;
    if (bgColor === theme.colors.primary) {
      textColor = theme.colors.onPrimary;
    } else if (bgColor === theme.colors.surface) {
      textColor = theme.colors.onSurface;
    } else if (bgColor === theme.colors.background) {
      textColor = theme.colors.onBackground;
    } else if (bgColor === theme.colors.secondary) {
      textColor = theme.colors.onSecondary;
    } else if (bgColor === theme.colors.secondaryContainer) {
      textColor = theme.colors.onSecondaryContainer;
    }
    const [menuVisible, setMenuVisible] = React.useState(false);
    const [menuAnchor, setMenuAnchor] = React.useState<{x: number; y: number}>({
      x: 0,
      y: 0,
    });
    const menuButtonRef = React.useRef<any>(null);

    const handleMenuOpen = () => {
      if (menuButtonRef.current && menuButtonRef.current.measureInWindow) {
        menuButtonRef.current.measureInWindow(
          (x: number, y: number, w: number, h: number) => {
            setMenuAnchor({x: x + w / 2, y: y + h});
            setMenuVisible(true);
          },
        );
      } else {
        setMenuAnchor({x: 0, y: 0});
        setMenuVisible(true);
      }
    };

    return (
      <Appbar.Header style={{backgroundColor: bgColor}} mode="small">
        {isBack ? (
          <Appbar.BackAction
            onPress={
              onBackPressed ? onBackPressed : () => !disableBack && nav.goBack()
            }
            color={disableBack ? '#ccc' : textColor}
          />
        ) : (
          <></>
        )}
        {showSearch ? (
          <View style={styles.searchContainer}>
            <Searchbar
              placeholder={searchPlaceholder || 'Tìm kiếm...'}
              value={searchValue || ''}
              onChangeText={onSearchChange}
              style={[
                styles.searchBar,
                {backgroundColor: theme.colors.surface},
              ]}
              inputStyle={[styles.searchInput, {color: theme.colors.onSurface}]}
              iconColor={theme.colors.onSecondaryContainer}
              placeholderTextColor={theme.colors.onSecondaryContainer}
              theme={{colors: {primary: theme.colors.secondary}}}
              icon="magnify"
              clearIcon="close-circle"
              selectionColor={theme.colors.secondary + '33'}
              blurOnSubmit={true}
              autoCapitalize="none"
            />
          </View>
        ) : (
          <Appbar.Content
            title={
              <Text
                numberOfLines={1}
                style={[styles.title, {color: textColor}]}>
                {label}
              </Text>
            }
          />
        )}
        {iconsAction?.map((e, i) => {
          return (
            <Appbar.Action
              icon={e.icon}
              onPress={e.onPress}
              key={i}
              color={textColor}
            />
          );
        })}
        {menuConfig && menuConfig.length > 0 && (
          <Appbar.Action
            ref={menuButtonRef}
            icon="dots-vertical"
            onPress={() => !disableBack && handleMenuOpen()}
            color={disableBack ? '#ccc' : textColor}
          />
        )}
        <Menu
          visible={menuVisible}
          onDismiss={() => setMenuVisible(false)}
          anchor={{
            x: isNaN(menuAnchor.x) ? 0 : menuAnchor.x,
            y: isNaN(menuAnchor.y) ? 56 : menuAnchor.y,
          }}>
          {menuConfig?.map((item, idx) => {
            if (React.isValidElement(item.icon)) {
              return (
                <CustomMenuItem
                  key={idx}
                  icon={item.icon}
                  text={item.text}
                  onPress={() => {
                    setMenuVisible(false);
                    item.onPress();
                  }}
                />
              );
            }
            return (
              <Menu.Item
                key={idx}
                onPress={() => {
                  setMenuVisible(false);
                  item.onPress();
                }}
                title={item.text}
                leadingIcon={item.icon as IconSource}
              />
            );
          })}
        </Menu>
      </Appbar.Header>
    );
  },
);
function CustomMenuItem({
  icon,
  text,
  onPress,
}: {
  icon: React.ReactNode;
  text: string;
  onPress: () => void;
}) {
  return (
    <Menu.Item
      onPress={onPress}
      title={
        <View style={customMenuStyles.row}>
          {icon}
          <Text style={customMenuStyles.text}>{text}</Text>
        </View>
      }
    />
  );
}
const customMenuStyles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  text: {
    marginLeft: 8,
  },
});
const themedStyles = () =>
  StyleSheet.create({
    title: {
      marginLeft: 8,
      fontSize: 20,
      fontWeight: 'bold',
    },
    searchContainer: {
      flex: 1,
      marginHorizontal: 8,
      alignItems: 'center',
    },
    searchBar: {
      elevation: 0,
      height: 36,
      borderRadius: 20,
      width: '100%',
      shadowColor: '#000',
      shadowOffset: {width: 0, height: 1},
      shadowOpacity: 0.1,
      shadowRadius: 1,
    },
    searchInput: {
      fontSize: 15,
      paddingVertical: 0,
      minHeight: 36,
      includeFontPadding: false,
      textAlignVertical: 'center',
      marginLeft: -8,
    },
  });
