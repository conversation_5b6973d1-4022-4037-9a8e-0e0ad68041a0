import React from 'react';
import {View, Text, StyleSheet, Pressable} from 'react-native';
import Skeleton from '../CustomShimmer/CustomShimmerComponent';
import {textStyles} from '../../../styles/QNH_textStyle';

interface CommonProcedureItem {
  id: string;
  title: string;
  onPress: (id: string, name: string) => void;
  icon?: React.ReactNode;
  isLoading?: boolean;
  direction?: 'vertical' | 'horizontal';
}

const CommonProcedureShimmer: React.FC<{
  direction?: 'vertical' | 'horizontal';
}> = ({direction = 'vertical'}) => {
  const isVertical = direction === 'vertical';

  return (
    <View
      style={[
        styles.loadingContainer,
        isVertical ? styles.vertical : styles.horizontal,
      ]}>
      <Skeleton
        style={[
          styles.skeletonIcon,
          !isVertical && {marginBottom: 0, marginRight: 12},
        ]}
      />
      <View
        style={[
          styles.skeletonTextContainer,
          !isVertical && {alignItems: 'flex-start'},
        ]}>
        <Skeleton style={styles.skeletonTitle} />
        <Skeleton style={[styles.skeletonTitle, styles.skeletonLine]} />
      </View>
    </View>
  );
};

export const CommonProcedureItem = React.memo(
  ({
    id,
    title,
    onPress,
    icon,
    isLoading = false,
    direction = 'vertical',
  }: CommonProcedureItem) => {
    const isVertical = direction === 'vertical';

    return (
      <Pressable
        style={({pressed}) => [
          styles.card,
          isVertical ? styles.vertical : styles.horizontal,
          pressed && !isLoading && styles.cardPressed,
        ]}
        onPress={() => !isLoading && onPress(id, title)}
        accessible
        accessibilityRole="button">
        {isLoading ? (
          <CommonProcedureShimmer direction={direction} />
        ) : (
          <>
            <View
              style={[
                styles.iconContainer,
                !isVertical && {marginBottom: 0, marginRight: 12},
              ]}>
              {icon}
            </View>
            <Text
              numberOfLines={2}
              ellipsizeMode="tail"
              className={textStyles.subTitle2}
              style={[
                {
                  color: '#212B36',
                  textAlign: isVertical ? 'center' : 'left',
                  flexShrink: 1,
                  flex: 1,
                },
              ]}>
              {title}
            </Text>
          </>
        )}
      </Pressable>
    );
  },
);

const styles = StyleSheet.create({
  card: {
    borderRadius: 8,
    backgroundColor: '#fff',
    padding: 8,
  },
  vertical: {
    width: 108,
    height: 120,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'column',
  },
  horizontal: {
    width: '100%',
    height: 80,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  cardPressed: {
    backgroundColor: '#f0f0f0',
  },
  iconContainer: {
    marginBottom: 8,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: '100%',
  },
  skeletonIcon: {
    width: 40,
    height: 40,
    borderRadius: 8,
    marginBottom: 8,
  },
  skeletonTextContainer: {
    width: '100%',
    alignItems: 'center',
  },
  skeletonTitle: {
    width: '60%',
    height: 12,
    borderRadius: 4,
  },
  skeletonLine: {
    marginTop: 6,
    width: '50%',
  },
});
