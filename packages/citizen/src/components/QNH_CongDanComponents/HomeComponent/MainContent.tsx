import React, {useRef, useCallback} from 'react';
import {ScrollView, StyleSheet, View, Platform, Keyboard} from 'react-native';
import {useTheme} from 'react-native-paper';
import {DrawNumberComponent} from '../DrawNumberComponent/DrawNumberComponent';
import {NotificationDrawNumber} from '../DrawNumberComponent/NotificationDrawNumber';
import {useDrawNumber} from '../../../stores/QNHCongDan/useDrawNumberStore/useDrawNumber';
import {NavigationProp, useNavigation} from '@react-navigation/native';
import {MainStackParamList} from '../../../navigation/MainNavigator';
import {
  BottomSheetModal,
  BottomSheetScrollView,
  BottomSheetBackdrop,
} from '@gorhom/bottom-sheet';
import {BottomSheetDefaultBackdropProps} from '@gorhom/bottom-sheet/lib/typescript/components/bottomSheetBackdrop/types';
import {DocumentSubmitted} from '../DocumentSubmitted/DocumentSubmitted';
import {SearchInputComponent} from '../SearchInputComponent/SearchInputComponent';
import {ReceivedDocument} from '../ReceivedDocument/ReceivedDocument';
import {CommonTemplates} from '../CommonTemplate';

export const MainContent = () => {
  const theme = useTheme();
  const nav = useNavigation<NavigationProp<MainStackParamList>>();
  const {getBranchOptions} = useDrawNumber();
  const bottomSheetRef = useRef<BottomSheetModal>(null);

  const handleShowNotificationBottomSheet = () => {
    bottomSheetRef.current?.present();
  };

  const handleDrawNumber = async () => {
    await getBranchOptions();
    nav.navigate('DrawNumberBranchSelect');
  };

  const renderBackdrop = useCallback(
    (
      props: React.JSX.IntrinsicAttributes & BottomSheetDefaultBackdropProps,
    ) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.3}
      />
    ),
    [],
  );

  // Custom handle for theme-reactive drag indicator
  const renderHandle = useCallback(
    () => (
      <View
        style={{
          alignItems: 'center',
          paddingTop: 8,
          paddingBottom: 4,
          backgroundColor: theme.colors.surface,
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
        }}>
        <View
          style={{
            width: 40,
            height: 5,
            // borderRadius: 3,
            backgroundColor: theme.colors.onSurfaceVariant,
          }}
        />
      </View>
    ),
    [theme.colors.surface, theme.colors.onSurfaceVariant],
  );

  return (
    <View style={styles.container}>
      {/* Content */}
      <ScrollView
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}>
        {/* Tìm kiếm */}
        <SearchInputComponent onPress={() => nav.navigate('CommonProcedure')} />

        {/* Lấy số */}
        <DrawNumberComponent
          onHandleShowNotificationBottomSheet={
            handleShowNotificationBottomSheet
          }
        />

        {/* Thủ tục phổ biến */}
        <CommonTemplates />

        {/* Hồ sơ nộp gần đây */}
        <DocumentSubmitted />

        {/* Giấy tờ mới được cấp */}
        <ReceivedDocument />

        {/* Search Component */}
        {/* Bạn có thể thêm nội dung tại đây */}
      </ScrollView>

      {/* Bottom Sheet - Moved outside ScrollView */}
      <BottomSheetModal
        ref={bottomSheetRef}
        snapPoints={[]}
        enablePanDownToClose
        backdropComponent={renderBackdrop}
        android_keyboardInputMode="adjustResize"
        keyboardBehavior={Platform.OS === 'ios' ? 'extend' : 'interactive'}
        keyboardBlurBehavior="restore"
        onDismiss={() => {
          Keyboard.dismiss();
        }}
        style={{backgroundColor: theme.colors.surface}}
        backgroundStyle={{backgroundColor: theme.colors.surface}}
        handleComponent={renderHandle}>
        <BottomSheetScrollView
          keyboardShouldPersistTaps="handled"
          contentContainerStyle={[
            styles.contentContainer,
            {backgroundColor: theme.colors.surface},
          ]}>
          <View
            style={[
              styles.bottomSheetContent,
              {backgroundColor: theme.colors.surface},
            ]}>
            <NotificationDrawNumber
              onHandleShowButtonSheetBranchList={handleDrawNumber}
            />
          </View>
        </BottomSheetScrollView>
      </BottomSheetModal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 16,
  },
  bottomSheetContent: {
    width: '100%',
    paddingBottom: 16,
  },
});
