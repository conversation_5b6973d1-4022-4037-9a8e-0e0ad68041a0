import React from 'react';
import {ActivityIndicator, StyleSheet, View} from 'react-native';
import LoadingIconComponent from '../../../icons/cong-dan_icons/LoadingIcon';

interface LoadingProps {
  isVisible: boolean; // <PERSON><PERSON><PERSON> thị hoặc ẩn component loading
  logoSize?: number; // <PERSON><PERSON><PERSON> thước logo
  loaderColor?: string; // <PERSON>àu của loader
  loaderSize?: number; // Kích thước loader (tỉ lệ lớn hơn logo)
}

export const LoadingComponent = React.memo(
  ({
    isVisible,
    logoSize = 30,
    loaderSize = 60,
    loaderColor = '#366AE2',
  }: LoadingProps) => {
    if (!isVisible) return null;

    return (
      <View style={styles.container}>
        <View style={styles.loaderContainer}>
          {/* ActivityIndicator lớn hơn logo */}
          <ActivityIndicator
            size="large"
            color={loaderColor}
            style={[
              styles.loader,
              {
                width: loaderSize,
                height: loaderSize,
                borderRadius: loaderSize / 2,
              },
            ]}
          />
          {/* Logo ở giữa */}
          <View
            style={[styles.logoContainer, {width: logoSize, height: logoSize}]}>
            <LoadingIconComponent
              width={logoSize}
              height={logoSize}
              colors={[loaderColor]}
            />
          </View>
        </View>
      </View>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  loaderContainer: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loader: {
    position: 'absolute', // Xoay vòng loader xung quanh logo
    transform: [{scale: 1.5}], // Phóng to ActivityIndicator
  },
  logoContainer: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1, // Logo nằm trên ActivityIndicator
  },
});
