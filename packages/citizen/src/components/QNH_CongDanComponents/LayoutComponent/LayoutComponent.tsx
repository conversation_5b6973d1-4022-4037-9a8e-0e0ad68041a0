import React from 'react';
import {StyleSheet, View} from 'react-native';

interface LayoutComponentProps {
  type: 'vertical' | 'horizontal' | 'grid';
  children: React.ReactNode; // Children là các ReactNode
  columns?: number; // Chỉ áp dụng cho kiểu 'grid'
  spacing?: number; // <PERSON><PERSON>ảng cách gi<PERSON>a các phần tử
}

export const LayoutComponent: React.FC<LayoutComponentProps> = React.memo(
  ({type, children, columns = 1, spacing = 12}: LayoutComponentProps) => {
    // Chuyển children thành mảng
    const items = React.Children.toArray(children);

    const renderHorizontal = () =>
      items.map((item, index) => (
        <View
          key={`horizontal-${index}`}
          style={{marginRight: index === items.length - 1 ? 0 : spacing}}>
          {item}
        </View>
      ));

    const renderVertical = () =>
      items.map((item, index) => (
        <View
          key={`vertical-${index}`}
          style={{
            marginBottom: index === items.length - 1 ? 0 : spacing,
          }}>
          {item}
        </View>
      ));

    const renderGrid = () => {
      const rows = [];
      for (let i = 0; i < items.length; i += columns) {
        const rowItems = items.slice(i, i + columns);
        rows.push(
          <View
            key={`grid-row-${i}`}
            style={[
              styles.row,
              {
                marginBottom: i + columns >= items.length ? 0 : spacing,
                marginHorizontal: -spacing / 2,
              },
            ]}>
            {rowItems.map((item, idx) => (
              <View
                key={`grid-item-${i + idx}`}
                style={{flex: 1, paddingHorizontal: spacing / 2}}>
                {item}
              </View>
            ))}
          </View>,
        );
      }
      return rows;
    };

    return (
      <View
        style={[styles.mainLayout, type === 'horizontal' && styles.horizontal]}
        accessible>
        {type === 'horizontal' && renderHorizontal()}
        {type === 'vertical' && columns === 1 && renderVertical()}
        {(type === 'grid' || (type === 'vertical' && columns > 1)) &&
          renderGrid()}
      </View>
    );
  },
);

const styles = StyleSheet.create({
  mainLayout: {
    display: 'flex',
  },
  horizontal: {
    flexDirection: 'row',
  },
  row: {
    flexDirection: 'row',
  },
});
