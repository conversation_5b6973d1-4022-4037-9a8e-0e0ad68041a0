import React from 'react';
import {StyleSheet, View, TouchableOpacity, Text} from 'react-native';
import {SearchIconButton} from './SearchIcon';
import {useTheme} from 'react-native-paper';

type SearchProp = {
  placeholder?: string;
  onPress?: () => void;
};

export const SearchInputComponent = ({
  placeholder = 'Thủ tục bạn muốn thực hiện?',
  onPress,
}: SearchProp) => {
  const theme = useTheme();

  const handlePress = () => {
    if (onPress) {
      onPress();
    }
  };

  return (
    <TouchableOpacity
      style={[styles.container, {backgroundColor: theme.colors.background}]}
      activeOpacity={0.8}
      onPress={handlePress}>
      <View style={styles.inputContainer}>
        <Text
          style={[
            styles.placeholderText,
            {color: theme.colors.outline || 'rgba(33, 43, 54, 0.6)'},
          ]}>
          {placeholder}
        </Text>

        <SearchIconButton
          backgroundColor={theme.colors.primary}
          color={theme.colors.onPrimary}
          size={32}
          onPress={handlePress}
        />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    borderRadius: 8,
    backgroundColor: 'transparent', // Will use the theme color
    overflow: 'hidden',
  },
  inputContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingLeft: 12,
    paddingRight: 7,
    paddingVertical: 8,
    height: 54,
  },
  placeholderText: {
    fontSize: 14,
    fontFamily: 'Public Sans',
    fontWeight: '400',
    lineHeight: 22,
    color: 'rgba(33, 43, 54, 0.6)', // Will override with theme color
    flex: 1,
  },
});
