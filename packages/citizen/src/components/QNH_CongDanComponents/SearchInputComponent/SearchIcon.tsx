import React from 'react';
import {TouchableOpacity, StyleSheet, ViewStyle} from 'react-native';
import {SvgProps} from 'react-native-svg';
import {SearchIcon} from '../../../icons';

interface IconButtonProps extends SvgProps {
  onPress?: () => void;
  style?: ViewStyle;
  size?: number;
  color?: string;
  backgroundColor?: string;
  borderRadius?: number;
}

export const SearchIconButton: React.FC<IconButtonProps> = ({
  onPress,
  style,
  size = 32,
  backgroundColor = '#D71920',
  borderRadius = 8,
  color = '#FFFFFF',
}) => {
  return (
    <TouchableOpacity
      style={[
        styles.container,
        {width: size, height: size, backgroundColor, borderRadius},
        style,
      ]}
      onPress={onPress}
      activeOpacity={0.7}>
      <SearchIcon width={size * 0.6} height={size * 0.6} fill={color} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 7,
    marginVertical: 4,
    padding: 8,
  },
});
