import React from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {useDrawNumber} from '../../../stores/QNHCongDan/useDrawNumberStore/useDrawNumber';
import {EIsLoading, IBranchOption} from '../../../types';
import {SVGCheckmarkCircle2Fill} from '../../../icons';
import {BottomSheetScrollView} from '@gorhom/bottom-sheet';
import {textStyles} from '../../../styles/QNH_textStyle';

export const BranchList = React.memo(() => {
  // COMMENT: Store
  const {isLoadingApi, brachOptions} = useDrawNumber();

  const [branchSelected, setBranchSelected] =
    React.useState<IBranchOption | null>(null);

  const handleSelectedBranch = (item: IBranchOption) => {
    setBranchSelected(item);
  };

  return (
    <>
      {isLoadingApi === EIsLoading.SUCCESS && brachOptions?.length > 0 && (
        <BottomSheetScrollView
          keyboardShouldPersistTaps="handled"
          contentContainerStyle={styles.contentContainer}>
          <View>
            {brachOptions.map((item: IBranchOption, index: number) => {
              return (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.item,
                    branchSelected?.id === item.id && styles.selectedItem,
                  ]}
                  onPress={() => handleSelectedBranch(item)}>
                  <View style={styles.menuItem}>
                    {branchSelected && branchSelected.id === item.id && (
                      <SVGCheckmarkCircle2Fill
                        width={24}
                        height={24}
                        color="#366AE2"
                      />
                    )}
                    <View style={styles.contentItem}>
                      <Text
                        numberOfLines={2}
                        ellipsizeMode="tail"
                        className={`${textStyles.subTitle2}`}>
                        {item.name}
                      </Text>
                      <Text
                        numberOfLines={2}
                        ellipsizeMode="tail"
                        className={`${textStyles.body2}`}>
                        {item.address}
                      </Text>
                    </View>
                  </View>
                </TouchableOpacity>
              );
            })}
          </View>
        </BottomSheetScrollView>
      )}
    </>
  );
});

const styles = StyleSheet.create({
  contentContainer: {
    padding: 16,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  contentItem: {
    flex: 1, // Đảm bảo chiếm đủ không gian
    marginLeft: 8,
  },
  item: {
    borderBottomColor: '#eee',
    padding: 8,
  },
  selectedItem: {
    backgroundColor: 'rgba(128, 128, 128, 0.2)', // Màu xám mờ
  },
});
