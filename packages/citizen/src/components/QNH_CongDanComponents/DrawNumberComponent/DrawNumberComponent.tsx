import React, {useEffect, useState, useCallback} from 'react';
import {StyleSheet, View, Text, TouchableOpacity, FlatList} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {CardComponent} from '../CardComponent/CardComponent';
import {Button, ActivityIndicator, useTheme} from 'react-native-paper';
import SVGCalendarAdd from '../../../icons/cong-dan_icons/CalendarAdd';
import {
  useTicketStore,
  getTicketStatusInfo,
} from '../../../stores/ticket.store';
import {useUserStore, getCitizenCode} from '../../../stores/user.store';
import {TicketItem} from '../../../requester-biz-service/apis/draw-number-api';
import {MainStackParamList} from '../../../navigation/MainNavigator';

type NavigationProp = NativeStackNavigationProp<MainStackParamList>;

type props = {
  onHandleShowNotificationBottomSheet: () => void;
};

const CalendarAddIcon = (color: string | undefined) => (
  <SVGCalendarAdd color={color} />
);

export const DrawNumberComponent = React.memo(
  ({onHandleShowNotificationBottomSheet}: props) => {
    const {tickets, isLoading, error, fetchTickets} = useTicketStore();
    const {userProfile} = useUserStore();
    const navigation = useNavigation<NavigationProp>();
    const [isExpanded, setIsExpanded] = useState(false);
    const theme = useTheme();
    useEffect(() => {
      const identificationId = getCitizenCode(userProfile);
      if (identificationId) {
        fetchTickets(identificationId);
      }
    }, [userProfile, fetchTickets]);

    const handleShowMore = useCallback(() => {
      setIsExpanded(!isExpanded);
    }, [isExpanded]);

    const handleTicketPress = useCallback(
      (ticketId: number) => {
        navigation.navigate('TicketDetail', {ticketId: ticketId.toString()});
      },
      [navigation],
    );

    const renderTicketItem = useCallback(
      ({item}: {item: TicketItem}) => {
        const statusInfo = getTicketStatusInfo(item.status);
        const ticketNumber = item.ticketNumber || (item as any).ticket_number;
        const branchInfo =
          item.services?.qmsBranch || (item.services as any)?.qms_branch;

        return (
          <TouchableOpacity
            style={[
              styles.ticketItem,
              {borderBottomColor: theme.colors.surfaceVariant},
            ]}
            onPress={() => handleTicketPress(item.id)}>
            <View style={styles.ticketContent}>
              <View style={styles.ticketRow}>
                <View style={styles.ticketInfo}>
                  <View style={styles.ticketCodeAndStatus}>
                    <Text
                      style={[
                        styles.ticketCode,
                        {color: theme.colors.primary},
                      ]}>
                      {ticketNumber}
                    </Text>
                    <Text
                      style={[
                        styles.separator,
                        {color: theme.colors.onSurfaceVariant},
                      ]}>
                      -
                    </Text>
                    <Text
                      style={[styles.ticketStatus, {color: statusInfo.color}]}>
                      {statusInfo.text}
                    </Text>
                  </View>
                  <Text
                    style={[
                      styles.ticketLocation,
                      {color: theme.colors.onSurfaceVariant},
                    ]}
                    numberOfLines={2}
                    ellipsizeMode="tail">
                    {branchInfo?.name ||
                      item.services?.name ||
                      'Chưa có thông tin chi nhánh'}
                  </Text>
                </View>
              </View>
            </View>
          </TouchableOpacity>
        );
      },
      [
        theme.colors.surfaceVariant,
        theme.colors.primary,
        theme.colors.onSurfaceVariant,
        handleTicketPress,
      ],
    );

    const renderContent = () => {
      if (isLoading) {
        return (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color={theme.colors.primary} />
            <Text style={[styles.loadingText, {color: theme.colors.onSurface}]}>
              Đang tải lịch hẹn...
            </Text>
          </View>
        );
      }

      if (error) {
        return (
          <View style={styles.errorContainer}>
            <Text style={[styles.errorText, {color: theme.colors.error}]}>
              {error}
            </Text>
          </View>
        );
      }

      if (!tickets || tickets.length === 0) {
        return (
          <View style={styles.content}>
            <Button
              mode="contained"
              icon={() => CalendarAddIcon(theme.colors.onPrimary)}
              onPress={() => onHandleShowNotificationBottomSheet()}>
              Đặt lịch ngay
            </Button>
          </View>
        );
      }

      const displayTickets = isExpanded ? tickets : tickets.slice(0, 3);

      return (
        <View style={styles.ticketsContainer}>
          <FlatList
            data={displayTickets}
            renderItem={renderTicketItem}
            keyExtractor={item => item.id.toString()}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />

          {tickets.length > 3 && (
            <TouchableOpacity
              onPress={handleShowMore}
              style={styles.showMoreButton}>
              <Text
                style={[styles.showMoreText, {color: theme.colors.primary}]}>
                {isExpanded ? 'Thu gọn' : 'Xem thêm'}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      );
    };

    const cardButton =
      tickets && tickets.length > 0 ? 'Đặt lịch mới' : undefined;
    const cardOnPress =
      tickets && tickets.length > 0
        ? onHandleShowNotificationBottomSheet
        : undefined;

    return (
      <CardComponent
        title="Lịch hẹn hôm nay"
        button={cardButton}
        onPress={cardOnPress}
        buttonMode="contained">
        {renderContent()}
      </CardComponent>
    );
  },
);

const styles = StyleSheet.create({
  content: {
    paddingTop: 16,
  },
  contentButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
    gap: 8,
  },
  loadingText: {},
  errorContainer: {
    paddingVertical: 16,
    alignItems: 'center',
  },
  errorText: {
    textAlign: 'center',
  },
  ticketsContainer: {
    marginTop: 8,
  },
  ticketItem: {
    paddingBottom: 6,
    borderBottomWidth: 1,
  },
  ticketContent: {
    flex: 1,
  },
  ticketRow: {
    flexDirection: 'row',
  },
  ticketInfo: {
    flex: 1,
    flexDirection: 'column',
  },
  ticketCodeAndStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  ticketCode: {
    fontWeight: '700',
    fontSize: 24,
  },
  separator: {
    marginHorizontal: 4,
  },
  ticketStatus: {
    fontSize: 12,
    fontWeight: '400',
  },
  ticketLocation: {
    fontSize: 14,
    lineHeight: 20,
  },
  showMoreButton: {
    alignItems: 'center',
    paddingVertical: 8,
    marginTop: 4,
  },
  showMoreText: {
    fontWeight: '500',
  },
});
