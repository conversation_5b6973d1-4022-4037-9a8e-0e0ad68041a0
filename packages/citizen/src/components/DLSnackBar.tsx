import {Snackbar} from 'react-native-paper';
import {View} from 'react-native';
import {useState} from 'react';
import {useAcTheme} from './AcThemeContext';
import React from 'react';
import {AcText} from './AcText';
import {DangerBold, InfoCircleBold, TickCircleBold} from '../icons';
export type SnackBarType = 'default' | 'info' | 'success' | 'warning' | 'error';
type DLSnackBarType = {
  type: SnackBarType;
  visible: boolean;
  onDismissSnackBar?: () => void;
  message: string;
  duration?: number;
  actionLabel?: any;
};

const DLSnackBar = ({
  type = 'default',
  visible = true,
  message = '',
  actionLabel,
  duration,
  onDismissSnackBar = () => {
    console.log('helloe');
  },
}: DLSnackBarType) => {
  const {theme} = useAcTheme();
  const [isVisible, setIsVisible] = useState<boolean>(visible);
  const componentProp = [
    {
      name: 'error',
      icon: <DangerBold color={theme.ac.error.default.main} />,
      backgroundColor: theme.ac.error.transparent.trans16,
    },
    {
      name: 'info',
      icon: <InfoCircleBold color={theme.ac.info.default.main} />,
      backgroundColor: theme.ac.info.transparent.trans16,
    },
    {
      name: 'success',
      icon: <TickCircleBold color={theme.ac.success.default.main} />,
      backgroundColor: theme.ac.success.transparent.trans16,
    },
    {
      name: 'warning',
      icon: <DangerBold color={theme.ac.warning.default.main} />,
      backgroundColor: theme.ac.warning.transparent.trans16,
    },
  ];
  const closeLabel = actionLabel ? actionLabel : 'Đóng';
  const sameProps = {
    visible: isVisible,
    onDismiss: () => {
      setIsVisible(false);
      setTimeout(() => {
        onDismissSnackBar();
      }, 0);
    },
    theme: {colors: {inversePrimary: theme.ac.text.secondary}},
    action: {
      label: closeLabel,
      onPress: () => {
        // Do something
      },
    },
  };
  const defaultSnack = <Snackbar {...sameProps}>{message}</Snackbar>;
  if (type === 'default') {
    return defaultSnack;
  } else {
    const selectedType = componentProp.find(i => i.name === type);
    if (selectedType) {
      return (
        <Snackbar
          {...sameProps}
          style={{
            backgroundColor: theme.ac.background.default,
            borderRadius: 8,
            paddingBottom: 4,
          }}
          duration={duration}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'flex-start',
              gap: 7,
              alignItems: 'center',
            }}>
            <View
              style={{
                padding: 3,
                borderRadius: 8,
                backgroundColor: selectedType.backgroundColor,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              {selectedType?.icon}
            </View>
            <AcText
              variant={'titleMedium'}
              style={{width: 0, flexGrow: 1, flexShrink: 1}}>
              {message}
            </AcText>
          </View>
        </Snackbar>
      );
    } else return defaultSnack;
  }
};
export default DLSnackBar;
