import React from 'react';
import {Dialog, Portal, Button} from 'react-native-paper';
import {StyleSheet, View, Text} from 'react-native';

type DialogConfirmType = {
  icon: React.ReactNode | any;
  title: string;
  visible: boolean;
  subtitle: string;
  bodyText: string;
  cancelButtonText: string;
  onCancel: () => void;
  confirmButtonText: string;
  onOK: () => void;
  loading: boolean;
};

const IconComponent = ({icon}: {icon: React.ReactNode | any}) => (
  <View style={styles.iconContainer}>
    <Text style={styles.iconText}>{icon}</Text>
  </View>
);

const DialogConfirm = ({
  icon,
  title,
  visible,
  subtitle,
  bodyText,
  cancelButtonText,
  onCancel,
  confirmButtonText,
  loading,
  onOK,
}: DialogConfirmType) => {
  console.log('🔔 DialogConfirm render:', {
    visible,
    title,
    subtitle,
    bodyText,
  });

  const iconRenderer = React.useCallback(
    () => <IconComponent icon={icon} />,
    [icon],
  );

  return (
    <Portal>
      <Dialog visible={visible} dismissable={false}>
        {icon && <Dialog.Icon icon={iconRenderer} />}

        <Dialog.Title style={styles.title}>{title}</Dialog.Title>

        <Dialog.Content>
          <Text style={styles.subtitle}>{subtitle}</Text>
          <Text style={styles.bodyText}>{bodyText}</Text>
        </Dialog.Content>

        <Dialog.Actions style={styles.actions}>
          <Button
            mode="outlined"
            onPress={onCancel}
            style={styles.cancelButton}
            disabled={loading}>
            {cancelButtonText}
          </Button>

          <Button
            mode="contained"
            onPress={onOK}
            style={styles.confirmButton}
            loading={loading}
            disabled={loading}>
            {confirmButtonText}
          </Button>
        </Dialog.Actions>
      </Dialog>
    </Portal>
  );
};

const styles = StyleSheet.create({
  centeredText: {
    textAlign: 'center',
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#E3F2FD',
    marginBottom: 8,
  },
  iconText: {
    fontSize: 32,
  },
  title: {
    textAlign: 'center',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  subtitle: {
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  bodyText: {
    textAlign: 'center',
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 16,
    paddingHorizontal: 16,
  },
  cancelButton: {
    flex: 1,
  },
  confirmButton: {
    flex: 1,
  },
});

export default DialogConfirm;
