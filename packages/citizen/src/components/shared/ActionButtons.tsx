import React from 'react';
import {View, StyleSheet} from 'react-native';
import {Button, useTheme} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

export type ButtonVariant =
  | 'contained'
  | 'outlined'
  | 'text'
  | 'elevated'
  | 'primary'
  | 'secondary';

export interface ActionButtonProps {
  label: string;
  onPress: () => void;
  variant?: ButtonVariant;
  iconName?: string;
  iconType?: 'material' | 'material-community';
  iconColor?: string;
  disabled?: boolean;
  loading?: boolean;
  style?: object;
  flex?: boolean;
  contentStyle?: object;
  labelStyle?: object;
  buttonColor?: string;
  textColor?: string;
}

interface ActionButtonsProps {
  primaryButton: ActionButtonProps;
  secondaryButton?: ActionButtonProps;
  singleButton?: boolean;
  spacing?: number;
  containerStyle?: object;
  buttonRowStyle?: object;
}

// Pre-defined icon components with customizable color
const getIconComponent = (
  name: string,
  type: string = 'material',
  color: string = '#FF3B30',
) => {
  if (type === 'material-community') {
    return () => <MaterialCommunityIcons name={name} size={20} color={color} />;
  }
  return () => <Icon name={name} size={20} color={color} />;
};

const SingleButton: React.FC<{
  button: ActionButtonProps;
}> = ({button}) => {
  const theme = useTheme();
  const {
    label,
    variant = 'contained',
    onPress,
    loading,
    disabled,
    iconName,
    iconType,
    iconColor,
    flex,
    style,
    contentStyle,
    labelStyle,
    buttonColor,
    textColor,
  } = button;

  const buttonStyle = flex !== false ? styles.flexButton : undefined;

  // Determine the icon color: use textColor if provided, else for primary/contained use theme.colors.onPrimary, else iconColor, else default
  let effectiveIconColor = button.textColor;
  if (!effectiveIconColor) {
    if (button.variant === 'contained' || button.variant === 'primary') {
      effectiveIconColor = theme.colors.onPrimary;
    } else if (
      button.variant === 'outlined' ||
      button.variant === 'secondary'
    ) {
      effectiveIconColor = theme.colors.primary;
    } else {
      effectiveIconColor = iconColor || theme.colors.onSurface;
    }
  }

  let iconComponent;
  if (iconName) {
    iconComponent = getIconComponent(iconName, iconType, effectiveIconColor);
  }

  return (
    <Button
      mode={variant as any}
      onPress={onPress}
      loading={loading}
      disabled={disabled}
      icon={iconComponent}
      style={[buttonStyle, style]}
      contentStyle={contentStyle}
      labelStyle={labelStyle}
      buttonColor={buttonColor}
      textColor={textColor}>
      {label}
    </Button>
  );
};

const ActionButtons: React.FC<ActionButtonsProps> = ({
  primaryButton,
  secondaryButton,
  singleButton = false,
  spacing = 16,
  containerStyle,
  buttonRowStyle,
}) => {
  if (singleButton || !secondaryButton) {
    return (
      <View style={[styles.container, containerStyle]}>
        <View style={[styles.buttonRow, buttonRowStyle]}>
          <SingleButton button={primaryButton} />
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, containerStyle]}>
      <View style={[styles.buttonRow, buttonRowStyle]}>
        <SingleButton button={secondaryButton} />
        <View style={{width: spacing}} />
        <SingleButton button={primaryButton} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  buttonRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  flexButton: {
    flex: 1,
  },
});

export default ActionButtons;
