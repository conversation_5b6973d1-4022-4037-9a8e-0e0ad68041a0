import React from 'react';
import {TrashBold} from '../icons';
import DialogConfirm from './DialogConfirm';

type DeleteDialogConfirmType = {
  visible: boolean;
  subtitle: string;
  bodyText: string;
  onCancel: () => void;
  onOK: () => void;
  loading: boolean;
  title: string;
};
const DeleteDialogConfirm = ({
  visible,
  subtitle,
  bodyText,
  onCancel,
  onOK,
  loading,
  title,
}: DeleteDialogConfirmType) => {
  return (
    <DialogConfirm
      icon={TrashBold}
      title={title}
      visible={visible}
      subtitle={subtitle}
      loading={loading}
      bodyText={bodyText}
      cancelButtonText={'Huỷ'}
      onCancel={onCancel}
      confirmButtonText={'Xác nhận'}
      onOK={onOK}
    />
  );
};
export default DeleteDialogConfirm;
