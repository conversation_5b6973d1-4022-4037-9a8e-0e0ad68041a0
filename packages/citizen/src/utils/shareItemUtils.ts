export interface ShareItem {
  id: string;
  progress: string | number;
  documents?: Array<{
    name?: string;
    template?: {
      code?: string;
    };
  }>;
  data?: Array<{
    name?: string;
    template?: {
      code?: string;
    };
  }>;
  shortId?: string;
  templateCodes?: string[];
  createdAt?: string;
  updatedAt?: string;
}

export const getTitle = (item: ShareItem): string => {
  if (item.documents && item.documents.length > 0 && item.documents[0].name) {
    return item.documents[0].name;
  }
  if (item.data && item.data.length > 0 && item.data[0].name) {
    return item.data[0].name;
  }
  return 'Untitled';
};

export const getShortCode = (item: ShareItem): string => {
  if (item.shortId) {
    return item.shortId;
  }
  if (item.documents && item.documents.length > 0) {
    const doc = item.documents[0];
    if (doc.template && doc.template.code) {
      return doc.template.code;
    }
  }
  if (item.data && item.data.length > 0) {
    const dataItem = item.data[0];
    if (dataItem.template && dataItem.template.code) {
      return dataItem.template.code;
    }
  }
  if (item.templateCodes && item.templateCodes.length > 0) {
    return item.templateCodes[0];
  }
  return '';
};

export const getSubmissionDate = (item: ShareItem): string => {
  if (item.createdAt) {
    return new Date(item.createdAt).toLocaleDateString('vi-VN');
  }
  if (item.updatedAt) {
    return new Date(item.updatedAt).toLocaleDateString('vi-VN');
  }
  return '';
};
