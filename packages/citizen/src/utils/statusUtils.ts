export interface StatusInfo {
  text: string;
  color: string;
  backgroundColor: string;
}

export const getStatusInfo = (status: string | number): StatusInfo => {
  const statusMap: {
    [key: string]: StatusInfo;
  } = {
    // Map by status text (Vietnamese)
    new: {
      text: 'Chưa gửi',
      color: '#ffffff',
      backgroundColor: '#1976D2',
    },
    SYNCED: {
      text: 'Đã gửi',
      color: '#ffffff',
      backgroundColor: '#4CAF50',
    },
    '1': {
      text: 'Đã nộp',
      color: '#ffffff',
      backgroundColor: '#f59e0b',
    },
    '2': {
      text: 'Đ<PERSON><PERSON>c chấp nhận',
      color: '#ffffff',
      backgroundColor: '#22c55e',
    },
    '3': {
      text: 'Bị từ chối',
      color: '#ffffff',
      backgroundColor: '#ef4444',
    },
    '4': {
      text: 'Đang xử lý',
      color: '#ffffff',
      backgroundColor: '#3b82f6',
    },
    '5': {
      text: '<PERSON><PERSON> bổ sung',
      color: '#000000',
      backgroundColor: '#fbbf24',
    },
    '9': {
      text: 'Đã xử lý',
      color: '#ffffff',
      backgroundColor: '#22c55e',
    },
    '10': {
      text: 'Đã trả về kết quả',
      color: '#ffffff',
      backgroundColor: '#22c55e',
    },
    '-1': {
      text: 'Đã gửi',
      color: '#ffffff',
      backgroundColor: '#10b981',
    },
    '-2': {
      text: 'Chưa gửi',
      color: '#ffffff',
      backgroundColor: '#6b7280',
    },
  };

  const statusKey = String(status);
  return (
    statusMap[statusKey] || {
      text: status ? String(status) : 'Không xác định',
      color: '#000000',
      backgroundColor: '#e5e7eb',
    }
  );
};
