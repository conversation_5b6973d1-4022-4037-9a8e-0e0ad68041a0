import {WebView} from 'react-native-webview';
import {Al<PERSON>} from 'react-native';
import {MutableRefObject} from 'react';

// Utils để chạy JS
export const runJavaScript = async (
  jsCode: string,
  webViewRef: MutableRefObject<WebView | null>,
) => {
  try {
    if (webViewRef.current) {
      webViewRef.current.injectJavaScript(jsCode);
      console.log('✅ Ran JS:', jsCode);
    } else {
      console.warn('⚠️ WebViewRef is null');
    }
  } catch (err) {
    console.error('❌ Error running JS:', err);
    Alert.alert('Error', 'Failed to run JavaScript');
  }
};

export class WebViewPdfHelper {
  static async handleEditMode(webViewRef: MutableRefObject<WebView | null>) {
    await runJavaScript(`window.postMessage("edit-mode", "*");`, webViewRef);
  }

  static async handleSignMode(webViewRef: MutableRefObject<WebView | null>) {
    await runJavaScript(`window.postMessage("sign-mode", "*");`, webViewRef);
  }

  static async requestMetadata(webViewRef: MutableRefObject<WebView | null>) {
    await runJavaScript(
      `window.postMessage("get-meta-data", "*");`,
      webViewRef,
    );
  }

  static async getHtml(webViewRef: MutableRefObject<WebView | null>) {
    await runJavaScript(`window.postMessage("get-html", "*");`, webViewRef);
    console.log('📤 Sent command: get-html');
  }

  static async getMode(webViewRef: MutableRefObject<WebView | null>) {
    await runJavaScript(`window.postMessage("get-mode", "*");`, webViewRef);
  }

  static async convertHtmlToPdf(htmlData: string) {
    try {
      const rawHtmlBody = htmlData.replace(/\\n/g, '').replace(/\\(.)/g, '$1');
      console.log('📄 Prepare download PDF:', rawHtmlBody);

      // 👇 Replace this with your actual API call to convert
      const pdfBytes = await fakeDownloadPdf(rawHtmlBody);
      return pdfBytes;
    } catch (err) {
      console.error('❌ Error converting HTML to PDF:', err);
      throw new Error('Failed to convert HTML to PDF');
    }
  }
}

// Fake API (Bạn thay thế bằng API thực tế)
const fakeDownloadPdf = async (html: string): Promise<Uint8Array> => {
  console.log('📥 Fake API convert HTML to PDF');
  return new Uint8Array([1, 2, 3, 4]); // Dummy PDF bytes
};
