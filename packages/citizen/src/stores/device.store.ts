import {create, createJSONStorage, persist} from '@ac-mobile/common';
import AsyncStorage from '@react-native-async-storage/async-storage';

export type DeviceStoreState = {
  disableBack: boolean;
};

export type DeviceStoreActions = {
  updateDisableBack: (disableBackValue: boolean) => void;
};

const initState: DeviceStoreState = {
  disableBack: false,
};

export const useDeviceStore = create<DeviceStoreState & DeviceStoreActions>()(
  persist(
    (set, _) => ({
      ...initState,

      updateDisableBack: (disableBackValue: boolean) => {
        set({disableBack: disableBackValue});
      },
    }),
    {
      name: 'device-store',
      storage: createJSONStorage(() => AsyncStorage),
    },
  ),
);
