import {isArray} from 'lodash';
import moment from 'moment';

export const folderObjectTransfer = (item: any) => {
  const rs = {
    ...item,
    documents: item.data,
    folderName: item.name,
    folderPrototypeName: item?.template?.name || undefined,
    updatedAt: moment(item.updatedAt).format('DD/MM/YYYY'),
    numberOfDocumentsInFolder:
      item?.data?.reduce(
        (count: number, obj: any) =>
          obj.hasOwnProperty('uri') ? count + 1 : count,
        0,
      ) || 0,
    numberOfRequiredDocuments: item?.template?.size || 0,
  };
  return rs;
};

export const templateObjectTransfer = (item: any) => {
  let documents = [];
  if (isArray(item.data)) {
    documents = item.data;
  } else if (typeof item.data === 'object') {
    documents = [item.data];
  }
  const templateItem = {
    ...item,
    template: {
      templateId: item.id,
      templateName: item.name,
    },
    folderName: '',
    documents: documents.map((docItem: any) => {
      return {...docItem, documentName: docItem.name};
    }),
  };
  return templateItem;
};

export const mapTemplateForStore = (templateArray: Array<any>) => {
  const rs = templateArray.map(item => {
    return templateObjectTransfer(item);
  });
  return rs;
};
