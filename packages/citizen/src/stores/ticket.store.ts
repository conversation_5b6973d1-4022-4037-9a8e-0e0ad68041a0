import {create, createJSONStorage, persist} from '@ac-mobile/common';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  drawNumber,
  TicketListResponse,
  TicketItem,
  GetTicketsParams,
} from '../requester-biz-service/apis/draw-number-api';

export type TicketStoreState = {
  tickets: TicketItem[];
  isLoading: boolean;
  error: string | null;
  lastFetched: number | null;
  totalItems: number;
};

export type TicketStoreActions = {
  fetchTickets: (
    identificationId: string,
    forceRefresh?: boolean,
  ) => Promise<void>;
  clearTickets: () => void;
  clearError: () => void;
  refreshTickets: (identificationId: string) => Promise<void>;
  isDataStale: () => boolean;
};

const initState: TicketStoreState = {
  tickets: [],
  isLoading: false,
  error: null,
  lastFetched: null,
  totalItems: 0,
};

export const useTicketStore = create<TicketStoreState & TicketStoreActions>()(
  persist(
    (set, get) => ({
      ...initState,

      fetchTickets: async (identificationId: string) => {
        const state = get();
        console.log('Fetching tickets for:', identificationId);
        // Avoid multiple concurrent requests
        if (state.isLoading) {
          return;
        }

        try {
          set({isLoading: true, error: null});

          const params: GetTicketsParams = {
            identificationId,
            page: 1,
            pageSize: 10,
            filter: {
              // Get today's tickets or recent ones
              createdDate: new Date().toISOString().split('T')[0],
            },
          };

          const response: TicketListResponse =
            await drawNumber.getTicketsByIdentificationId(params);

          console.log('Tickets fetched successfully:', response);

          set({
            tickets: response.items || [],
            totalItems: response.totalItems || 0,
            isLoading: false,
            error: null,
            lastFetched: Date.now(),
          });
        } catch (error: any) {
          console.error('Error fetching tickets:', error);
          set({
            isLoading: false,
            error: error.message || 'Không thể tải danh sách lịch hẹn',
            tickets: [],
            totalItems: 0,
          });
        }
      },

      refreshTickets: async (identificationId: string) => {
        const {fetchTickets} = get();
        await fetchTickets(identificationId);
      },

      clearTickets: () => {
        set({
          tickets: [],
          error: null,
          lastFetched: null,
          totalItems: 0,
        });
      },

      clearError: () => {
        set({error: null});
      },

      isDataStale: () => {
        const state = get();
        if (!state.lastFetched) {
          return true;
        }

        const now = Date.now();
        const STALE_THRESHOLD = 5 * 60 * 1000; // 5 minutes
        return now - state.lastFetched > STALE_THRESHOLD;
      },
    }),
    {
      name: 'ticket-store',
      storage: createJSONStorage(() => AsyncStorage),
      // Don't persist loading state and errors
      partialize: state => ({
        tickets: state.tickets,
        lastFetched: state.lastFetched,
        totalItems: state.totalItems,
      }),
    },
  ),
);

// Helper function to get ticket status color and text
export const getTicketStatusInfo = (status: string) => {
  switch (status?.toUpperCase()) {
    case 'CANCELLED':
      return {
        text: 'Đã Huỷ',
        color: '#FF3B30', // Red
      };
    case 'INIT':
      return {
        text: 'Sắp đến lượt',
        color: '#8E8E93', // Grey
      };
    case 'CALLED':
      return {
        text: 'Bạn đang được phục vụ',
        color: '#34C759', // Green
      };
    case 'RECALLED':
      return {
        text: 'Số của bạn đã được gọi lại',
        color: '#FF9500', // Warning Red/Orange
      };
    case 'SKIPED':
      return {
        text: 'Bạn đã bỏ lỡ lượt phục vụ',
        color: '#FF9500', // Warning Red/Orange
      };
    case 'FINISHED':
      return {
        text: 'Bạn đã được phục vụ',
        color: '#34C759', // Green
      };
    // Keep legacy status support
    case 'PENDING':
    case 'WAITING':
      return {
        text: 'Chờ xử lý',
        color: '#FF9500', // Orange
      };
    case 'PROCESSING':
    case 'SERVING':
      return {
        text: 'Đang xử lý',
        color: '#007AFF', // Blue
      };
    case 'COMPLETED':
    case 'DONE':
      return {
        text: 'Hoàn thành',
        color: '#34C759', // Green
      };
    default:
      return {
        text: 'Không xác định',
        color: '#8E8E93', // Grey
      };
  }
};

// Helper function to format date for display
export const formatTicketDate = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  } catch (error) {
    return dateString;
  }
};

// Helper function to format time for display
export const formatTicketTime = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    return date.toLocaleTimeString('vi-VN', {
      hour: '2-digit',
      minute: '2-digit',
    });
  } catch (error) {
    return dateString;
  }
};
