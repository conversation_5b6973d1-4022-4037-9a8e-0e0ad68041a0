import {create, createJSONStorage, persist} from '@ac-mobile/common';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  UserProfile,
  UserResponse,
  auth,
} from '../requester-biz-service/apis/auth-api';

export type UserStoreState = {
  userProfile: UserProfile | null;
  isLoading: boolean;
  error: string | null;
  lastFetched: number | null;
  isInitialized: boolean;
  retryCount: number;
};

export type UserStoreActions = {
  fetchUserProfile: (forceRefresh?: boolean) => Promise<void>;
  setUserProfile: (profile: UserProfile) => void;
  clearUserProfile: () => void;
  clearError: () => void;
  refreshUserProfile: () => Promise<void>;
  isDataStale: () => boolean;
  getDisplayName: () => string;
  isAuthenticated: () => boolean;
  clearPersistedData: () => Promise<void>;
};

const initState: UserStoreState = {
  userProfile: null,
  isLoading: false,
  error: null,
  lastFetched: null,
  isInitialized: false,
  retryCount: 0,
};

export const useUserStore = create<UserStoreState & UserStoreActions>()(
  persist(
    (set, get) => ({
      ...initState,

      fetchUserProfile: async (forceRefresh = false) => {
        const state = get();

        // Avoid multiple concurrent requests
        if (state.isLoading) {
          return;
        }

        // Check if we have recent data (within last 5 minutes) and not forcing refresh
        const now = Date.now();
        const CACHE_DURATION = 5 * 60 * 0; // 5 minutes
        const hasRecentData =
          state.userProfile &&
          state.lastFetched &&
          now - state.lastFetched < CACHE_DURATION;

        if (hasRecentData && !forceRefresh) {
          // Mark as initialized if we have data
          if (!state.isInitialized) {
            set({isInitialized: true});
          }
          return;
        }

        // Reset retry count on new fetch attempt
        if (state.retryCount > 0 && !forceRefresh) {
          set({retryCount: 0});
        }

        try {
          set({isLoading: true, error: null});

          // console.log(
          //   'Fetching user profile...',
          //   forceRefresh ? '(forced)' : '',
          // );
          const response: UserResponse = await auth.fillUserInfo();
          console.log('Fetched user profile successfully:', response.data);
          set({
            userProfile: response.data,
            isLoading: false,
            error: null,
            lastFetched: now,
            isInitialized: true,
            retryCount: 0,
          });
        } catch (error: any) {
          console.error('Error fetching user profile:', JSON.stringify(error));
          const newRetryCount = state.retryCount + 1;

          set({
            isLoading: false,
            error: error.message || 'Không thể tải thông tin người dùng',
            retryCount: newRetryCount,
            isInitialized: true, // Mark as initialized even on error
          });
        }
      },

      refreshUserProfile: async () => {
        const {fetchUserProfile} = get();
        await fetchUserProfile(true);
      },

      setUserProfile: (profile: UserProfile) => {
        set({
          userProfile: profile,
          error: null,
          lastFetched: Date.now(),
          isInitialized: true,
          retryCount: 0,
        });
      },

      clearUserProfile: () => {
        set({
          userProfile: null,
          error: null,
          lastFetched: null,
          isInitialized: false,
          retryCount: 0,
        });
      },

      clearError: () => {
        set({error: null});
      },

      isDataStale: () => {
        const state = get();
        if (!state.userProfile || !state.lastFetched) {
          return true;
        }

        const now = Date.now();
        const STALE_THRESHOLD = 15 * 60 * 1000; // 15 minutes
        return now - state.lastFetched > STALE_THRESHOLD;
      },

      getDisplayName: () => {
        const state = get();

        // Handle both cases: userProfile might be the full response or just the data part
        let fullName = '';
        if (state.userProfile) {
          // If userProfile has a 'data' property, it's the full response
          if ('data' in state.userProfile && state.userProfile.data) {
            fullName = (state.userProfile as any).data.fullName || '';
          } else {
            // Otherwise it's just the user data
            fullName = state.userProfile.fullName || '';
          }
        }
        return fullName;
      },

      isAuthenticated: () => {
        const state = get();
        if (!state.userProfile) {
          return false;
        }

        // Handle both cases: userProfile might be the full response or just the data part
        let userId = '';
        if ('data' in state.userProfile && (state.userProfile as any).data) {
          userId = (state.userProfile as any).data.userId || '';
        } else {
          userId = state.userProfile.userId || '';
        }

        return !!userId;
      },

      clearPersistedData: async () => {
        try {
          await AsyncStorage.removeItem('user-store');
          console.log('Cleared persisted user data');
          // Reset the store to initial state
          set({...initState});
        } catch (error) {
          console.error('Error clearing persisted data:', error);
        }
      },
    }),
    {
      name: 'user-store',
      storage: createJSONStorage(() => AsyncStorage),
      // Don't persist loading state, errors, and retry count
      partialize: state => ({
        userProfile: state.userProfile,
        lastFetched: state.lastFetched,
        isInitialized: state.isInitialized,
      }),
      // Add migration to fix persisted data structure
      migrate: (persistedState: any, _version: number) => {
        // If persisted userProfile has 'data' property, extract it
        if (
          persistedState.userProfile &&
          'data' in persistedState.userProfile
        ) {
          console.log('Migrating userProfile structure...');
          persistedState.userProfile = persistedState.userProfile.data;
        }
        return persistedState;
      },
      version: 1,
    },
  ),
);

// Helper function to get citizen code (identificationId)
export const getCitizenCode = (userProfile: UserProfile | null): string => {
  if (!userProfile) {
    return '';
  }

  // Handle both cases: userProfile might be the full response or just the data part
  if ('data' in userProfile && (userProfile as any).data) {
    return (userProfile as any).data.identificationId || '';
  }
  return userProfile.identificationId || '';
};

// Helper function to format date of birth for API
export const formatDateOfBirth = (userProfile: UserProfile | null): string => {
  if (!userProfile) {
    return '';
  }

  let dateOfBirth = '';

  // Handle both cases: userProfile might be the full response or just the data part
  if ('data' in userProfile && (userProfile as any).data) {
    dateOfBirth = (userProfile as any).data.dateOfBirth || '';
  } else {
    dateOfBirth = userProfile.dateOfBirth || '';
  }

  if (!dateOfBirth) {
    return '';
  }

  try {
    const date = new Date(dateOfBirth);
    return date.toISOString().split('T')[0]; // Returns YYYY-MM-DD format
  } catch (error) {
    console.error('Error formatting date of birth:', error);
    return '';
  }
};

// Helper function to format date of birth for display
export const formatDateOfBirthDisplay = (
  userProfile: UserProfile | null,
): string => {
  if (!userProfile) {
    return '';
  }

  let dateOfBirth = '';

  // Handle both cases: userProfile might be the full response or just the data part
  if ('data' in userProfile && (userProfile as any).data) {
    dateOfBirth = (userProfile as any).data.dateOfBirth || '';
  } else {
    dateOfBirth = userProfile.dateOfBirth || '';
  }

  if (!dateOfBirth) {
    return '';
  }

  try {
    const date = new Date(dateOfBirth);
    return date.toLocaleDateString('vi-VN'); // Vietnamese format
  } catch (error) {
    console.error('Error formatting date of birth for display:', error);
    return '';
  }
};

// Helper function to get user initials for avatar
export const getUserInitials = (userProfile: UserProfile | null): string => {
  if (!userProfile) {
    return '';
  }

  let fullName = '';

  // Handle both cases: userProfile might be the full response or just the data part
  if ('data' in userProfile && (userProfile as any).data) {
    fullName = (userProfile as any).data.fullName || '';
  } else {
    fullName = userProfile.fullName || '';
  }

  if (!fullName) {
    return '';
  }

  const names = fullName.trim().split(' ');
  if (names.length === 1) {
    return names[0].charAt(0).toUpperCase();
  }

  // Take first letter of first name and last name
  const firstName = names[0];
  const lastName = names[names.length - 1];
  return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
};

// Helper function to check if user has complete profile
export const hasCompleteProfile = (
  userProfile: UserProfile | null,
): boolean => {
  if (!userProfile) {
    return false;
  }

  const requiredFields = [
    'fullName',
    'identificationId',
    'email',
    'phoneNumber',
    'dateOfBirth',
  ];

  // Get the actual user data (handle both nested and direct structure)
  let userData: any = userProfile;
  if ('data' in userProfile && (userProfile as any).data) {
    userData = (userProfile as any).data;
  }

  console.log('hasCompleteProfile - checking userData:', userData);

  return requiredFields.every(field => {
    const value = userData[field];
    const isComplete = value && value.toString().trim() !== '';
    console.log(
      `hasCompleteProfile - ${field}:`,
      value,
      'isComplete:',
      isComplete,
    );
    return isComplete;
  });
};

// Helper function to get user age
export const getUserAge = (userProfile: UserProfile | null): number | null => {
  if (!userProfile) {
    return null;
  }

  let dateOfBirth = '';

  // Handle both cases: userProfile might be the full response or just the data part
  if ('data' in userProfile && (userProfile as any).data) {
    dateOfBirth = (userProfile as any).data.dateOfBirth || '';
  } else {
    dateOfBirth = userProfile.dateOfBirth || '';
  }

  if (!dateOfBirth) {
    return null;
  }

  try {
    const birthDate = new Date(dateOfBirth);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    return age;
  } catch (error) {
    console.error('Error calculating user age:', error);
    return null;
  }
};

export const getUserGender = (
  userProfile: UserProfile | null,
): number | null => {
  if (!userProfile) {
    return null;
  }

  try {
    // Handle both cases: userProfile might be the full response or just the data part
    if ('data' in userProfile && (userProfile as any).data) {
      return (userProfile as any).data.gender ?? null;
    }
    return (userProfile as any).gender ?? null;
  } catch (error) {
    console.error('Error getting user gender:', error);
    return null;
  }
};

export const getUserPhoneNumberOrEmail = (
  userProfile: UserProfile | null,
  key: 'phoneNumber' | 'email',
): number | null => {
  if (!userProfile) {
    return null;
  }

  try {
    // Handle both cases: userProfile might be the full response or just the data part
    if ('data' in userProfile && (userProfile as any).data) {
      return (userProfile as any)?.data?.[key] ?? null;
    }
    return (userProfile as any)?.data?.[key] ?? null;
  } catch (error) {
    console.error('Error getting user gender:', error);
    return null;
  }
};
