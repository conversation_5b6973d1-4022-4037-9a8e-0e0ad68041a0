export type IDocumentItemInfo = {
  type?: string;
  dataType?: 'file' | 'text';
  state?: string;
  downloaded?: boolean;
  removed?: boolean;
  title: string;
  capacity?: string;
  signed?: boolean;
  documentUri?: string;
  assigned?: boolean;
  enableCheckBox?: boolean;
  defaultCheckbox?: boolean;
};

export type IDocumentItem = {
  dataType?: 'file' | 'text';
  document?: IDocumentItemInfo;
  documentName: string;
  code?: string;
  name?: string;
  uri?: string;
};

export type IFolder = {
  template?: {
    templateId: string;
    templateName: string;
  };
  folderName: string;
  documents: Array<IDocumentItem>;
};
