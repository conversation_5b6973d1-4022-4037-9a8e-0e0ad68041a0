import {create} from '@ac-mobile/common';
import {SnackBarType} from '../components/DLSnackBar';

export type SnackBarConfig = {
  type: SnackBarType;
  message: string;
  timeOut?: number;
  id?: string;
};

interface SnackStateState {
  snacks: SnackBarConfig[];
  pushSnack: (snack: SnackBarConfig) => void;
  popSnack: (id: string) => void;
  clearAllSnack: () => void;
}

const initValue = {
  snacks: [],
};

const useSnackStore = create<SnackStateState>()(set => ({
  ...initValue,
  pushSnack: (snack: SnackBarConfig) => {
    set(state => ({
      snacks: [...state.snacks, snack],
    }));
    setTimeout(() => {
      set(state => ({
        snacks: state.snacks.filter(item => item.id !== snack.id),
      }));
    }, snack.timeOut);
  },
  popSnack: (id: string) => {
    set(state => ({
      snacks: state.snacks.filter(item => item.id !== id),
    }));
  },
  clearAllSnack: () => {
    set(state => {
      return {...state, snacks: []};
    });
  },
}));
export {useSnackStore};
