import {create, createJSONStorage, persist} from '@ac-mobile/common';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {Branch, Service} from '../requester-biz-service/apis/draw-number-api';

export type DrawNumberStoreState = {
  selectedBranch: Branch | null;
  selectedService: Service | null;
};

export type DrawNumberStoreActions = {
  setSelectedBranch: (branch: Branch | null) => void;
  setSelectedService: (service: Service | null) => void;
  clearSelection: () => void;
};

const initState: DrawNumberStoreState = {
  selectedBranch: null,
  selectedService: null,
};

export const useDrawNumberStore = create<
  DrawNumberStoreState & DrawNumberStoreActions
>()(
  persist(
    (set, _) => ({
      ...initState,
      setSelectedBranch: (branch: Branch | null) =>
        set({selectedBranch: branch}),
      setSelectedService: (service: Service | null) =>
        set({selectedService: service}),
      clearSelection: () => set({selectedBranch: null, selectedService: null}),
    }),
    {
      name: 'draw-number-store',
      storage: createJSONStorage(() => AsyncStorage),
    },
  ),
);
