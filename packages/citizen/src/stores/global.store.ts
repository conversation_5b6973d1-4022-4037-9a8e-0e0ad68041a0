import {
  create,
  createJSONStorage,
  persist,
  // useAuthStore,
} from '@ac-mobile/common';
import {ILoading} from '../model';
// import Toast from 'react-native-toast-message';
import AsyncStorage from '@react-native-async-storage/async-storage';
// import {DashboardApi} from '../../api';
// import {GetDanhMucLinhVuc} from '../../api/dashboard-api';

export type GlobalStoreState = {
  loading: ILoading;
  initialized: boolean;
  dsLinhVuc: {
    linhVucID: number;
    maLinhVuc: string;
    tenLinhVuc: string;
  }[];
};

export type GlobalStoreActions = {
  setLoading: (loadingState: ILoading) => void;
  updateState: () => void;
};

const initState: GlobalStoreState = {
  loading: 'none',
  initialized: false,
  dsLinhVuc: [],
};

export const useGlobalStore = create<GlobalStoreState & GlobalStoreActions>()(
  persist(
    (set, _) => ({
      ...initState,
      setLoading: (loadingState: ILoading) => set({loading: loadingState}),
      updateState: async () => {
        try {
          set({loading: 'loading'});
          // const [resDsLinhVuc] = await Promise.all([
          //   // Thêm hàm tải global
          //   // getDsLinhVuc(),
          //   // GetDanhMucLinhVuc(),
          // ]);
          set({
            // dsLinhVuc: resDsLinhVuc.data.data,
            initialized: true,
          });
          // Thử xóa access token để test refresh token
          // useAuthStore.getState().setAccessToken('fake-access-token');

          // TODO: demo, remove when feel ok
          // Toast.show({
          //   type: 'success',
          //   props: {
          //     title: 'Tải thành công danh mục lĩnh vực',
          //     content: 'This is some something props.content 👋',
          //   },
          // });
          // Toast.show({
          //   type: 'success',
          //   text1: 'Hello',
          //   text2: 'This is some something 👋',
          // });
          // Toast.show({
          //   type: 'error',
          //   text1: 'Hello error',
          //   text2: 'This is some something error 👋',
          // });
        } catch (error) {
          console.log(error, 'Loading global state 12');
        } finally {
          set({loading: 'none'});
        }
      },
    }),
    {
      name: 'dash-board-global-store-v1', // Nếu mà storage đổi cấu trúc, nâng version v1 -> v2
      storage: createJSONStorage(() => AsyncStorage),
    },
  ),
);
