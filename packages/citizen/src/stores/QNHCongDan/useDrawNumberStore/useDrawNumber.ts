import {AnyObj, create} from '@ac-mobile/common';
import {IBranchOption} from '../../../types';
import {drawNumberFunction} from '../../../api/cong-dan/dashboard-api';
import {EIsLoading} from '../../../types';
type initDataDrawNumber = {
  isLoadingApi: EIsLoading;
  brachOptions: IBranchOption[];
  getBranchOptions: () => void;
  handleReset: () => void;
};

const initState = {
  brachOptions: [],
  isLoadingApi: EIsLoading.NONE,
};

export const useDrawNumber = create<initDataDrawNumber>()((set, get) => ({
  ...initState,

  getBranchOptions: async () => {
    set({isLoadingApi: EIsLoading.LOADING});
    try {
      const params = {
        page: 1,
        pageSize: 100,
        orderBy: 'createdAt',
        orderType: 'DESC',
      };

      const response = await drawNumberFunction.getListBranch({
        params,
        headers,
      });

      if (!response?.data?.items) {
        set({isLoadingApi: EIsLoading.SUCCESS});
        return;
      }

      const branchOptionsFormat: IBranchOption[] = response?.data?.items.map(
        (item: AnyObj) => ({
          id: item?.id || 0,
          branchGroupId: item?.branch_group_id || 0,
          name: item?.name || '',
          address: item?.address || '',
          telephone: item?.telephone || '',
        }),
      );

      set({
        brachOptions: branchOptionsFormat,
      });
      set({isLoadingApi: EIsLoading.SUCCESS});
    } catch (error) {
      console.log('getBranchOptions', error);
      set({isLoadingApi: EIsLoading.ERROR});
    }
  },

  handleReset: () => {
    set({
      ...initState,
    });
  },
}));
