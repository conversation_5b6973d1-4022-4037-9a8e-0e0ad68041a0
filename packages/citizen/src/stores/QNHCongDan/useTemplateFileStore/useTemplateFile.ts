import {AnyObj, create} from '@ac-mobile/common';
import {EIsLoading} from '../../../types';

type initTemplateFile = {
  templateFiles: AnyObj[];
  isLoadingApi: EIsLoading;

  handleGetTemplateFile: () => void;
};

const initState = {
  templateFiles: [],
  isLoadingApi: EIsLoading.NONE,
};

export const useTemplateFile = create<initTemplateFile>()((set, get) => ({
  ...initState,

  handleGetTemplateFile: () => {
    try {
      set({isLoadingApi: EIsLoading.LOADING});
    } catch (error) {
      set({isLoadingApi: EIsLoading.ERROR});
    }
  },
}));
