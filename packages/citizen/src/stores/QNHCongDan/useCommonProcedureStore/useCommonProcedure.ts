import {AnyObj, create, useAuthStore} from '@ac-mobile/common';
import {
  handleFunction,
  headersDefault,
} from '../../../api/cong-dan/dashboard-api';
import {EIsLoading} from '../../../types';
import {buildUrlWithParams} from '../../../utils';
import {
  document,
  GetDocsParams,
} from '../../../requester-biz-service/apis/documents-api';
import {template} from '../../../requester-biz-service/apis/template-api';

export type IChoseCommonProcedure = {
  procedureId: string;
  procedureName: string;
};

// COMMENT: Kiểm tra lại dữ liệu
export type ITemplateSelected = {
  id: string;
  name: string;
  partnerData: AnyObj;
  data: AnyObj[];
};

export type IDsDonViOption = {
  code: string;
  donViCapChaID: number;
  donViID: number;
  mucDo: number;
  name: string;
  phuongXaID: number;
  quanHuyenID: number;
  tenDonVi: string;
  tenDonViCapCha: string;
  tinhThanhID: number;
};

export type IForwardData = {
  CapThucHien: string;
  DoiTuongThucHien: string;
  DonViThucHien: number;
  DonViCapCha: string;
  CapDonViID: number;
  TenDonViThucHien: string;
};

export type InputsData = {
  code: string;
  name: string;
  dataType: string;
  value: string | string[] | number[];
  required?: boolean;
};

type initDataCommonProcedure = {
  choseCommonProcedureItem: IChoseCommonProcedure | undefined;
  templateSelected: AnyObj | undefined;
  isLoadingApi: EIsLoading;
  dsDonViThucHienOptions: AnyObj[];
  forwardData: IForwardData | undefined;
  inputsData: InputsData[];
  isLoading: EIsLoading;
  eFormData: AnyObj | undefined;
  eFormUrl: string | undefined;
  templateFiles: AnyObj[] | [];
  templateDetail?: AnyObj;
  formName: string | null;
  formId: string | null;
  hasFormFieldInTemplateDetail: boolean;
  textSearchProcedureItem: string | undefined;

  handleGetProcedureItemDetail: (id: string) => void;
  getProcedureItem: (item: IChoseCommonProcedure) => void;
  handleGetDonViThucHien: () => void;
  updateForwardData: (data: IForwardData) => void;
  handleInitForm: ({ownerId}: {ownerId: string}) => void;
  handleGetTemplateFiles: ({
    page,
    pageSize,
    keyWord,
  }: {
    page: number;
    pageSize: number;
    keyWord?: string;
  }) => void;
  updateTextSearchProcedureItem: (
    textSearchProcedureItem: string | undefined,
  ) => void;

  handleReset: () => void;
};

const initState = {
  choseCommonProcedureItem: undefined,
  templateSelected: undefined,
  isLoadingApi: EIsLoading.NONE,
  dsDonViThucHienOptions: [],
  forwardData: undefined,
  inputsData: [],
  isLoading: EIsLoading.NONE,
  eFormData: undefined,
  eFormUrl: undefined,
  templateFiles: [],
  templateDetail: undefined,
  formName: null,
  formId: null,
  hasFormFieldInTemplateDetail: false,
  textSearchProcedureItem: undefined,
};

export const useCommonProcedure = create<initDataCommonProcedure>()(
  (set, get) => ({
    ...initState,
    // hasFormFieldInTemplateDetail is now a variable, set after getTemplateDetail
    /**
     * COMMENT: Xem lại dữ liệu khi làm đến màn nộp hồ sơ.
     * - Cần thêm phần format lại dữ liệu input của thành phần hồ sơ.
     */
    handleGetProcedureItemDetail: async (id: string) => {
      try {
        const response = await template.getTemplateDetail(id);
        if (response?.data?.data?.item) {
          const {name, partnerData, data, forms} = response?.data?.data?.item;
          let formValue;
          let hasFormField = false;
          let formName: string | null = null;
          let formId: string | null = null;
          if (Array.isArray(data)) {
            for (const field of data) {
              if (field.formId != null && field.dataType === 'form') {
                hasFormField = true;
                formName = field.name ?? '';
                formId = field.formId ?? '';
                break;
              }
            }
          }
          if (forms && forms.length > 0) {
            formValue = {
              formId: forms[0].formId,
              name: forms[0].name,
            };
          }
          const templateSelectedData: ITemplateSelected = {
            id: response?.data?.data?.item?.id || '',
            name: name || '',
            partnerData: partnerData || {},
            data: data || [],
          };
          const inputData =
            partnerData?.hoSoKemTheo?.filter(
              (item: AnyObj) => item.eform === false,
            ) || [];
          set({
            templateSelected: templateSelectedData,
            eFormData: formValue,
            inputsData: inputData || [],
            templateDetail: response?.data?.data?.item,
            hasFormFieldInTemplateDetail: hasFormField,
            formName,
            formId,
          });
        }
        set({isLoadingApi: EIsLoading.SUCCESS});
      } catch (error) {
        console.log('handleGetProcedureItemDetail', error);
        set({isLoadingApi: EIsLoading.ERROR});
      }
    },

    getProcedureItem: async (item: IChoseCommonProcedure) => {
      set({isLoadingApi: EIsLoading.LOADING});
      try {
        set({choseCommonProcedureItem: item});
        get().handleGetProcedureItemDetail(item.procedureId);
      } catch (error) {
        console.log('getProcedureItem', error);
        set({isLoadingApi: EIsLoading.ERROR});
      }
    },

    /**
     * COMMENT: Khi ấn button thực hiện thủ tục thì gọi function
     * - Lấy thông tin đơn vị thưc hiện.
     */
    handleGetDonViThucHien: async () => {
      set({isLoadingApi: EIsLoading.LOADING});
      try {
        if (!get().templateSelected) {
          return;
        }
        const {partnerData} = get().templateSelected;
        // COMMENT: xoá đi và thay thế lại
        const params = {
          // ThuTucHanhChinhID: partnerData?.tthcid,
          ThuTucHanhChinhID: 35461,
        };
        const response = await handleFunction.getDonViByTTHCID(params);
        const items = response?.data?.data;
        if (!items) {
          return;
        }
        set({
          dsDonViThucHienOptions: items,
          isLoadingApi: EIsLoading.SUCCESS,
        });
      } catch (error) {
        console.log('handleGetDonViThucHien', error);
        set({isLoadingApi: EIsLoading.ERROR});
      }
    },

    updateForwardData: (data: IForwardData) => {
      set({
        forwardData: data,
      });
    },

    handleInitForm: async ({ownerId}: {ownerId: string}) => {
      set({isLoadingApi: EIsLoading.LOADING});
      try {
        const accessToken = await useAuthStore.getState().accessToken;
        const eForm = get()?.eFormData;
        if (!eForm || !accessToken) {
          return;
        }
        const payload = {
          formId: eForm?.formId || '',
          name: eForm.name || '',
          jsonData: {},
          ownerId,
        };
        const response = await template.initUserForm({data: payload});
        if (!response?.data?.data) {
          return;
        }
        const params = {
          viewMode: 'edit',
          formUserId: response?.data?.data?.userFormId,
          token: accessToken || '',
          requesterServiceId: headersDefault['X-Requester-Service-Id'],
        };
        const baseUrl = 'https://dl-eform.dev.cluster02.fis-cloud.xplat.online';
        const eFormUrl = buildUrlWithParams({baseUrl: baseUrl, params: params});
        set({
          isLoadingApi: EIsLoading.SUCCESS,
          eFormUrl: eFormUrl,
        });
      } catch (error) {
        console.log('handleInitForm', error);
        set({isLoadingApi: EIsLoading.ERROR});
      }
    },

    handleGetTemplateFiles: async ({
      page,
      pageSize,
      keyword,
    }: {
      page: number;
      pageSize: number;
      keyword?: string;
    }) => {
      try {
        const params: GetDocsParams = {
          page,
          pageSize,
          keyword,
        };

        const response = await document.getDocs(params);
        console.log('handleGetTemplateFiles', response?.data?.data?.items);
      } catch (error) {
        console.log('handleGetTemplateFiles', error);
      }
    },

    updateTextSearchProcedureItem: (
      textSearchProcedureItem: string | undefined,
    ) => {
      set({textSearchProcedureItem});
    },

    handleReset: () => {
      set({
        ...initState,
      });
    },
  }),
);
