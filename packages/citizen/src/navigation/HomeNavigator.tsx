import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import HomeScreen from '../screens/dashboard/QNHCongDan/QNHDashboardHomeScreen';
import SharesScreen from '../screens/share/SharesScreen';
import ShareDocumentDetailScreen from '../screens/share/ShareDetailScreen';
import ShareDetailMetadata from '../screens/share/ShareDetailMetadata';
import ShareDetailDocsData from '../screens/share/ShareDetailDocsData';
import ShareDetailProcessHistory from '../screens/share/ShareDetailProcessHistory';
import DocumentsScreen from '../screens/document/DocumentsScreen';
import DocumentDetailScreen from '../screens/document/DocumentDetailScreen';
import DocumentUploadScreen from '../screens/document/DocumentUploadScreen';
import TicketDetailScreen from '../screens/dashboard/QNHCongDan/TicketDetail/TicketDetailScreen';
import InformationCommonScreen from '../screens/dashboard/QNHCongDan/CommonProcedures/CommonProcedureDetail/InformationCommonScreen';
import ProfileCommonScreen from '../screens/dashboard/QNHCongDan/CommonProcedures/CommonProcedureDetail/ProfileCommonScreen';
import StepCommonScreen from '../screens/dashboard/QNHCongDan/CommonProcedures/CommonProcedureDetail/StepCommonScreen';
import {CommonProcedureSubmitScreen} from '../screens/dashboard/QNHCongDan/CommonProcedureSubmit/CommonProcedureSubmitScreen';

export type HomeStackParamList = {
  Home: undefined;
  Upcoming: undefined;
  SharesScreen: undefined;
  ShareDetailScreen: {id: string; detail?: any};
  ShareDetailMetadata: {id: string; detail?: any};
  ShareDetailDocsData: {id: string; detail?: any};
  ShareDetailProcessHistory: {id: string; detail?: any};
  DocumentsScreen: {code?: string; name?: string};
  DocumentDetailScreen: {uri: string; item?: any};
  TicketDetail: {ticketId: string};
  InformationCommonScreen: undefined;
  ProfileCommonScreen: undefined;
  StepCommonScreen: undefined;
  DocumentUploadScreen: {
    autoPickType?:
      | 'camera'
      | 'image'
      | 'video'
      | 'videoCamera'
      | 'pdf'
      | 'document';
  };
  CommonProcedureSubmit: undefined;
};

const Home = createNativeStackNavigator<HomeStackParamList>();

const HomeNavigator = () => {
  return (
    <Home.Navigator
      screenOptions={{
        headerShown: false,
      }}>
      <Home.Screen name="Home" component={HomeScreen} />
      <Home.Screen name="SharesScreen" component={SharesScreen} />
      <Home.Screen
        name="ShareDetailScreen"
        component={ShareDocumentDetailScreen}
      />
      <Home.Screen name="ShareDetailMetadata" component={ShareDetailMetadata} />
      <Home.Screen name="ShareDetailDocsData" component={ShareDetailDocsData} />
      <Home.Screen
        name="ShareDetailProcessHistory"
        component={ShareDetailProcessHistory}
      />
      <Home.Screen name="DocumentsScreen" component={DocumentsScreen} />
      <Home.Screen
        name="DocumentDetailScreen"
        component={DocumentDetailScreen}
      />
      <Home.Screen
        name="DocumentUploadScreen"
        component={DocumentUploadScreen}
      />
      <Home.Screen
        name="TicketDetail"
        component={TicketDetailScreen}
        options={{
          headerShown: false,
        }}
      />
      <Home.Screen
        name="InformationCommonScreen"
        component={InformationCommonScreen}
        options={{
          headerShown: false,
        }}
      />
      <Home.Screen
        name="ProfileCommonScreen"
        component={ProfileCommonScreen}
        options={{
          headerShown: false,
        }}
      />
      <Home.Screen
        name="StepCommonScreen"
        component={StepCommonScreen}
        options={{
          headerShown: false,
        }}
      />
      <Home.Screen
        name="CommonProcedureSubmit"
        component={CommonProcedureSubmitScreen}
        options={{
          headerShown: false,
        }}
      />
    </Home.Navigator>
  );
};

export default HomeNavigator;
