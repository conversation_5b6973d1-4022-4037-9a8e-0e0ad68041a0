/* eslint-disable react/no-unstable-nested-components */
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import React from 'react';
import {Text} from 'react-native-paper';
import {HomeIcon} from '../icons/HomeIcon';
import HomeNavigator from './HomeNavigator';
import {HomeIconOutLine} from '../icons/HomeIconOutline';

export type TabsParamList = {
  HomeNavigator: undefined;
};

const Tabs = createBottomTabNavigator<TabsParamList>();

const TabsNavigator = () => {
  // const theme = useTheme();
  // return <TermsScreen />;
  return (
    <Tabs.Navigator
      screenOptions={{
        headerShown: false,
        tabBarStyle: {
          height: 0,
          justifyContent: 'center',
          alignItems: 'center',
          paddingBottom: 5,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          color: '#aaaaaa',
        },
        tabBarIconStyle: {
          justifyContent: 'center',
          alignItems: 'center',
        },
      }}>
      <Tabs.Screen
        name="HomeNavigator"
        component={HomeNavigator}
        options={{
          title: 'Trang chủ',
          tabBarIcon: ({focused}: {focused: boolean}) =>
            focused ? (
              <HomeIcon width={24} height={24} />
            ) : (
              <HomeIconOutLine width={24} height={24} />
            ),
          tabBarLabel: () => (
            <Text
              style={{
                fontSize: 12,
                fontWeight: 400,
              }}>
              Trang chủ
            </Text>
          ),
        }}
      />
    </Tabs.Navigator>
  );
};

export default TabsNavigator;
