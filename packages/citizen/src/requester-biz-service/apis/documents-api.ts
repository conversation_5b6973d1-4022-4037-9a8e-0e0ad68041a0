import {RequesterApi} from '../requester-client';

// Types based on the API response structure
interface DocumentItem {
  id: string;
  ownerId: string;
  name: string;
  issuerId: string;
  type: string;
  no: string;
  size: number;
  contentType: string;
  status: number;
  docType: number;
  data: any;
  shared: number;
  createdAt: string;
  updatedAt: string;
  requesterId?: string;
  tenantId?: string;
  templateId?: string;
  issuerOwnerId?: string;
  issuerServiceOwnerId?: string;
  isNew: boolean;
  template?: any;
  hash: string;
  isSigned: boolean;
  signatureInfo?: any;
  metadata?: any;
  keyword: string;
  signedBy?: string;
  issuerOrigin?: any;
  issuerInfo?: any;
  syncByDvc?: any;
  isFileDeleted: boolean;
  uri: string;
}

interface DocumentsResponse {
  data: {
    kind: string;
    items: DocumentItem[];
    totalItems: number;
  };
}

interface GetDocsParams {
  page?: number;
  pageSize?: number;
  orderBy?: string;
  orderDir?: 'asc' | 'desc';
  type?: string;
  status?: number;
  keyword?: string;
  uploadBy?: string; // Fixed: should be uploadBy, not uploadedBy
}

export type createDocDto = {
  docOwnerType: string;
  docType: string;
  docName: string;
  issuedOn: any;
  validFrom: any;
  validTo?: any;
  metaData: any;
  docFileType: any;
  docFileData: any;
  fileSize: number;
};

const getDocs = (params: GetDocsParams = {}) => {
  console.log('Fetching documents with params:', params);
  const defaultParams: GetDocsParams = {
    page: 1,
    pageSize: 10,
    status: 5,
    orderBy: 'createdAt',
    orderDir: 'desc',
    ...params,
  };

  return RequesterApi.get<DocumentsResponse>('/v1/documents', {
    params: defaultParams,
  });
};

const getDocInfo = (uri: string) => {
  return RequesterApi.get(`/v1/documents/method/info/${uri}`);
};

const addToWallet = (uri: string) => {
  return RequesterApi.post(`/v1/documents/method/wallet/${uri}`);
};

const removeFromWallet = (uri: string) => {
  return RequesterApi.delete(`/v1/documents/method/wallet/${uri}`);
};

const getRecentDoc = () => {
  return RequesterApi.get('/v1/documents/method/newly');
};

const createDoc = (doc: createDocDto) => {
  return RequesterApi.post('/v1/documents', doc);
};

export const document = {
  getRecentDoc,
  getDocs,
  addToWallet,
  removeFromWallet,
  getDocInfo,
  createDoc,
};

// Export types for use in components
export type {DocumentItem, DocumentsResponse, GetDocsParams};
