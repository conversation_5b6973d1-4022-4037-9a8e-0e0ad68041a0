import {RequesterApi} from '../requester-client';

interface UserProfile {
  kind: string;
  identificationId: string;
  userId: string;
  avatar: string | null;
  fullName: string;
  gender: number;
  email: string;
  level: number;
  phoneNumber: string;
  dateOfBirth: string;
  createdAt: string;
  updatedAt: string;
  subscriptionId: string;
  createdInfo: any;
  usedStorage: string;
}

interface SubscriptionInfo {
  subscriptionId: string;
  status: number;
  subscriptionName: string;
  subscriptionCode: string;
  options: {
    storage: {
      max: number;
      unit: string;
    };
  };
  createdAt: string;
  updatedAt: string;
}

interface UsageDetails {
  storage: {
    used: number;
    unit: string;
  };
}

interface UserMetadata {
  subscription: SubscriptionInfo;
  usageDetails: UsageDetails;
  tenants: any[];
}

interface UserResponse {
  data: UserProfile;
  metadata: UserMetadata;
}

const fillUserInfo = (): Promise<UserResponse> => {
  return RequesterApi.post('/v1/auth', {});
};

export const auth = {
  fillUserInfo,
};

export type {
  UserProfile,
  UserResponse,
  SubscriptionInfo,
  UsageDetails,
  UserMetadata,
};
