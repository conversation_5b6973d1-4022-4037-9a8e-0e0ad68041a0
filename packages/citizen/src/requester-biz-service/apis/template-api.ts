import {AnyObj} from '@ac-mobile/common';
import {RequesterApi} from '../requester-client';

export const headersDefault = {
  // 'X-Requester-Service-Id': 'eb5154cb-07d6-4b2c-99e5-f9bd07170d0f',
  'X-Requester-Service-Id': '09a2ddfc-f240-4d0a-89fa-2007b90ee551',
  'X-Requester-Service-Key-Auth-Profile': 'citizen',
};

const getTemplates = (query: any) => {
  return RequesterApi.get('/v1/templates', {...query});
};

const getTemplateDetail = (id: string) => {
  return RequesterApi.get(`/v1/templates/${id}`);
};

export const getTemplate = ({
  templateId,
  headers,
}: {
  templateId: string;
  headers?: AnyObj;
}) => {
  return RequesterApi.get('/v1/templates/' + templateId, {
    headers: {...headers, ...headersDefault},
  });
};

const getTemplateList = ({
  params,
  headers,
}: {
  params: AnyObj;
  headers?: AnyObj;
}) => {
  return RequesterApi.get('/v1/templates', {
    params,
    headers: {...headers, ...headersDefault},
  });
};

export const getShares = ({params}: {params: AnyObj}) => {
  return RequesterApi.get('/v1/shares', {
    params,
  });
};

const initUserForm = ({data, headers}: {data: AnyObj; headers?: AnyObj}) =>
  RequesterApi.post('v1/document-sets/method/user-form', data, {
    headers: {...headersDefault, ...headers},
  });

const downloadPdf = ({data, headers}: {data: AnyObj; headers?: AnyObj}) =>
  RequesterApi.post('v1/document-sets/method/print/html-to-pdf', data, {
    headers: {...headersDefault, ...headers},
  });

export const template = {
  getTemplates,
  getTemplateDetail,
  getTemplate,
  getTemplateList,
  getShares,
  initUserForm,
  downloadPdf,
};
