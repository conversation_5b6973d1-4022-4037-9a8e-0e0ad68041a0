import {Requester<PERSON><PERSON>} from '../requester-client';

// Types for Draw Number/Ticket API
interface Branch {
  id: string;
  name: string;
  address?: string;
  createdAt: string;
  updatedAt: string;
}

interface Service {
  id: string;
  name: string;
  branchId: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  keyword?: string;
  tenantId?: string;
  tags?: string; // Comma-separated tags like in Flutter
  metadata?: Record<string, any>;
  procedures?: any[];
}

interface DrawNumberMetadata {
  citizenCode: string;
}

interface DrawNumberRequest {
  branchId: string;
  serviceId: string;
  citizenCode: string;
  fullName: string;
  dateOfBirth: string;
  type?: 'NORMAL' | 'PRIORITY';
}

interface DrawNumberResponse {
  ticket_number: string;
  id: string;
  ticketNumber: string;
  branchId: string;
  serviceId: string;
  metadata: DrawNumberMetadata;
  type: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

interface TicketMetadata {
  citizenCode: string;
}

interface TicketDetailMetadata {
  citizenCode: string;
  fullName: string;
  dateOfBirth?: any;
}

interface TicketServiceList {
  name: string;
  qmsBranch: QmsBranchList;
}

interface QmsBranchList {
  name: string;
  address: string;
}

interface TicketService {
  qms_branch: any;
  id: number;
  name: string;
  description: string;
  createdAt: string;
  updatedAt?: any;
  keyword: string;
  tenantId: string;
  metadata: Record<string, any>;
  procedures: any[];
  branchId: number;
  qmsBranch: QmsBranch;
}

interface QmsBranch {
  id: number;
  code?: any;
  oneGateId: number;
  branchGroupId: number;
  name: string;
  address: string;
  telephone: string;
  createdAt: string;
  updatedAt: string;
  keyword: string;
  status: number;
}

interface TicketService {
  id: number;
  name: string;
  description: string;
  createdAt: string;
  updatedAt?: any;
  keyword: string;
  tenantId: string;
  metadata: Record<string, any>;
  procedures: any[];
  branchId: number;
  qmsBranch: QmsBranch;
}

interface CounterServing {
  id: number;
  name: string;
  code?: any;
}

interface TicketDetailResponse {
  kind: string;
  id: number;
  ticketNumber: string;
  serviceId: number;
  branchStationId?: any;
  timeIn: number;
  timeServed: number;
  timeOut: number;
  voteScore: number;
  status: string;
  createdAt: string;
  updatedAt: string;
  createdDate: string;
  type: string;
  metadata: TicketDetailMetadata;
  employeeId: number;
  keyword: string;
  isOnline: number;
  services: TicketService;
  servingTicket?: any;
  counterServing: CounterServing[];
}

interface TicketItem {
  id: number;
  ticketNumber: string;
  status: string;
  type: string;
  createdDate: string;
  isOnline: number;
  createdAt: string;
  updatedAt: string;
  metadata: TicketMetadata;
  services: TicketServiceList;
}

interface TicketListResponse {
  kind: string;
  items: TicketItem[];
  totalItems: number;
}

interface BranchListResponse {
  kind: string;
  items: Branch[];
  totalItems: number;
}

interface ServiceListResponse {
  kind: string;
  items: Service[];
  totalItems: number;
}

interface GetTicketsParams {
  page?: number;
  pageSize?: number;
  filter?: Record<string, any>;
  identificationId: string;
}

interface ListParams {
  page?: number;
  pageSize?: number;
  orderBy?: string;
  orderDir?: 'asc' | 'desc';
}

interface CancelTicketRequest {
  citizenCode: string;
  ticketId: number;
}

// API Functions
const drawNumberListBranch = (params: ListParams = {}) => {
  const defaultParams: ListParams = {
    page: 1,
    pageSize: 100,
    orderBy: 'createdAt',
    orderDir: 'desc',
    ...params,
  };

  return RequesterApi.get<BranchListResponse>('/v1/draw-number/list-branch', {
    params: defaultParams,
  });
};

const drawNumberListService = (branchId: string, params: ListParams = {}) => {
  const defaultParams: ListParams = {
    page: 1,
    pageSize: 100,
    orderBy: 'createdAt',
    orderDir: 'desc',
    ...params,
  };

  return RequesterApi.get<ServiceListResponse>('/v1/draw-number/list-service', {
    params: {
      ...defaultParams,
      branchId,
    },
  });
};

const createDrawNumber = async (
  data: DrawNumberRequest,
): Promise<DrawNumberResponse> => {
  const requestBody = {
    branch_id: data.branchId,
    metadata: {
      CitizenCode: data.citizenCode,
      DateOfBirth: data.dateOfBirth,
      FullName: data.fullName,
    },
    service_id: data.serviceId,
    type: data.type || 'NORMAL',
  };

  try {
    const response = await RequesterApi.post<DrawNumberResponse>(
      '/v1/draw-number',
      requestBody,
    );
    return response.data;
  } catch (error: any) {
    throw new Error(error.response.data?.message);
  }
};

const getTicketDetail = async (
  ticketId: string,
): Promise<TicketDetailResponse> => {
  try {
    console.log('Fetching ticket detail for ID:', ticketId); // Debug log
    const response = await RequesterApi.get<TicketDetailResponse>(
      `/v1/draw-number/get-ticket-detail/${ticketId}`,
    );
    return response.data;
  } catch (error: any) {
    console.error('Error in getTicketDetail:', error);
    throw new Error('Lỗi hệ thống, vui lòng thử lại sau');
  }
};

const cancelTicket = async (data: CancelTicketRequest) => {
  try {
    const response = await RequesterApi.patch('/v1/draw-number/cancel-ticket', {
      citizenCode: data.citizenCode,
      ticketId: data.ticketId,
    });
    return response.data;
  } catch (error: any) {
    console.error('Error in cancelTicket:', error);
    throw new Error('Không thể huỷ lịch hẹn.');
  }
};

const getTicketsByIdentificationId = async (
  params: GetTicketsParams,
): Promise<TicketListResponse> => {
  const {page = 1, pageSize = 10, filter = {}, identificationId} = params;

  try {
    const response = await RequesterApi.get<TicketListResponse>(
      '/v1/draw-number/list-tickets-by-identification-id',
      {
        params: {
          page,
          pageSize,
          filter: JSON.stringify(filter),
          identificationId,
        },
      },
    );
    return response.data;
  } catch (error: any) {
    console.error(
      'Error in getTicketsByIdentificationId:',
      JSON.stringify(error),
    );
    const message =
      error.response?.data?.message || 'Lỗi khi lấy danh sách lịch hẹn';
    throw new Error(message);
  }
};

export const drawNumber = {
  drawNumberListBranch,
  drawNumberListService,
  createDrawNumber,
  getTicketDetail,
  cancelTicket,
  getTicketsByIdentificationId,
};

// Export types for use in components
export type {
  Branch,
  Service,
  DrawNumberMetadata,
  DrawNumberRequest,
  DrawNumberResponse,
  TicketMetadata,
  TicketDetailMetadata,
  TicketService,
  TicketServiceList,
  QmsBranch,
  QmsBranchList,
  CounterServing,
  TicketDetailResponse,
  TicketItem,
  TicketListResponse,
  BranchListResponse,
  ServiceListResponse,
  GetTicketsParams,
  ListParams,
  CancelTicketRequest,
};
