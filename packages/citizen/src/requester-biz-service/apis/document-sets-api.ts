import {RequesterApi} from '../requester-client';
export type createDocSetDto = {
  tenantId?: string;
  name: string;
  type: string;
  templateId?: string;
  data: createDocSetDataItemDto[];
};
export type createDocSetDataItemDto = {
  name: string;
  code: string;
  uri?: string;
  dataType?: string;
};
export type updateDocSetDto = {
  name: string;
  templateId?: string;
  data: updateDocSetDataItemDto[];
};

export type updateDocSetDataItemDto = {
  name: string;
  code: string;
  uri?: string;
  dataType?: 'file' | 'text';
};

const getDocSets = (query: any) => {
  return RequesterApi.get('/v1/document-sets', {params: query});
};

const createDocSet = (docset: createDocSetDto) => {
  return RequesterApi.post('/v1/document-sets', docset);
};

const submitDocSet = ({
  uri,
  requesterServiceId,
  identification,
  forwardData,
}: {
  uri: string;
  requesterServiceId: string;
  identification: any;
  forwardData: any;
}) => {
  const requestBody = {
    requesterServiceId,
    requestDevice: 'MOBILE',
    transportOptions: {enable: false},
    forwardMetadata: {identification, forwardData},
    uri: uri,
  };
  console.log(
    'Submitting document set with request body:',
    JSON.stringify(requestBody, null, 2),
  );
  return RequesterApi.post('/v1/document-sets/method/submit', requestBody);
};

const deleteDocSet = (uri: string) => {
  return RequesterApi.delete(`/v1/document-sets/method/delete/${uri}`);
};

const updateDocSet = (uri: string, docset: updateDocSetDto) => {
  return RequesterApi.put(`/v1/document-sets/method/update/${uri}`, docset);
};

const getDocSetInfo = (uri: string) => {
  return RequesterApi.get(`/v1/document-sets/method/detail/${uri}`);
};

export const preprocessHtml = (html: string): string => {
  // Remove first and last character, replace escaped \n and 'n' with newline, and unescape backslashes
  let processed = html.slice(1, -1);
  // Replace \n or \\n with real newline
  processed = processed.replace(/\\n/g, '\n'); // Replace literal \n with newline
  processed = processed.replace(/\\\\n/g, '\n'); // Replace literal \\n with newline
  processed = processed.replace(/\bn\b/g, '\n'); // Replace 'n' as a word with newline
  processed = processed.replace(/\\/g, ''); // Remove remaining backslashes
  return processed;
};

const downloadPdf = (html: string) => {
  // const finalHTML = preprocessHtml(html);
  console.log('📥 Downloading PDF...: ', html);
  return RequesterApi.post(
    '/v1/document-sets/method/print/html-to-pdf',
    {html: html},
    {
      responseType: 'arraybuffer', // Expect binary PDF data
    },
  );
};

export const documentSet = {
  getDocSets,
  createDocSet,
  getDocSetInfo,
  deleteDocSet,
  updateDocSet,
  downloadPdf,
  submitDocSet,
};
