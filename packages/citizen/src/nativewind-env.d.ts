/// <reference types="nativewind/types" />
import { IUser } from '../src/model/i-user'; // Import IUser

export declare const useAuthStore: import("zustand").UseBoundStore<Omit<import("zustand").StoreApi<AuthStore<IUser> & AuthStoreActions<IUser>>, "persist"> & {
  persist: {
      setOptions: (options: Partial<import("zustand/middleware").PersistOptions<AuthStore<IUser> & AuthStoreActions<IUser>, unknown>>) => void;
      clearStorage: () => void;
      rehydrate: () => void | Promise<void>;
      hasHydrated: () => boolean;
      onHydrate: (fn: (state: AuthStore<IUser> & AuthStoreActions<IUser>) => void) => () => void;
      onFinishHydration: (fn: (state: AuthStore<IUser> & AuthStoreActions<IUser>) => void) => () => void;
      getOptions: () => Partial<import("zustand/middleware").PersistOptions<AuthStore<IUser> & AuthStoreActions<IUser>, unknown>>;
  };
}>;

