import React, {useState} from 'react';
import {
  Text,
  View,
  StyleSheet,
  TextInput,
  Alert,
  ScrollView,
  Platform,
  NativeModules,
} from 'react-native';
import RNFS from 'react-native-fs';
import {useRoute, useNavigation} from '@react-navigation/native';
import {CCAppBar} from '../../components/QNH_CongDanComponents/HeaderComponent/CCAppBar';
import type {DocumentUploadScreenRouteProp, PickedFile} from './types';
import {FilePickerButtons} from './components/FilePickerButtons';
import {FilePreviewList} from './components/FilePreviewList';
import {Button, useTheme} from 'react-native-paper';
import ErrorBoundary from '../../components/QNH_CongDanComponents/ErrorBoundary';
import {useMutation, useQueryClient} from 'react-query';
import {
  document,
  createDocDto,
} from '../../requester-biz-service/apis/documents-api';

const convertHeicToJpeg = async (uri: string): Promise<string> => {
  try {
    const {RNHeicConverter} = NativeModules;
    const result = await RNHeicConverter.convert({
      path: uri,
      quality: 0.8, // JPEG quality (0.0 to 1.0)
    });

    if (!result?.path) {
      throw new Error('Conversion failed - no output path');
    }

    console.log('Successfully converted HEIC to JPEG');
    return result.path;
  } catch (error) {
    // Just log the error and continue with original file
    console.log('HEIC conversion attempt:', error);
    return uri;
  }
};

const DocumentUploadScreen = () => {
  const route = useRoute<DocumentUploadScreenRouteProp>();
  const navigation = useNavigation();
  const {autoPickType} = route.params || {};
  const queryClient = useQueryClient();
  const theme = useTheme();

  const [documentName, setDocumentName] = useState('');
  const [pickedFiles, setPickedFiles] = useState<PickedFile[]>([]);

  const uploadMutation = useMutation({
    mutationFn: (data: createDocDto) => document.createDoc(data),
    onSuccess: () => {
      // Invalidate and refetch documents queries
      queryClient.invalidateQueries(['documents']);
      Alert.alert('Thành công', 'Tài liệu đã được tải lên thành công!', [
        {
          text: 'OK',
          onPress: () => navigation.goBack(),
        },
      ]);
    },
    onError: (error: any) => {
      console.error('Upload error:', error);
      // Print out response body if available
      if (error?.response?.data) {
        console.error(
          'Error response data:',
          JSON.stringify(error.response.data, null, 2),
        );
      }
      if (error?.response?.status) {
        console.error('Error status:', error.response.status);
      }
      Alert.alert('Lỗi', 'Không thể tải lên tài liệu. Vui lòng thử lại.');
    },
  });

  const isImageMode = autoPickType === 'camera' || autoPickType === 'image';
  const showPickerMenu = !autoPickType || !isImageMode;

  const handleFilesPicked = (newFiles: PickedFile[]) => {
    if (isImageMode) {
      setPickedFiles(prev => [...prev, ...newFiles]);
    } else {
      setPickedFiles(newFiles);
    }
  };

  const getBase64Content = async (file: PickedFile): Promise<string> => {
    if (Platform.OS === 'web') {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file as any);
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = error => reject(error);
      });
    } else {
      try {
        // Decode URI to handle spaces and special characters
        const decodedUri = decodeURIComponent(file.uri);
        let filePath = decodedUri.replace('file://', '');

        // Check if the file is HEIC/HEIF
        const ext = getFileExtension(file.name || file.uri);
        if (ext.toLowerCase() === 'heic' || ext.toLowerCase() === 'heif') {
          const convertedPath = await convertHeicToJpeg(decodedUri);

          // Only update if conversion was successful (path changed)
          if (convertedPath !== decodedUri) {
            filePath = convertedPath.replace('file://', '');
            // Update file type and name to reflect conversion
            file.type = 'image/jpeg';
            file.name =
              file.name?.replace(/\.(heic|heif)$/i, '.jpg') || 'converted.jpg';
          }
        }

        const base64Content = await RNFS.readFile(filePath, 'base64');
        return base64Content;
      } catch (error) {
        console.error('Error reading file:', error);
        throw error;
      }
    }
  };

  const getFileExtension = (fileName: string): string => {
    const match = /\.([^.]+)$/.exec(fileName?.toLowerCase());
    return match ? match[1] : '';
  };

  const getDocFileType = (
    fileName: string,
    mimeType: string | null,
  ): string => {
    const ext = getFileExtension(fileName);
    switch (ext) {
      case 'pdf':
        return 'pdf';
      case 'html':
      case 'htm':
        return 'html';
      case 'jpg':
      case 'jpeg':
      case 'heic': // Add HEIC as a supported image type
      case 'heif': // Also support HEIF
        return 'jpg'; // We'll convert HEIC/HEIF to JPG
      case 'png':
        return 'png';
      case 'mp4':
      case 'm4v':
      case 'mov':
        return 'mp4';
      case 'mp3':
      case 'wav':
      case 'm4a':
        return 'mp3';
      default:
        // If we can't determine from extension, try to use the mime type
        if (mimeType?.includes('pdf')) {
          return 'pdf';
        }
        if (mimeType?.includes('html')) {
          return 'html';
        }
        if (
          mimeType?.includes('jpg') ||
          mimeType?.includes('jpeg') ||
          mimeType?.includes('heic') ||
          mimeType?.includes('heif')
        ) {
          return 'jpg';
        }
        if (mimeType?.includes('png')) {
          return 'png';
        }
        if (mimeType?.includes('video') || mimeType?.includes('mp4')) {
          return 'mp4';
        }
        if (mimeType?.includes('audio') || mimeType?.includes('mp3')) {
          return 'mp3';
        }
        return 'pdf'; // default to pdf if we can't determine
    }
  };

  // Merge images to PDF utility (from FilePreviewList)
  const generatePdfFromImages = async (
    imageFilesGen: PickedFile[],
  ): Promise<PickedFile> => {
    let jsPDFModule = require('jspdf');
    const jsPDF = jsPDFModule.default ? jsPDFModule.default : jsPDFModule;
    let RNFSLocal = RNFS;
    if (!RNFSLocal) {
      RNFSLocal = require('react-native-fs');
    }
    try {
      const pdf = new jsPDF();
      let isFirstPage = true;
      for (const imageFile of imageFilesGen) {
        const base64Data = await RNFSLocal.readFile(imageFile.uri, 'base64');
        const imageDataUri = `data:${imageFile.type};base64,${base64Data}`;
        if (!isFirstPage) {
          pdf.addPage();
        }
        const pdfWidth = pdf.internal.pageSize.getWidth();
        const pdfHeight = pdf.internal.pageSize.getHeight();

        // Get image dimensions
        let imgWidth = pdfWidth - 20;
        let imgHeight = pdfHeight - 20;
        let naturalWidth = imgWidth;
        let naturalHeight = imgHeight;
        try {
          // Try to get image size using Image.getSize (RN) or HTMLImageElement (web)
          if (Platform.OS !== 'web') {
            const {Image} = require('react-native');
            await new Promise<void>(resolve => {
              Image.getSize(
                imageFile.uri,
                (width: number, height: number) => {
                  naturalWidth = width;
                  naturalHeight = height;
                  resolve();
                },
                (_error: any) => {
                  resolve(); // fallback to default
                },
              );
            });
          }
        } catch (e) {
          // fallback to default
        }
        // Calculate aspect ratio fit
        const ratio = Math.min(
          (pdfWidth - 20) / naturalWidth,
          (pdfHeight - 20) / naturalHeight,
        );
        imgWidth = naturalWidth * ratio;
        imgHeight = naturalHeight * ratio;
        const x = (pdfWidth - imgWidth) / 2;
        const y = (pdfHeight - imgHeight) / 2;
        pdf.addImage(imageDataUri, 'JPEG', x, y, imgWidth, imgHeight);
        isFirstPage = false;
      }
      const pdfOutput = pdf.output('datauristring');
      const base64Data = pdfOutput.split(',')[1];
      const pdfPath = `${
        RNFSLocal.DocumentDirectoryPath
      }/merged_images_${Date.now()}.pdf`;
      await RNFSLocal.writeFile(pdfPath, base64Data, 'base64');
      return {
        uri: `file://${pdfPath}`,
        name: `merged_images_${Date.now()}.pdf`,
        type: 'application/pdf',
        size: base64Data.length * 0.75,
      };
    } catch (error) {
      console.error('Error generating PDF:', error);
      throw error;
    }
  };

  const handleUpload = async () => {
    if (!documentName.trim()) {
      Alert.alert('Thông báo', 'Vui lòng nhập tên tài liệu.');
      return;
    }
    if (pickedFiles.length === 0) {
      Alert.alert('Thông báo', 'Vui lòng chọn tệp để tải lên.');
      return;
    }
    try {
      // Check if all files are images
      const allImages = pickedFiles.every(
        f => f.type && f.type.startsWith('image/'),
      );
      let fileToUpload: PickedFile;
      if (allImages) {
        // Merge images to PDF
        fileToUpload = await generatePdfFromImages(pickedFiles);
      } else {
        // Use the first file (pdf, video, etc.)
        fileToUpload = pickedFiles[0];
      }
      let base64Content = await getBase64Content(fileToUpload);
      // Handle HEIC/HEIF for single image (should not happen if merged, but keep for safety)
      // if (
      //   fileToUpload.type === 'image/heic' ||
      //   fileToUpload.type === 'image/heif'
      // ) {
      //   try {
      //     const jpegFilePath = await convertHeicToJpeg(fileToUpload.uri);
      //     if (jpegFilePath !== fileToUpload.uri) {
      //       const jpegFile = {
      //         ...fileToUpload,
      //         uri: jpegFilePath,
      //         type: 'image/jpeg',
      //         name: `${fileToUpload.name.split('.')[0]}.jpg`,
      //       };
      //       base64Content = await getBase64Content(jpegFile);
      //       fileToUpload = jpegFile;
      //       console.log('Successfully converted and processing HEIC image');
      //     } else {
      //       console.log('Using original HEIC file');
      //     }
      //   } catch (error) {
      //     console.warn('HEIC processing:', error);
      //   }
      // }
      const documentData: createDocDto = {
        docOwnerType: 'Resident',
        docType: 'Personal',
        docName: documentName,
        issuedOn: new Date().toISOString(),
        validFrom: new Date().toISOString(),
        metaData: {
          originalName: fileToUpload.name,
          size: fileToUpload.size,
          type: fileToUpload.type,
        },
        fileSize: fileToUpload.size,
        docFileType: getDocFileType(fileToUpload.name, fileToUpload.type),
        docFileData: base64Content,
      };
      uploadMutation.mutate(documentData);
    } catch (error) {
      console.error('Error preparing upload:', error);
      Alert.alert(
        'Lỗi',
        'Không thể chuẩn bị tài liệu để tải lên. Vui lòng thử lại.',
      );
    }
  };

  return (
    <ErrorBoundary name="DocumentsScreen">
      <View style={[styles.container, {backgroundColor: theme.colors.surface}]}>
        {/* Use theme color */}
        <CCAppBar label="Tải lên tài liệu" isBack={true} />
        <View style={[styles.mainContainer]}>
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}>
            <View style={styles.form}>
              <Text style={[styles.label, {color: theme.colors.onSurface}]}>
                Tên tài liệu *
              </Text>
              <TextInput
                style={[
                  styles.input,
                  {
                    backgroundColor: theme.colors.surfaceVariant,
                    color: theme.colors.onSurface,
                    borderColor: theme.colors.primary,
                  },
                ]}
                value={documentName}
                onChangeText={setDocumentName}
                placeholder="Nhập tên tài liệu..."
                placeholderTextColor={theme.colors.onSurfaceVariant}
              />

              <Text style={[styles.label, {color: theme.colors.onSurface}]}>
                Chọn tệp
              </Text>
              <FilePreviewList
                files={pickedFiles}
                onFilesChange={setPickedFiles}
                isImageMode={isImageMode}
              />
              <FilePickerButtons
                isImageMode={isImageMode}
                showPickerMenu={showPickerMenu}
                onFilesPicked={handleFilesPicked}
                autoPickType={autoPickType}
                selectedFiles={pickedFiles}
              />
            </View>
          </ScrollView>

          <View style={styles.bottomButtonContainer}>
            <Button
              onPress={handleUpload}
              mode="contained"
              loading={uploadMutation.isLoading}
              disabled={
                !documentName.trim() ||
                pickedFiles.length === 0 ||
                uploadMutation.isLoading
              }>
              Tải lên
            </Button>
          </View>
        </View>
      </View>
    </ErrorBoundary>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: '#f5f5f5', // Remove hardcoded color
  },
  mainContainer: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  form: {
    padding: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    // color: '#333', // Remove hardcoded color
  },
  input: {
    // backgroundColor: '#fff', // Remove hardcoded color
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    fontSize: 16,
    // color: '#333', // Remove hardcoded color
    borderWidth: 1,
    // borderColor: '#ddd', // Remove hardcoded color
  },
  bottomButtonContainer: {
    padding: 4,
  },
});

export default DocumentUploadScreen;
