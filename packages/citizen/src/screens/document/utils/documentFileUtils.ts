// Utility functions for XML parsing and base64 extraction
import xmlParser from 'xml-parser';
import RNFS from 'react-native-fs';

export const getBase64FromXML = (type: string, xmlData: string) => {
  const xmlToJSON = xmlParser(xmlData);
  try {
    const docBodyBase64 =
      xmlToJSON.root.children.find((child: any) => {
        return (
          child.name === 'DocBody' &&
          child.attributes.id === 'docfile' &&
          child.attributes.type === type
        );
      })?.content || null;
    return docBodyBase64;
  } catch (error) {
    console.error(error, 'error at getBase64FromXML');
    return null;
  }
};

export const extractBase64FromXML = (xmlString: string, fileType: string) => {
  try {
    let base64Content = getBase64FromXML(fileType, xmlString);
    if (!base64Content && fileType.includes('/')) {
      const extension = fileType.split('/').pop()?.toLowerCase();
      if (extension) {
        base64Content = getBase64FromXML(extension, xmlString);
      }
    }
    if (!base64Content) {
      const commonTypes = ['PDF', 'pdf', 'JPEG', 'jpeg', 'PNG', 'png'];
      for (const type of commonTypes) {
        base64Content = getBase64FromXML(type, xmlString);
        if (base64Content) {
          break;
        }
      }
    }
    if (!base64Content) {
      console.warn('No DocBody element found with valid content');
      return null;
    }
    base64Content = base64Content
      .trim()
      .replace(/^data:.*?;base64,/, '')
      .replace(/\s/g, '');
    if (!isValidBase64(base64Content)) {
      console.warn('Invalid base64 content detected');
      return null;
    }
    return base64Content;
  } catch (error) {
    console.error('Error extracting base64 from XML:', error);
    return null;
  }
};

export const isValidBase64 = (str: string): boolean => {
  try {
    const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
    return base64Regex.test(str) && str.length % 4 === 0 && str.length > 0;
  } catch {
    return false;
  }
};

export const getFileExtensionFromMimeType = (mimeType: string): string => {
  const mimeToExt: Record<string, string> = {
    'application/pdf': 'pdf',
    'image/jpeg': 'jpeg',
    'image/jpg': 'jpg',
    'image/png': 'png',
    'image/gif': 'gif',
    'image/bmp': 'bmp',
    'image/webp': 'webp',
    'video/mp4': 'mp4',
    'video/avi': 'avi',
    'video/mov': 'mov',
    'video/wmv': 'wmv',
  };
  const cleanMimeType = mimeType.toLowerCase().trim();
  return mimeToExt[cleanMimeType] || 'bin';
};

export const saveBase64ToFile = async (
  base64String: string,
  filePath: string,
): Promise<void> => {
  try {
    await RNFS.writeFile(filePath, base64String, 'base64');
    console.log('File saved to:', filePath);
  } catch (error) {
    console.error('Error saving base64 to file:', error);
    throw error;
  }
};

export const docType = (docFileType: string) => {
  switch (docFileType) {
    case 'application/pdf':
      return 'PDF';
    case 'image/jpeg':
    case 'image/png':
      return 'Image Document';
    case 'video/mp4':
      return 'Video';
    default:
      return docFileType;
  }
};
