import React from 'react';
import {View, Text} from 'react-native';
import {InfoRowProps} from './types';
import {useTheme} from 'react-native-paper';

const InfoRow: React.FC<InfoRowProps> = ({
  label,
  value,
  valueStyle,
  labelStyle,
}) => {
  const theme = useTheme();
  return (
    <View style={{marginBottom: 12}}>
      <Text
        style={[
          {
            fontSize: 14,
            fontWeight: '500',
            marginBottom: 4,
            color: theme.colors.onSurface,
          },
          labelStyle,
        ]}>
        {label}
      </Text>
      <Text
        style={[
          {fontSize: 16, fontWeight: '400', color: theme.colors.onSurface},
          valueStyle,
        ]}>
        {value}
      </Text>
    </View>
  );
};

export default InfoRow;
