import React from 'react';
import {View, Text, TouchableOpacity, ActivityIndicator} from 'react-native';
import Pdf from 'react-native-pdf';
import ImageViewing from 'react-native-image-viewing';
import {useTheme} from 'react-native-paper';
import {Image} from 'react-native';

interface FilePreviewProps {
  fileType: string;
  processedFilePath: string | null;
  xmlFilePath: string | null;
  loading: boolean;
  cacheLoading: boolean;
  setError: (msg: string) => void;
  isImageViewerVisible: boolean;
  setIsImageViewerVisible: (v: boolean) => void;
  styles: any;
}

const FilePreview: React.FC<FilePreviewProps> = ({
  fileType,
  processedFilePath,
  xmlFilePath,
  loading,
  cacheLoading,
  setError,
  isImageViewerVisible,
  setIsImageViewerVisible,
  styles,
}) => {
  const theme = useTheme();

  if (loading || cacheLoading) {
    return (
      <View
        style={[
          styles.loadingContainer,
          {backgroundColor: theme.colors.surface},
        ]}>
        <ActivityIndicator size="large" />
        <Text style={styles.loadingText}>
          {loading ? 'Đang tải thông tin...' : 'Đang xử lý tài liệu...'}
        </Text>
      </View>
    );
  }

  if (fileType.includes('pdf')) {
    return (
      <View
        style={[
          styles.contentContainer,
          {backgroundColor: theme.colors.surface},
        ]}>
        {processedFilePath ? (
          <Pdf
            source={{uri: `file://${processedFilePath}`}}
            style={styles.pdfViewer}
            onError={err => {
              console.error('PDF error:', err);
              setError('Failed to load PDF content');
            }}
          />
        ) : (
          <View style={styles.demoCard}>
            <Text style={styles.demoTitle}>📄 PDF Document</Text>
            <Text style={styles.demoDescription}>
              PDF content is being processed from XML file.
              {xmlFilePath ? `\nXML cached at: ${xmlFilePath}` : ''}
            </Text>
          </View>
        )}
      </View>
    );
  }

  if (fileType.includes('image')) {
    return (
      <View style={styles.contentContainer}>
        {processedFilePath ? (
          <>
            <TouchableOpacity
              style={[
                styles.imageContainer,
                {backgroundColor: theme.colors.surface},
              ]}
              onPress={() => setIsImageViewerVisible(true)}>
              <Image
                source={{uri: `file://${processedFilePath}`}}
                style={styles.documentImage}
                resizeMode="contain"
                onError={() => {
                  setError('Failed to load image content');
                }}
              />
            </TouchableOpacity>
            <ImageViewing
              images={[{uri: `file://${processedFilePath}`}]}
              imageIndex={0}
              visible={isImageViewerVisible}
              onRequestClose={() => setIsImageViewerVisible(false)}
              backgroundColor={theme.colors.surface}
              doubleTapToZoomEnabled={true}
            />
          </>
        ) : (
          <View style={styles.demoCard}>
            <Text style={styles.demoTitle}>🖼️ Image Document</Text>
            <Text style={styles.demoDescription}>
              Image content is being processed from XML file.
              {xmlFilePath ? `\nXML cached at: ${xmlFilePath}` : ''}
            </Text>
          </View>
        )}
      </View>
    );
  }

  if (fileType.includes('video')) {
    return (
      <View style={styles.contentContainer}>
        <View style={styles.demoCard}>
          <Text style={styles.demoTitle}>🎥 Video Document</Text>
          <Text style={styles.demoDescription}>
            Video playback would be implemented here.
            {processedFilePath
              ? `\nVideo file: ${processedFilePath}`
              : '\nProcessing video from XML...'}
          </Text>
          <View style={styles.videoDemoContainer}>
            <Text style={styles.videoDemoText}>Video Player Placeholder</Text>
          </View>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.contentContainer}>
      <Text style={styles.errorText}>Unsupported file type: {fileType}</Text>
    </View>
  );
};

export default FilePreview;
