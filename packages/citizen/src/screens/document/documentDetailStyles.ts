import {StyleSheet} from 'react-native';

const documentDetailStyles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabContainer: {
    flexDirection: 'row',
    // borderBottomWidth: 1,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
    borderBottomWidth: 2,
  },
  activeTab: {
    borderBottomWidth: 2,
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
  },
  tabContent: {
    flex: 1,
  },
  contentContainer: {
    flex: 1,
  },
  demoCard: {
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
  },
  demoTitle: {
    fontSize: 24,
    fontWeight: '600',
    marginBottom: 12,
  },
  demoDescription: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 20,
  },
  videoDemoContainer: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderStyle: 'dashed',
  },
  videoDemoText: {
    fontSize: 16,
    fontWeight: '500',
  },
  imageDemoContainer: {
    width: '100%',
    height: 150,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderStyle: 'dashed',
  },
  imageDemoText: {
    fontSize: 16,
    fontWeight: '500',
  },
  viewButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 6,
    marginTop: 10,
  },
  viewButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  webView: {
    flex: 1,
  },
  pdfViewer: {
    flex: 1,
  },
  imageContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  documentImage: {
    width: '100%',
    height: '100%',
  },
  infoContainer: {
    flex: 1,
    padding: 16,
  },
  infoSection: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  infoRow: {
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '400',
  },
  infoValueSmall: {
    fontSize: 12,
    fontWeight: '400',
    fontFamily: 'monospace',
  },
  additionalDataItem: {
    padding: 12,
    borderRadius: 6,
    marginBottom: 8,
  },
  additionalDataText: {
    fontSize: 12,
    fontFamily: 'monospace',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 6,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  errorButtonContainer: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 10,
  },
});

export default documentDetailStyles;
