export interface DocumentDetailData {
  fileUrl: string;
  metadata?: any;
  hash: string;
  docName: string;
  docType: string;
  docId: string;
  docFileType: string;
  docOwnerId: string;
  docOwnerType: string;
  uri: string;
  additionalData?: any[];
  status?: number;
  createdAt: string;
}
export interface InfoRowProps {
  label: string;
  value: React.ReactNode;
  valueStyle?: any;
  labelStyle?: any;
}
import type {RouteProp} from '@react-navigation/native';
import type {HomeStackParamList} from '../../navigation/HomeNavigator';

export type DocumentUploadScreenRouteProp = RouteProp<
  HomeStackParamList,
  'DocumentUploadScreen'
>;

export interface PickedFile {
  uri: string;
  name: string;
  type: string;
  size: number;
}

export type FilePickerMode = 'single' | 'multiple';

export interface FilePickerButtonsProps {
  isImageMode: boolean;
  showPickerMenu: boolean;
  onFilesPicked: (files: PickedFile[]) => void;
  autoPickType?:
    | 'camera'
    | 'image'
    | 'video'
    | 'videoCamera'
    | 'pdf'
    | 'document';
  selectedFiles: PickedFile[];
}
