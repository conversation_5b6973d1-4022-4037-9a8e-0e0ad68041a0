import React from 'react';
import {View, Text} from 'react-native';
import InfoRow from './InfoRow';
import {formatDateTime} from '../../utils/fn';
import {getCitizenCode} from '../../stores/user.store';
import {docType} from './utils/documentFileUtils';
import {formatFileSize} from './utils';
import {useTheme} from 'react-native-paper';
import {DocumentDetailData} from './types';

interface DocumentInfoProps {
  document: DocumentDetailData;
  fileSize: number;
  userProfile: any;
}

const DocumentInfo: React.FC<DocumentInfoProps> = ({
  document,
  fileSize,
  userProfile,
}) => {
  const theme = useTheme();
  return (
    <View style={{borderRadius: 12, padding: 16, marginBottom: 16}}>
      <Text
        style={{
          fontSize: 18,
          fontWeight: '600',
          marginBottom: 16,
          color: theme.colors.onSurface,
        }}>
        Thông tin tài liệu
      </Text>
      <InfoRow label="Tên tài liệu:" value={document.docName} />
      <InfoRow
        label="Loại tài liệu:"
        value={
          document.docOwnerId === getCitizenCode(userProfile)
            ? 'Giấy tờ tải lên'
            : 'Giấy tờ được cấp'
        }
      />
      <InfoRow label="Định dạng:" value={docType(document.docFileType)} />
      <InfoRow label="URI:" value={document.uri} />
      <InfoRow
        label="Mã hash:"
        value={document.hash}
        valueStyle={{fontSize: 12, fontFamily: 'monospace'}}
      />
      <InfoRow
        label="Chủ sở hữu:"
        value={
          document.docOwnerId === getCitizenCode(userProfile)
            ? 'Bạn'
            : 'Cá nhân/Tổ chức khác'
        }
      />
      <InfoRow
        label="Thời gian tạo:"
        value={formatDateTime(document.createdAt)}
      />
      <InfoRow label="Kích thước:" value={formatFileSize(fileSize || 0)} />
    </View>
  );
};

export default DocumentInfo;
