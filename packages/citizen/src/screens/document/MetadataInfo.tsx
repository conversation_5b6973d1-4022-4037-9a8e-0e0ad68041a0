import React from 'react';
import {View, Text} from 'react-native';
import InfoRow from './InfoRow';
import {formatDateTime} from '../../utils/fn';

interface DvcInfoProps {
  dvc: any;
}

export const DvcInfo: React.FC<DvcInfoProps> = ({dvc}) => {
  if (!dvc) return null;
  return (
    <>
      <InfoRow label="Nơi xử lý:" value={dvc.processingDepartment || ''} />
      <InfoRow label="Kết thúc xử lý:" value={dvc.endProcessedAt || ''} />
    </>
  );
};

interface CertInfoProps {
  certs: any[];
}

export const CertInfo: React.FC<CertInfoProps> = ({certs}) => {
  if (!certs || !Array.isArray(certs) || certs.length === 0) return null;
  return (
    <View style={{marginTop: 12}}>
      <Text style={{fontSize: 16, fontWeight: '600', marginBottom: 12}}>
        <PERSON><PERSON><PERSON> <PERSON><PERSON> số
      </Text>
      {certs.map((cert, idx) => (
        <View key={idx} style={{marginBottom: 12}}>
          <InfoRow
            label="Tên người ký:"
            value={cert.issuedTo?.commonName || ''}
          />
          <InfoRow
            label="Tổ chức:"
            value={cert.issuedTo?.organizationName || ''}
          />
          <InfoRow
            label="Hiệu lực từ:"
            value={
              cert.validityPeriod?.notBefore
                ? formatDateTime(cert.validityPeriod.notBefore)
                : ''
            }
          />
          <InfoRow
            label="Hiệu lực đến:"
            value={
              cert.validityPeriod?.notAfter
                ? formatDateTime(cert.validityPeriod.notAfter)
                : ''
            }
          />
        </View>
      ))}
    </View>
  );
};
