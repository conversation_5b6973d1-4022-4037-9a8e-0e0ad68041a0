import React from 'react';
import {View, Text, TouchableOpacity} from 'react-native';

interface ErrorStateProps {
  error: string | null;
  lastErrorStatus: number | null;
  retryAttempts: number;
  handleRetry: () => void;
  handleGoBack: () => void;
  styles: any;
  Button: any;
}

const ErrorState: React.FC<ErrorStateProps> = ({
  error,
  lastErrorStatus,
  retryAttempts,
  handleRetry,
  handleGoBack,
  styles,
  Button,
}) => {
  const showGoBackOption = lastErrorStatus === 400 && retryAttempts >= 2;
  return (
    <View style={styles.errorContainer}>
      <Text style={styles.errorText}>{error}</Text>
      <View style={styles.errorButtonContainer}>
        {!showGoBackOption ? (
          <TouchableOpacity style={styles.retryButton} onPress={handleRetry}>
            <Text style={styles.retryButtonText}>
              {retryAttempts > 0 ? 'Thử lại lần nữa' : 'Thử lại'}
            </Text>
          </TouchableOpacity>
        ) : (
          <>
            <Button onPress={handleRetry}>Thử lại lần cuối</Button>
            <Button onPress={handleGoBack}>Quay lại</Button>
          </>
        )}
      </View>
    </View>
  );
};

export default ErrorState;
