import React from 'react';
import {View, ActivityIndicator, Text} from 'react-native';

interface LoadingStateProps {
  loading: boolean;
  cacheLoading: boolean;
  styles: any;
  theme: any;
}

const LoadingState: React.FC<LoadingStateProps> = ({
  loading,
  cacheLoading,
  styles,
  theme,
}) => (
  <View
    style={[styles.loadingContainer, {backgroundColor: theme.colors.surface}]}>
    <ActivityIndicator size="large" />
    <Text style={styles.loadingText}>
      {loading ? 'Đang tải thông tin...' : '<PERSON>ang xử lý tài liệu...'}
    </Text>
  </View>
);

export default LoadingState;
