import React, {useState} from 'react';
import {Platform, View, StyleSheet, Alert, FlatList} from 'react-native';
import {Button, useTheme, Text, ActivityIndicator} from 'react-native-paper';
import {FilePreviewModal} from './FilePreviewModal';
import {FilePreviewItem} from './FilePreviewItem';
import type {PickedFile} from '../types';
import RNFS from 'react-native-fs';
import jsPDF from 'jspdf';
import FileViewer from 'react-native-file-viewer';

interface FilePreviewListProps {
  files: PickedFile[];
  onFilesChange: (files: PickedFile[]) => void;
  isImageMode: boolean;
}

export const FilePreviewList: React.FC<FilePreviewListProps> = ({
  files,
  onFilesChange,
}) => {
  const theme = useTheme();
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [previewFile, setPreviewFile] = useState<PickedFile | null>(null);
  const [isCreatingPdf, setIsCreatingPdf] = useState(false);

  const imageFiles = files.filter(file => file.type.startsWith('image/'));
  const hasMultipleImages = imageFiles.length > 1;

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) {
      return '0 Bytes';
    }
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const openPreview = async (file: PickedFile) => {
    let fileUri = file.uri;
    // If content://, copy to app storage
    if (Platform.OS === 'android' && fileUri.startsWith('content://')) {
      try {
        const destPath = `${RNFS.CachesDirectoryPath}/${
          file.name || `temp_${Date.now()}`
        }`;
        await RNFS.copyFile(fileUri, destPath);
        fileUri = `file://${destPath}`;
      } catch (err) {
        Alert.alert(
          'Lỗi',
          'Không thể truy cập tệp này. Vui lòng thử lại hoặc chọn tệp khác.',
        );
        return;
      }
    }
    if (file.type.startsWith('image/')) {
      setPreviewFile({...file, uri: fileUri});
      setShowPreviewModal(true);
    } else if (
      file.type === 'application/pdf' ||
      file.name?.toLowerCase().endsWith('.pdf')
    ) {
      setPreviewFile({...file, uri: fileUri});
      setShowPreviewModal(true);
    } else {
      // Use FileViewer for other files
      try {
        if (!fileUri.startsWith('file://')) {
          fileUri = 'file://' + fileUri;
        }
        await FileViewer.open(fileUri, {showOpenWithDialog: true});
      } catch (error) {
        console.error('Error opening file:', error);
        Alert.alert(
          'Không thể xem tệp',
          'Ứng dụng không thể mở tệp này. Vui lòng kiểm tra quyền truy cập hoặc thử lại.',
        );
      }
    }
  };

  const closePreview = () => {
    setShowPreviewModal(false);
    setPreviewFile(null);
  };

  const removeFile = (index: number) => {
    onFilesChange(files.filter((_, i) => i !== index));
  };

  const generatePdfFromImages = async (
    imageFilesGen: PickedFile[],
  ): Promise<PickedFile> => {
    setIsCreatingPdf(true);
    try {
      const pdf = new jsPDF();
      let isFirstPage = true;

      for (const imageFile of imageFilesGen) {
        const base64Data = await RNFS.readFile(imageFile.uri, 'base64');
        const imageDataUri = `data:${imageFile.type};base64,${base64Data}`;

        if (!isFirstPage) {
          pdf.addPage();
        }

        const pdfWidth = pdf.internal.pageSize.getWidth();
        const pdfHeight = pdf.internal.pageSize.getHeight();

        pdf.addImage(
          imageDataUri,
          'JPEG',
          10,
          10,
          pdfWidth - 20,
          pdfHeight - 20,
        );
        isFirstPage = false;
      }

      const pdfOutput = pdf.output('datauristring');
      const base64Data = pdfOutput.split(',')[1];
      const pdfPath = `${
        RNFS.DocumentDirectoryPath
      }/merged_images_${Date.now()}.pdf`;

      await RNFS.writeFile(pdfPath, base64Data, 'base64');

      return {
        uri: `file://${pdfPath}`,
        name: `merged_images_${Date.now()}.pdf`,
        type: 'application/pdf',
        size: base64Data.length * 0.75,
      };
    } catch (error) {
      console.error('Error generating PDF:', error);
      throw error;
    } finally {
      setIsCreatingPdf(false);
    }
  };

  // const handleMergeImagesToPdf = async () => {
  //   if (imageFiles.length === 0) {
  //     Alert.alert('Thông báo', 'Không có ảnh nào để tạo PDF.');
  //     return;
  //   }

  //   try {
  //     const pdfFile = await generatePdfFromImages(imageFiles);
  //     onFilesChange([pdfFile]);
  //     Alert.alert('Thành công', 'Đã tạo PDF từ các ảnh thành công!');
  //   } catch (error) {
  //     Alert.alert('Lỗi', 'Không thể tạo PDF từ các ảnh. Vui lòng thử lại.');
  //   }
  // };
  const renderItem = ({item, index}: {item: PickedFile; index: number}) => (
    <FilePreviewItem
      item={item}
      index={index}
      onPreview={openPreview}
      onRemove={removeFile}
      formatFileSize={formatFileSize}
    />
  );

  const styles = StyleSheet.create({
    container: {
      marginTop: 8,
    },
    fileList: {
      flexGrow: 0,
    },
    filePreview: {
      padding: 16,
      borderRadius: 8,
    },
    filePreviewTitle: {
      fontSize: 16,
      fontWeight: '600',

      marginBottom: 12,
    },
    buttonRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 12,
    },
    sortButton: {
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderRadius: 8,
      flex: 1,
      marginRight: 8,
    },
    sortButtonText: {
      fontSize: 14,
      fontWeight: '600',
      textAlign: 'center',
    },
    mergeToPdfButton: {
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderRadius: 8,
      flex: 1,
      marginLeft: 8,
    },
    mergeToPdfButtonText: {
      fontSize: 14,
      fontWeight: '600',
      textAlign: 'center',
    },
    fileItem: {
      marginBottom: 10,
      borderRadius: 10,
      elevation: 1,
    },
    thumbnail: {
      width: 64,
      height: 64,
      borderRadius: 8,
      marginRight: 12,
    },
    fileTypeIcon: {
      fontSize: 24,
      textAlign: 'center',
    },
    fileInfo: {
      flex: 1,
    },
    fileName: {
      fontSize: 14,
      fontWeight: '500',

      marginBottom: 4,
    },
    fileSize: {
      fontSize: 12,
    },
    removeButton: {
      padding: 8,
    },
    removeText: {
      fontSize: 14,
    },
    actionsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginVertical: 8,
    },
    button: {
      flex: 1,
      marginHorizontal: 4,
    },
    loadingContainer: {
      padding: 20,
      alignItems: 'center',
    },
    emptyContainer: {
      padding: 20,
      alignItems: 'center',
    },
    emptyText: {
      fontSize: 16,
    },
    fileListContent: {
      paddingVertical: 8,
    },
  });

  if (isCreatingPdf) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={{color: theme.colors.onSurface}}>Đang tạo PDF...</Text>
      </View>
    );
  }

  if (files.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text
          style={[styles.emptyText, {color: theme.colors.onSurfaceVariant}]}>
          Chưa có tệp nào được chọn
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {hasMultipleImages && (
        <View style={styles.actionsContainer}>
          <Button
            mode="outlined"
            style={styles.button}
            onPress={() => generatePdfFromImages(imageFiles)}>
            Tạo PDF
          </Button>
        </View>
      )}

      <FlatList
        data={files}
        renderItem={renderItem}
        keyExtractor={(item, index) => `${item.uri}-${index}`}
        scrollEnabled={false}
        style={styles.fileList}
        contentContainerStyle={styles.fileListContent}
      />

      <FilePreviewModal
        visible={showPreviewModal && !!previewFile}
        file={previewFile}
        onClose={closePreview}
      />
    </View>
  );
};
