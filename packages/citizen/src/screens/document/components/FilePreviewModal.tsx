import React from 'react';
import {View, Image, Alert} from 'react-native';
import {Modal, Portal, IconButton, Text} from 'react-native-paper';
import Pdf from 'react-native-pdf';
import type {PickedFile} from '../types';

interface FilePreviewModalProps {
  visible: boolean;
  file: PickedFile | null;
  onClose: () => void;
}

export const FilePreviewModal: React.FC<FilePreviewModalProps> = ({
  visible,
  file,
  onClose,
}) => {
  if (!file) return null;
  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onClose}
        contentContainerStyle={{
          backgroundColor: 'white',
          borderRadius: 12,
          alignItems: 'center',
          minHeight: 420,
          minWidth: 340,
          alignSelf: 'center',
          padding: 0,
        }}>
        <IconButton
          icon="close"
          size={28}
          style={{position: 'absolute', top: 8, right: 8, zIndex: 10}}
          onPress={onClose}
          accessibilityLabel="Đóng xem trước"
        />
        {file.type.startsWith('image/') ? (
          <Image
            source={{uri: file.uri}}
            style={{
              width: 320,
              height: 400,
              resizeMode: 'contain',
              borderRadius: 8,
            }}
          />
        ) : file.type === 'application/pdf' ||
          file.name?.toLowerCase().endsWith('.pdf') ? (
          <View
            style={{
              width: 320,
              height: 400,
              backgroundColor: '#fff',
              borderRadius: 8,
              overflow: 'hidden',
            }}>
            <Pdf
              source={{
                uri: file.uri.startsWith('file://')
                  ? file.uri
                  : `file://${file.uri}`,
              }}
              style={{flex: 1, width: 320, height: 400}}
              onError={err => {
                console.error('PDF error:', err);
                // Alert.alert('Lỗi', 'Không thể xem file PDF này.');
                onClose();
              }}
            />
          </View>
        ) : (
          <Text>Không hỗ trợ xem trước tệp này.</Text>
        )}
      </Modal>
    </Portal>
  );
};
