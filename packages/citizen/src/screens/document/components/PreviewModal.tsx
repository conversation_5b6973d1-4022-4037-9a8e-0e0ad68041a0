import React from 'react';
import {
  View,
  Modal,
  Image,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import Pdf from 'react-native-pdf';
import Video from 'react-native-video';
import type {PickedFile} from '../types';

interface PreviewModalProps {
  visible: boolean;
  onClose: () => void;
  file: PickedFile | null;
}

export const PreviewModal: React.FC<PreviewModalProps> = ({
  visible,
  onClose,
  file,
}) => {
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}>
      <View style={styles.previewModalContainer}>
        <View style={styles.previewModalContent}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>

          {file && (
            <>
              {file.type.startsWith('image/') && (
                <Image
                  source={{uri: file.uri}}
                  style={styles.previewImage}
                  resizeMode="contain"
                />
              )}

              {file.type === 'application/pdf' && (
                <Pdf
                  source={{uri: file.uri}}
                  style={styles.previewPdf}
                  onLoadComplete={(numberOfPages, _filePath) => {
                    console.log(`PDF loaded with ${numberOfPages} pages`);
                  }}
                  onPageChanged={(page, numberOfPages) => {
                    console.log(`Page ${page} of ${numberOfPages}`);
                  }}
                  onError={error => {
                    console.log('PDF error:', error);
                  }}
                />
              )}

              {file.type.startsWith('video/') && (
                <Video
                  source={{uri: file.uri}}
                  style={styles.previewVideo}
                  controls={true}
                  resizeMode="contain"
                  onError={error => {
                    console.log('Video error:', error);
                  }}
                />
              )}

              <Text style={styles.previewFileName}>{file.name}</Text>
            </>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  previewModalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  previewModalContent: {
    width: '90%',
    maxWidth: 600,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
  },
  closeButton: {
    position: 'absolute',
    right: 16,
    top: 16,
    zIndex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    width: 30,
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  previewImage: {
    width: '100%',
    height: 400,
    borderRadius: 8,
    marginBottom: 12,
  },
  previewPdf: {
    width: '100%',
    height: 400,
    borderRadius: 8,
    marginBottom: 12,
  },
  previewVideo: {
    width: '100%',
    height: 400,
    borderRadius: 8,
    marginBottom: 12,
  },
  previewFileName: {
    fontSize: 14,
    color: '#000000',
    textAlign: 'center',
    marginTop: 8,
  },
});
