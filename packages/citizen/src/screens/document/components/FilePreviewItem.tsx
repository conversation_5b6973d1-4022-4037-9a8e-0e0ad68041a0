import React from 'react';
import {View, Image} from 'react-native';
import {Card, List, IconButton, Text, useTheme} from 'react-native-paper';
import type {PickedFile} from '../types';

interface FilePreviewItemProps {
  item: PickedFile;
  index: number;
  onPreview: (file: PickedFile) => void;
  onRemove: (index: number) => void;
  formatFileSize: (bytes: number) => string;
}

export const FilePreviewItem: React.FC<FilePreviewItemProps> = ({
  item,
  index,
  onPreview,
  onRemove,
  formatFileSize,
}) => {
  const theme = useTheme();
  return (
    <Card
      style={{marginBottom: 10, borderRadius: 10, elevation: 1}}
      mode="outlined"
      onPress={() => onPreview(item)}
      accessible
      accessibilityLabel={`Xem trước tệp ${item.name || `File ${index + 1}`}`}>
      <View style={{flexDirection: 'row', alignItems: 'center'}}>
        {item.type.startsWith('image/') ? (
          <Image
            source={{uri: item.uri}}
            style={{
              width: 64,
              height: 64,
              borderRadius: 8,
              marginRight: 12,
              backgroundColor: theme.colors.surfaceVariant,
            }}
            resizeMode="cover"
          />
        ) : (
          <View
            style={{
              width: 64,
              height: 64,
              borderRadius: 8,
              marginRight: 12,
              backgroundColor: theme.colors.surfaceVariant,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <List.Icon
              icon="file-document-outline"
              color={theme.colors.primary}
            />
          </View>
        )}
        <View style={{flex: 1, minWidth: 0}}>
          <Text
            variant="bodyLarge"
            style={{marginBottom: 2, color: theme.colors.onSurface}}
            numberOfLines={1}>
            {item.name || `File ${index + 1}`}
          </Text>
          <Text
            variant="bodySmall"
            style={{opacity: 0.7, color: theme.colors.onSurfaceVariant}}>
            {formatFileSize(item.size)}
          </Text>
        </View>
        <IconButton
          icon="delete"
          size={22}
          style={{marginLeft: 4, alignSelf: 'flex-start'}}
          onPress={() => onRemove(index)}
          accessibilityLabel={`Xóa tệp ${item.name || `File ${index + 1}`}`}
          iconColor={theme.colors.error}
        />
      </View>
    </Card>
  );
};
