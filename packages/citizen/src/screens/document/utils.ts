import RNFS from 'react-native-fs';
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) {
    return '0 Bytes';
  }
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const getPdfAsBase64 = async (
  filePath: string,
): Promise<string | null> => {
  try {
    // filePath should be absolute, e.g. `${RNFS.CachesDirectoryPath}/form_1753408555769.pdf`
    const base64 = await RNFS.readFile(filePath, 'base64');
    return base64;
  } catch (error) {
    console.error('Error reading PDF as base64:', error);
    return null;
  }
};
