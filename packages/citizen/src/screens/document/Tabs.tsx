import React from 'react';
import {View, TouchableOpacity, Text} from 'react-native';

interface TabsProps {
  activeTab: string;
  setActiveTab: (tab: 'content' | 'info') => void;
  styles: any;
  theme: any;
}

const Tabs: React.FC<TabsProps> = ({
  activeTab,
  setActiveTab,
  styles,
  theme,
}) => (
  <View style={[styles.tabContainer, {backgroundColor: theme.colors.surface}]}>
    <TouchableOpacity
      style={[
        styles.tabButton,
        activeTab === 'content'
          ? {borderBottomColor: theme.colors.primary}
          : {borderBottomColor: theme.colors.surface},
      ]}
      onPress={() => setActiveTab('content')}>
      <Text
        style={[
          styles.tabText,
          {
            color:
              activeTab === 'content'
                ? theme.colors.onBackground
                : theme.colors.onSurface,
          },
        ]}>
        Nội dung
      </Text>
    </TouchableOpacity>
    <TouchableOpacity
      style={[
        styles.tabButton,
        activeTab === 'info'
          ? {borderBottomColor: theme.colors.primary}
          : {borderBottomColor: theme.colors.surface},
      ]}
      onPress={() => setActiveTab('info')}>
      <Text
        style={[
          styles.tabText,
          {
            color:
              activeTab === 'info'
                ? theme.colors.onBackground
                : theme.colors.onSurface,
          },
        ]}>
        Thông tin
      </Text>
    </TouchableOpacity>
  </View>
);

export default Tabs;
