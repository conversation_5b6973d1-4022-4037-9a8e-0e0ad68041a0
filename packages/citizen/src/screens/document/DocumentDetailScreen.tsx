import React, {useState, useEffect} from 'react';
import {Text, View, ScrollView, Alert, StyleSheet} from 'react-native';
import ImageViewing from 'react-native-image-viewing';
import {RouteProp, useRoute, useNavigation} from '@react-navigation/native';
import {HomeStackParamList} from '../../navigation/HomeNavigator';
import {CCAppBar} from '../../components/QNH_CongDanComponents/HeaderComponent/CCAppBar';
import {document} from '../../requester-biz-service/apis/documents-api';
import RNFS from 'react-native-fs';
import DocumentInfo from './DocumentInfo';
import {DvcInfo, CertInfo} from './MetadataInfo';
import {useUserStore} from '../../stores/user.store';
import {
  extractBase64FromXML,
  getFileExtensionFromMimeType,
  saveBase64ToFile,
} from './utils/documentFileUtils';
import {useTheme, But<PERSON>} from 'react-native-paper';
import {useQueryClient} from 'react-query';
import FilePreview from './FilePreview';
import Tabs from './Tabs';
import ErrorState from './ErrorState';
import LoadingState from './LoadingState';
import documentDetailStyles from './documentDetailStyles';
import {useDeviceStore} from '../../stores/device.store';


interface DocumentDetailData {
  fileUrl: string;
  metadata: {
    certificates: any;
    dvc: any;
    score: number;
    address: string;
  };
  docFileType: string;
  docName: string;
  docOwnerType: string;
  docType: string;
  uri: string;
  hash: string;
  docOwnerId: string;
  docId: string;
  additionalData: any[];
  status: number;
  createdAt: string;
}

interface DocumentDetailResponse {
  data: DocumentDetailResponseData;
}
interface DocumentDetailResponseData {
  kind: string;
  document: DocumentDetailData;
  xmlFile: string;
  xmlExpiresIn: number;
  fileSize: number;
}

// ...existing code...

const DocumentDetailScreen = () => {
  const route =
    useRoute<RouteProp<HomeStackParamList, 'DocumentDetailScreen'>>();
  const {updateDisableBack} = useDeviceStore();
  const navigation = useNavigation();
  const {uri} = route.params;
  const {userProfile} = useUserStore();
  const [activeTab, setActiveTab] = useState<'content' | 'info'>('content');
  const [documentData, setDocumentData] =
    useState<DocumentDetailResponseData | null>(null);
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryAttempts, setRetryAttempts] = useState(0);
  const [lastErrorStatus, setLastErrorStatus] = useState<number | null>(null);
  const [xmlFilePath, setXmlFilePath] = useState<string | null>(null);
  const [processedFilePath, setProcessedFilePath] = useState<string | null>(
    null,
  );
  const [cacheLoading, setCacheLoading] = useState(false);
  const [imageViewerVisible, setImageViewerVisible] = useState(false);
  const [isImageViewerVisible, setIsImageViewerVisible] = useState(false);

  // Fetch document information
  useEffect(() => {
    fetchDocumentInfo();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [uri]);

  const fetchDocumentInfo = async () => {
    try {
      updateDisableBack(true);
      setLoading(true);
      setError(null);

      // Get document info from API
      const response = await document.getDocInfo(uri);
      const data = response.data as DocumentDetailResponse;
      console.log('Document Info:xxxx', JSON.stringify(data, null, 2));
      setDocumentData(data.data);

      // Reset retry attempts on successful request
      setRetryAttempts(0);
      setLastErrorStatus(null);

      // Define cache paths
      const baseCachePath = `${RNFS.CachesDirectoryPath}/${data.data.document.uri}`;
      const xmlCachePath = `${baseCachePath}.xml`;

      // Check if XML file exists in cache
      let xmlExists = false;
      try {
        xmlExists = await RNFS.exists(xmlCachePath);
      } catch (err) {
        xmlExists = false;
      }

      let xmlContent = '';

      // Download XML file if not cached
      if (!xmlExists && data.data.xmlFile) {
        setCacheLoading(true);
        try {
          await RNFS.downloadFile({
            fromUrl: data.data.xmlFile,
            toFile: xmlCachePath,
          }).promise;

          // Read the downloaded XML content
          xmlContent = await RNFS.readFile(xmlCachePath, 'utf8');
          setXmlFilePath(xmlCachePath);
        } catch (downloadError) {
          console.error('Error downloading XML file:', downloadError);
          setError('Failed to download document content');
          return;
        }
      } else if (xmlExists) {
        // Read existing XML file
        try {
          xmlContent = await RNFS.readFile(xmlCachePath, 'utf8');
          setXmlFilePath(xmlCachePath);
        } catch (readError) {
          console.error('Error reading cached XML:', readError);
        }
      }

      // Process the XML content to extract and save base64 files
      if (xmlContent && data.data.document.docFileType) {
        await processXMLContent(
          xmlContent,
          data.data.document.docFileType,
          baseCachePath,
        );
      }

      setCacheLoading(false);
    } catch (err: any) {
      console.error('Error fetching document info:', err);

      // Extract status code from error if available
      const statusCode = err?.response?.status || err?.status || null;
      setLastErrorStatus(statusCode);

      // Set appropriate error message based on status code
      let errorMessage = 'Failed to load document information';
      if (statusCode === 400) {
        errorMessage =
          'Invalid request. The document may not exist or you may not have permission to access it.';
      } else if (statusCode === 404) {
        errorMessage = 'Document not found.';
      } else if (statusCode === 403) {
        errorMessage = 'You do not have permission to access this document.';
      } else if (statusCode >= 500) {
        errorMessage = 'Server error. Please try again later.';
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
      updateDisableBack(false);
    }
  };

  const processXMLContent = async (
    xmlContent: string,
    docFileType: string,
    baseCachePath: string,
  ) => {
    try {
      // Determine file extension from docFileType
      const extension = getFileExtensionFromMimeType(docFileType);
      const targetFilePath = `${baseCachePath}.${extension}`;

      // Check if processed file already exists
      const fileExists = await RNFS.exists(targetFilePath);

      if (!fileExists) {
        // Extract base64 content from XML
        const base64Content = extractBase64FromXML(xmlContent, docFileType);

        if (base64Content) {
          // Save base64 content to file
          await saveBase64ToFile(base64Content, targetFilePath);
          setProcessedFilePath(targetFilePath);
        } else {
          console.warn(
            'No base64 content found in XML for file type:',
            docFileType,
          );
        }
      } else {
        // File already exists, just set the path
        setProcessedFilePath(targetFilePath);
      }
    } catch (err) {
      console.error('Error processing XML content:', err);
    }
  };

  const renderFileContent = () => {
    if (!documentData) return null;
    const fileType = documentData.document.docFileType.toLowerCase();
    return (
      <FilePreview
        fileType={fileType}
        processedFilePath={processedFilePath}
        xmlFilePath={xmlFilePath}
        loading={loading}
        cacheLoading={cacheLoading}
        setError={setError}
        isImageViewerVisible={isImageViewerVisible}
        setIsImageViewerVisible={setIsImageViewerVisible}
        styles={documentDetailStyles}
      />
    );
  };

  const renderDocumentInfo = () => {
    if (!documentData) {
      return null;
    }
    const meta = documentData.document.metadata;
    const dvc = meta?.dvc?.DongBoHoSo;
    const certs = meta?.certificates?.certs;
    return (
      <ScrollView
        style={[
          styles.infoContainer,
          {backgroundColor: theme.colors.background},
        ]}
        showsVerticalScrollIndicator={false}>
        <DocumentInfo
          document={documentData.document}
          fileSize={documentData.fileSize}
          userProfile={userProfile}
        />
        {/* Metadata Section */}
        {meta && (
          <View style={styles.infoSection}>
            <Text
              style={[styles.sectionTitle, {color: theme.colors.onSurface}]}>
              Thông tin xử lý hồ sơ
            </Text>
            <DvcInfo dvc={dvc} />
            <CertInfo certs={certs} />
          </View>
        )}
      </ScrollView>
    );
  };

  const handleRetry = async () => {
    const newRetryAttempts = retryAttempts + 1;
    setRetryAttempts(newRetryAttempts);

    // If this is the second retry attempt for a 400 error, show different options
    if (lastErrorStatus === 400 && newRetryAttempts >= 2) {
      setError(
        'This document cannot be accessed. Please check if the document exists and you have the proper permissions.',
      );
      return;
    }

    // Try to fetch again
    await fetchDocumentInfo();
  };

  const handleGoBack = () => {
    navigation.goBack();
  };
  const queryClient = useQueryClient();

  const handleDeleteDocument = () => {
    Alert.alert(
      'Xóa tài liệu',
      'Bạn có chắc chắn muốn xóa tài liệu này không?',
      [
        {
          text: 'Hủy',
          style: 'cancel',
        },
        {
          text: 'Xóa',
          style: 'destructive',
          onPress: async () => {
            try {
              if (!documentData?.document?.uri) {
                return;
              }

              setLoading(true);
              await document.removeFromWallet(documentData.document.uri);

              // Invalidate and refetch documents query
              queryClient.invalidateQueries(['documents']);

              navigation.goBack();
            } catch (err) {
              console.error('Error deleting document:', err);
              setError('Failed to delete document');
            } finally {
              setLoading(false);
            }
          },
        },
      ],
    );
  };

  // Prepare content based on loading/error state
  let mainContent;
  if (loading) {
    mainContent = (
      <LoadingState
        loading={loading}
        cacheLoading={cacheLoading}
        styles={documentDetailStyles}
        theme={theme}
      />
    );
  } else if (error) {
    mainContent = (
      <ErrorState
        error={error}
        lastErrorStatus={lastErrorStatus}
        retryAttempts={retryAttempts}
        handleRetry={handleRetry}
        handleGoBack={handleGoBack}
        styles={documentDetailStyles}
        Button={Button}
      />
    );
  } else {
    mainContent = (
      <>
        <Tabs
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          styles={documentDetailStyles}
          theme={theme}
        />
        <View style={documentDetailStyles.tabContent}>
          {activeTab === 'content' ? renderFileContent() : renderDocumentInfo()}
        </View>
      </>
    );
  }

  // Single return with one CCAppBar - using View instead of SafeAreaView to avoid double headers
  return (
    <View style={styles.container}>
      <CCAppBar
        label={documentData?.document?.docName || 'Chi tiết tài liệu'}
        isBack={true}
        menuConfig={[
          {
            icon: 'delete',
            text: 'Xóa tài liệu',
            onPress: handleDeleteDocument,
          },
        ]}
      />

      {mainContent}

      {/* Image Viewer */}
      {imageViewerVisible && (
        <ImageViewing
          images={[{uri: `file://${processedFilePath}`}]}
          imageIndex={0}
          visible={imageViewerVisible}
          onRequestClose={() => setImageViewerVisible(false)}
          presentationStyle="overFullScreen"
          backgroundColor={theme.colors.surface}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabContainer: {
    flexDirection: 'row',
    // borderBottomWidth: 1,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
    borderBottomWidth: 2,
  },
  activeTab: {
    borderBottomWidth: 2,
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
  },
  tabContent: {
    flex: 1,
  },
  contentContainer: {
    flex: 1,
  },
  demoCard: {
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
  },
  demoTitle: {
    fontSize: 24,
    fontWeight: '600',
    marginBottom: 12,
  },
  demoDescription: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 20,
  },
  videoDemoContainer: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderStyle: 'dashed',
  },
  videoDemoText: {
    fontSize: 16,
    fontWeight: '500',
  },
  imageDemoContainer: {
    width: '100%',
    height: 150,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderStyle: 'dashed',
  },
  imageDemoText: {
    fontSize: 16,
    fontWeight: '500',
  },
  viewButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 6,
    marginTop: 10,
  },
  viewButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  webView: {
    flex: 1,
  },
  pdfViewer: {
    flex: 1,
  },
  imageContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  documentImage: {
    width: '100%',
    height: '100%',
  },
  infoContainer: {
    flex: 1,
    padding: 16,
  },
  infoSection: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  infoRow: {
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '400',
  },
  infoValueSmall: {
    fontSize: 12,
    fontWeight: '400',
    fontFamily: 'monospace',
  },
  additionalDataItem: {
    padding: 12,
    borderRadius: 6,
    marginBottom: 8,
  },
  additionalDataText: {
    fontSize: 12,
    fontFamily: 'monospace',
  },
});
export default DocumentDetailScreen;
