import React from 'react';
import {
  Text,
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import {RouteProp, useRoute, useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {HomeStackParamList} from '../../navigation/HomeNavigator';
import {getStatusText, formatDateTime, handleFormatDate} from '../../utils/fn';
import {CCAppBar} from '../../components/QNH_CongDanComponents/HeaderComponent/CCAppBar';
import {useTheme} from 'react-native-paper';

type ShareDetailScreenRouteProp = RouteProp<
  HomeStackParamList,
  'ShareDetailScreen'
>;

type NavigationProp = NativeStackNavigationProp<HomeStackParamList>;

const ShareDocumentDetailScreen = () => {
  const theme = useTheme();
  const route = useRoute<ShareDetailScreenRouteProp>();
  const navigation = useNavigation<NavigationProp>();
  const {detail} = route.params;

  // Extract data from detail object
  const documentTitle = detail?.data?.[0]?.name || 'Tài liệu chia sẻ';
  const createdAt = detail?.createdAt || '';
  const progress = detail?.progress || 'new';

  const statusInfo = getStatusText(progress);

  const handleNavigateToMetadata = () => {
    navigation.navigate('ShareDetailMetadata', {
      id: detail?.id || '',
      detail: detail,
    });
  };

  const handleNavigateToDocsData = () => {
    navigation.navigate('ShareDetailDocsData', {
      id: detail?.id || '',
      detail: detail,
    });
  };

  const handleNavigateToProcessHistory = () => {
    navigation.navigate('ShareDetailProcessHistory', {
      id: detail?.id || '',
      detail: detail,
    });
  };

  const getSectionCardStyle = (): object => ({
    backgroundColor: theme.colors.surface,
    borderWidth: 0.5,
    borderColor: theme.colors.outline,
    shadowColor: theme.dark ? '#000' : theme.colors.outline,
    shadowOffset: {width: 0, height: theme.dark ? 1 : 2},
    shadowOpacity: theme.dark ? 0.3 : 0.12,
    shadowRadius: theme.dark ? 2 : 4,
    elevation: theme.dark ? 1 : 3,
  });

  return (
    <View
      style={[styles.container, {backgroundColor: theme.colors.background}]}>
      {/* Header */}
      <CCAppBar label="Chi tiết" isBack={true} />
      <ScrollView
        style={[styles.scrollView, {backgroundColor: theme.colors.background}]}
        showsVerticalScrollIndicator={false}>
        {/* Document Title Section */}
        <View
          style={[
            styles.titleSection,
            {backgroundColor: theme.colors.background},
          ]}>
          <Text
            style={[styles.documentTitle, {color: theme.colors.onBackground}]}>
            {documentTitle}
          </Text>
          <View
            style={[
              styles.statusBadge,
              {backgroundColor: statusInfo.background},
            ]}>
            <Text style={[styles.statusText, {color: statusInfo.color}]}>
              {statusInfo.label}
            </Text>
          </View>
        </View>
        {/* Submission Info */}
        <View
          style={[styles.infoSection, {backgroundColor: theme.colors.surface}]}>
          <Text
            style={[styles.infoText, {color: theme.colors.onSurfaceVariant}]}>
            Ngày nộp hồ sơ:{' '}
            {handleFormatDate({date: createdAt, format: 'hh:mm - DD/MM/YYYY'})}
          </Text>
          <Text
            style={[styles.infoText, {color: theme.colors.onSurfaceVariant}]}>
            {' '}
            (Cập nhật lần cuối:{' '}
            {handleFormatDate({
              date: detail?.updatedAt || createdAt,
              format: 'hh:mm - DD/MM/YYYY',
            })}
            ){' '}
          </Text>
        </View>
        {/* Document Details Section */}
        <TouchableOpacity
          style={[styles.section, getSectionCardStyle()]}
          onPress={handleNavigateToMetadata}>
          <View style={styles.sectionHeader}>
            <Text
              style={[styles.sectionTitle, {color: theme.colors.onSurface}]}>
              Chi tiết hồ sơ
            </Text>
          </View>
          <Text
            style={[
              styles.sectionDescription,
              {color: theme.colors.onSurfaceVariant},
            ]}>
            Bao gồm các thông tin chi tiết như thông tin thủ tục, thông tin
            người yêu cầu, thông tin trả kết quả, v.v.
          </Text>
        </TouchableOpacity>
        {/* Document Components Section */}
        <TouchableOpacity
          style={[styles.section, getSectionCardStyle()]}
          onPress={handleNavigateToDocsData}>
          <View style={styles.sectionHeader}>
            <Text
              style={[styles.sectionTitle, {color: theme.colors.onSurface}]}>
              Thành phần giấy tờ
            </Text>
          </View>
          <Text
            style={[
              styles.sectionDescription,
              {color: theme.colors.onSurfaceVariant},
            ]}>
            Những loại giấy tờ và tài liệu cần thiết đã được nộp kèm theo hồ sơ
          </Text>
        </TouchableOpacity>
        {/* Processing Status Section */}
        <TouchableOpacity
          style={[styles.section, getSectionCardStyle()]}
          onPress={handleNavigateToProcessHistory}>
          <View style={styles.sectionHeader}>
            <Text
              style={[styles.sectionTitle, {color: theme.colors.onSurface}]}>
              Quá trình xử lý
            </Text>
          </View>
          <Text
            style={[
              styles.sectionDescription,
              {color: theme.colors.onSurfaceVariant},
            ]}>
            Mô tả các giai đoạn mà một hồ sơ từ khi được tiếp nhận đến khi hoàn
            tất
          </Text>
        </TouchableOpacity>
      </ScrollView>

      {/* Action Button */}
      {/* <View
        style={[
          styles.buttonContainer,
          {backgroundColor: theme.colors.surface},
        ]}>
        <Button>Bổ sung hồ sơ</Button>
      </View> */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  titleSection: {
    padding: 16,
    marginTop: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  documentTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    lineHeight: 22,
  },
  statusBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  infoSection: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  infoText: {
    fontSize: 14,
    marginBottom: 4,
  },
  section: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  sectionDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  buttonContainer: {
    padding: 16,
    borderTopWidth: 1,
  },
  actionButton: {
    paddingVertical: 4,
    borderRadius: 8,
    alignItems: 'center',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ShareDocumentDetailScreen;
