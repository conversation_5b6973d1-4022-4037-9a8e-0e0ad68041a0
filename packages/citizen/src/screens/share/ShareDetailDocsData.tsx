import React from 'react';
import {
  Text,
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import {
  NavigationProp,
  RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import {HomeStackParamList} from '../../navigation/HomeNavigator';
import {CCAppBar} from '../../components/QNH_CongDanComponents/HeaderComponent/CCAppBar';
import {useTheme} from 'react-native-paper';

type ShareDetailDocsDataRouteProp = RouteProp<
  HomeStackParamList,
  'ShareDetailDocsData'
>;

interface DocumentItemProps {
  filename: string;
  onPress: () => void;
}

interface JsonDataProps {
  data: any;
}

const uyQuyenCode = [
  '_taiLieuUyQuyenCaNhan',
  '_thongTinUyQuyenCaNhan',
  '_taiLieuUyQuyenDoanhNghiep',
  '_thongTinUyQuyenDoanhNghiep',
];

const DocumentItem: React.FC<DocumentItemProps> = ({filename, onPress}) => {
  const theme = useTheme();
  return (
    <TouchableOpacity
      style={[
        styles.documentItem,
        {backgroundColor: theme.colors.surfaceVariant},
      ]}
      onPress={onPress}>
      <View style={styles.documentContent}>
        <View style={styles.documentInfo}>
          <Text
            style={[styles.documentFilename, {color: theme.colors.onSurface}]}>
            {filename}
          </Text>
        </View>
      </View>
      <Text
        style={[styles.chevronIcon, {color: theme.colors.onSurfaceVariant}]}>
        →
      </Text>
    </TouchableOpacity>
  );
};

const JsonDataDisplay: React.FC<JsonDataProps> = ({data}) => {
  const theme = useTheme();
  const renderJsonData = (jsonData: any) => {
    if (!jsonData || typeof jsonData !== 'object') {
      return null;
    }
    return Object.entries(jsonData).map(([key, value], index) => (
      <View key={`${key}-${index}`} style={styles.jsonItem}>
        <Text style={[styles.jsonKey, {color: theme.colors.onSurfaceVariant}]}>
          {key}:
        </Text>
        <Text style={[styles.jsonValue, {color: theme.colors.onSurface}]}>
          {String(value)}
        </Text>
      </View>
    ));
  };
  let parsedData = data;
  if (typeof data === 'string') {
    try {
      parsedData = JSON.parse(data);
    } catch (e) {
      parsedData = {value: data};
    }
  }
  return (
    <View
      style={[
        styles.jsonContainer,
        {backgroundColor: theme.colors.surfaceVariant},
      ]}>
      {renderJsonData(parsedData)}
    </View>
  );
};

const ShareDetailDocsData = () => {
  const theme = useTheme();
  const route = useRoute<ShareDetailDocsDataRouteProp>();
  const {detail} = route.params;
  const navigation = useNavigation<NavigationProp<HomeStackParamList>>();

  // Extract all document data dynamically
  const documents = detail?.data || [];

  const handleDocumentPress = (document: any) => {
    // Handle document press - could open document viewer, download, etc.
    console.log('Document pressed:', document);
    // TODO: Implement document viewing/downloading logic
    navigation.navigate('DocumentDetailScreen', {
      uri: document.uri || '',
      item: document,
    });
  };

  const getDocumentsCardStyle = (): object => ({
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.outline || (theme.dark ? '#F4F6F8' : '#23272A'),
    shadowColor: theme.dark ? '#000' : theme.colors.outline,
    shadowOffset: {width: 0, height: theme.dark ? 1 : 2},
    shadowOpacity: theme.dark ? 0.3 : 0.12,
    shadowRadius: theme.dark ? 2 : 4,
    elevation: theme.dark ? 1 : 3,
  });

  return (
    <View
      style={[styles.container, {backgroundColor: theme.colors.background}]}>
      {/* Use theme color */}
      <CCAppBar label="Thành phần giấy tờ" isBack={true} />
      <ScrollView
        style={[styles.scrollView, {backgroundColor: theme.colors.background}]}
        showsVerticalScrollIndicator={false}>
        {documents[0].data.length > 0 ? (
          <View style={styles.section}>
            {documents[0].data.map((document: any, index: number) => {
              if (document.code.includes(uyQuyenCode)) {
                return;
              }
              return (
                <View
                  key={`${document.id || index}`}
                  style={[styles.documentsCard, getDocumentsCardStyle()]}>
                  <Text
                    style={[
                      styles.sectionTitle,
                      {color: theme.colors.onSurface},
                    ]}>
                    {document.name || `Document ${index + 1}`}
                  </Text>
                  {['uri', 'form'].includes(document.dataType) && (
                    <DocumentItem
                      filename={
                        document.detail.docName || `document-${index + 1}`
                      }
                      onPress={() => handleDocumentPress(document)}
                    />
                  )}
                  {document.dataType === 'json' && (
                    <JsonDataDisplay data={document.value} />
                  )}
                  {document.dataType !== 'uri' &&
                    document.dataType !== 'json' && (
                      <View
                        style={[
                          styles.textContainer,
                          {backgroundColor: theme.colors.surface},
                        ]}>
                        <Text
                          style={[
                            styles.textValue,
                            {color: theme.colors.onSurface},
                          ]}>
                          {String(document.value)}
                        </Text>
                      </View>
                    )}
                </View>
              );
            })}
          </View>
        ) : (
          <View style={styles.emptyState}>
            <Text
              style={[
                styles.emptyStateText,
                {color: theme.colors.onSurfaceVariant},
              ]}>
              Không có tài liệu nào
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    // backgroundColor: '#FFFFFF',
    // borderBottomWidth: 1,
    // borderBottomColor: '#E5E5E5',
  },
  backButton: {
    fontSize: 24,
    // color: '#000000',
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    // color: '#000000',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  documentsCard: {
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
  },
  section: {
    marginVertical: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    paddingHorizontal: 4,
  },
  documentItem: {
    borderRadius: 8,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  documentContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  fileIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    // backgroundColor: '#10B981',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  fileIconText: {
    fontSize: 16,
    // color: '#FFFFFF',
  },
  documentInfo: {
    flex: 1,
  },
  documentTitle: {
    fontSize: 14,
    fontWeight: '500',
    // color: '#000000',
    marginBottom: 2,
  },
  documentFilename: {
    fontSize: 14,
    // color: '#000000',
    fontWeight: '500',
  },
  chevronIcon: {
    fontSize: 18,
    // color: '#666666',
    marginLeft: 8,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    textAlign: 'center',
  },
  jsonContainer: {
    // backgroundColor: '#F8F9FA',
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
  },
  jsonItem: {
    flexDirection: 'row',
    marginBottom: 6,
    alignItems: 'flex-start',
  },
  jsonKey: {
    fontSize: 14,
    fontWeight: '500',
    // color: '#666666',
    minWidth: 100,
    marginRight: 8,
  },
  jsonValue: {
    fontSize: 14,
    // color: '#000000',
    flex: 1,
    lineHeight: 20,
  },
  textContainer: {
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
  },
  textValue: {
    fontSize: 14,
    lineHeight: 20,
  },
});

export default ShareDetailDocsData;
