import React, {useState} from 'react';
import {
  Text,
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import {RouteProp, useRoute} from '@react-navigation/native';
import {HomeStackParamList} from '../../navigation/HomeNavigator';
import {formatDateTime, getStatusText} from '../../utils/fn';
import {CCAppBar} from '../../components/QNH_CongDanComponents/HeaderComponent/CCAppBar';
import {useTheme} from 'react-native-paper';

type ShareDetailMetadataRouteProp = RouteProp<
  HomeStackParamList,
  'ShareDetailMetadata'
>;

interface DropdownSectionProps {
  title: string;
  isExpanded: boolean;
  onToggle: () => void;
  children: React.ReactNode;
}

const DropdownSection: React.FC<DropdownSectionProps> = ({
  title,
  isExpanded,
  onToggle,
  children,
}) => {
  const theme = useTheme();
  return (
    <View
      style={[
        styles.dropdownContainer,
        {backgroundColor: theme.colors.surface},
      ]}>
      {/* Card bg */}
      <TouchableOpacity
        style={[styles.dropdownHeader, {backgroundColor: theme.colors.surface}]}
        onPress={onToggle}>
        <Text style={[styles.dropdownTitle, {color: theme.colors.onSurface}]}>
          {title}
        </Text>
        <Text
          style={[styles.dropdownIcon, {color: theme.colors.onSurfaceVariant}]}>
          {isExpanded ? '−' : '+'}
        </Text>
      </TouchableOpacity>
      {isExpanded && (
        <View
          style={[
            styles.dropdownContent,
            {backgroundColor: theme.colors.surface},
          ]}>
          {children}
        </View>
      )}
    </View>
  );
};

interface InfoRowProps {
  label: string;
  value: string | number | null | undefined;
  showEmptyValues?: boolean;
}

const InfoRow: React.FC<InfoRowProps> = ({
  label,
  value,
  showEmptyValues = false,
}) => {
  const theme = useTheme();
  // Normalize the value - handle various forms of empty values
  const normalizedValue =
    value === null ||
    value === undefined ||
    value === '' ||
    value === 'null' ||
    value === 'undefined' ||
    (typeof value === 'string' && value.trim() === '')
      ? null
      : value;

  // Don't render if value is empty and showEmptyValues is false
  if (!showEmptyValues && normalizedValue === null) {
    return null;
  }

  // Don't render null values even if showEmptyValues is true
  if (normalizedValue === null) {
    return null;
  }

  return (
    <View style={styles.infoRow}>
      <Text style={[styles.infoLabel, {color: theme.colors.onSurfaceVariant}]}>
        {label}:
      </Text>
      <Text style={[styles.infoValue, {color: theme.colors.onSurface}]}>
        {String(normalizedValue)}
      </Text>
    </View>
  );
};

// Utility function to render multiple InfoRows from an object
const renderInfoFromObject = (
  data: any,
  fieldMapping: {[key: string]: string},
) => {
  if (!data) {
    return null;
  }

  return Object.entries(fieldMapping).map(([key, label]) => (
    <InfoRow key={key} label={label} value={data[key]} />
  ));
};

// Utility function to render InfoRows with date formatting
const renderDateInfoFromObject = (
  data: any,
  fieldMapping: {[key: string]: string},
) => {
  if (!data) {
    return null;
  }

  return Object.entries(fieldMapping).map(([key, label]) => (
    <InfoRow
      key={key}
      label={label}
      value={data[key] ? formatDateTime(data[key]) : data[key]}
    />
  ));
};

// Utility function to check if object has any non-empty values
const hasValidData = (data: any): boolean => {
  if (!data || typeof data !== 'object') {
    return false;
  }
  return Object.values(data).some(value => {
    if (value === null || value === undefined || value === '') {
      return false;
    }
    if (typeof value === 'string' && value.trim() === '') {
      return false;
    }
    return true;
  });
};

const ShareDetailMetadata = () => {
  const theme = useTheme();
  const route = useRoute<ShareDetailMetadataRouteProp>();
  const {detail} = route.params;

  const [expandedSections, setExpandedSections] = useState<{
    [key: string]: boolean;
  }>({
    documentInfo: true,
    submitterInfo: false,
    procedureInfo: false,
    collectorInfo: false,
    processInfo: false,
    documentData: false,
    uyQuyenInfo: false,
  });

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  // Extract data from detail object
  const documentData = detail?.data?.[0];
  const ownerInfo = detail?.owner;
  const collectorInfo = detail?.collector;
  const forwardMetadata = detail?.forwardMetadata;
  const identification = forwardMetadata?.identification;

  // Find authorization info from document data
  const uyQuyenData = documentData?.data?.find(
    (item: any) => item.code === '_thongTinUyQuyenCaNhan',
  );

  // Get status info for badge
  const statusInfo = getStatusText(String(detail?.progress || 'new'));

  return (
    <View
      style={[styles.container, {backgroundColor: theme.colors.background}]}>
      {/* Header */}
      <CCAppBar label="Chi tiết hồ sơ" isBack />
      <ScrollView
        style={[styles.scrollView, {backgroundColor: theme.colors.background}]}
        showsVerticalScrollIndicator={false}>
        <DropdownSection
          title="Thông tin hồ sơ"
          isExpanded={expandedSections.documentInfo}
          onToggle={() => toggleSection('documentInfo')}>
          <InfoRow label="Tên hồ sơ" value={documentData?.name} />
          <InfoRow label="Mã hồ sơ" value={detail?.shortId} />
          <InfoRow label="Số hồ sơ" value={documentData?.no} />
          <InfoRow label="Loại hồ sơ" value={documentData?.type} />
          <View style={styles.statusRow}>
            <Text
              style={[
                styles.statusLabel,
                {color: theme.colors.onSurfaceVariant},
              ]}>
              Trạng thái:
            </Text>
            <View
              style={[
                styles.statusBadge,
                {backgroundColor: statusInfo.background},
              ]}>
              {/* statusInfo.background is dynamic */}
              <Text style={[styles.statusText, {color: statusInfo.color}]}>
                {statusInfo.label}
              </Text>
            </View>
          </View>
          <InfoRow label="Từ khóa" value={detail?.keyword} />
          {renderDateInfoFromObject(detail, {
            createdAt: 'Ngày tạo',
            updatedAt: 'Ngày cập nhật',
            acceptedAt: 'Ngày chấp nhận',
            rejectedAt: 'Ngày từ chối',
          })}
        </DropdownSection>

        {/* Submitter Information */}
        <DropdownSection
          title="Thông tin người nộp"
          isExpanded={expandedSections.submitterInfo}
          onToggle={() => toggleSection('submitterInfo')}>
          <InfoRow label="Họ và tên" value={ownerInfo?.fullName} />
          <InfoRow label="Số CCCD/CMND" value={ownerInfo?.identificationId} />
          {hasValidData(identification) &&
            renderInfoFromObject(identification, {
              Sex: 'Giới tính',
              DateOfBirth: 'Ngày sinh',
              PlaceOfOrigin: 'Quê quán',
              PlaceOfResidence: 'Nơi thường trú',
              Nationality: 'Quốc tịch',
              Nation: 'Dân tộc',
              Religion: 'Tôn giáo',
              DateOfProvision: 'Ngày cấp CCCD',
              PlaceOfProvision: 'Nơi cấp CCCD',
            })}
        </DropdownSection>

        {/* Procedure Information */}
        <DropdownSection
          title="Thông tin thủ tục"
          isExpanded={expandedSections.procedureInfo}
          onToggle={() => toggleSection('procedureInfo')}>
          {renderInfoFromObject(documentData?.template, {
            code: 'Mã thủ tục',
            name: 'Tên thủ tục',
            version: 'Phiên bản',
            // content: 'Mô tả',
            keyword: 'Từ khóa mẫu',
          })}
          {renderDateInfoFromObject(documentData?.template, {
            createdAt: 'Ngày tạo mẫu',
            updatedAt: 'Ngày cập nhật mẫu',
          })}
        </DropdownSection>

        {/* Collector Information */}
        <DropdownSection
          title="Thông tin tiếp nhận"
          isExpanded={expandedSections.collectorInfo}
          onToggle={() => toggleSection('collectorInfo')}>
          <InfoRow label="Người tiếp nhận" value={collectorInfo?.requesterId} />
          <InfoRow
            label="Thiết bị tiếp nhận"
            value={collectorInfo?.requestDevice}
          />
          <InfoRow label="Đơn vị tiếp nhận" value={collectorInfo?.tenantId} />
          <InfoRow
            label="Địa chỉ nộp hồ sơ"
            value={forwardMetadata?.forwardData?.DiaChiNopHoSo}
          />
          <InfoRow
            label="Cấp thực hiện"
            value={forwardMetadata?.forwardData?.CapThucHien}
          />
        </DropdownSection>

        {/* Process Information */}
        {/* <DropdownSection
          title="Thông tin xử lý"
          isExpanded={expandedSections.processInfo}
          onToggle={() => toggleSection('processInfo')}>
          <InfoRow label="Tiến độ" value={detail?.progress} />
          <InfoRow label="Ghi chú" value={detail?.note} />
          <InfoRow label="Người xử lý" value={detail?.processor?.fullName} />
          <InfoRow label="Đơn vị xử lý" value={detail?.processingUnit} />
          {renderDateInfoFromObject(detail, {
            completedAt: 'Ngày hoàn thành',
          })}
        </DropdownSection> */}

        {/* Document Data */}
        {documentData?.data &&
          documentData.data.filter(
            (item: any) => item.code !== '_thongTinUyQuyenCaNhan',
          ).length > 0 && (
            <DropdownSection
              title="Dữ liệu hồ sơ"
              isExpanded={expandedSections.documentData}
              onToggle={() => toggleSection('documentData')}>
              {documentData.data
                .filter((item: any) => item.code !== '_thongTinUyQuyenCaNhan') // Exclude authorization data to avoid duplication
                .map((item: any, index: number) => {
                  const filteredData = documentData.data.filter(
                    (filterItem: any) =>
                      filterItem.code !== '_thongTinUyQuyenCaNhan',
                  );
                  return (
                    <View key={`${item.code}-${index}`} style={styles.dataItem}>
                      <InfoRow label="Mã trường" value={item.code} />
                      <InfoRow label="Tên trường" value={item.name} />
                      <InfoRow label="Loại" value={item.type} />
                      <InfoRow label="Giá trị" value={item.value} />
                      {index < filteredData.length - 1 && (
                        <View style={styles.separator} />
                      )}
                    </View>
                  );
                })}
            </DropdownSection>
          )}

        {/* Authorization Information */}
        {uyQuyenData && uyQuyenData.value && (
          <DropdownSection
            title="Thông tin ủy quyền cá nhân"
            isExpanded={expandedSections.uyQuyenInfo}
            onToggle={() => toggleSection('uyQuyenInfo')}>
            {renderInfoFromObject(uyQuyenData.value, {
              HoTen: 'Họ và tên người được ủy quyền',
              QuanHe: 'Quan hệ',
              SoGiayTo: 'Số giấy tờ',
              NgayThangNamSinh: 'Ngày tháng năm sinh',
            })}
          </DropdownSection>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: '#F5F5F5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    // backgroundColor: '#FFFFFF',
    // borderBottomWidth: 1,
    // borderBottomColor: '#E5E5E5',
  },
  backButton: {
    fontSize: 24,
    // color: '#000000',
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    // color: '#000000',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  dropdownContainer: {
    // backgroundColor: '#FFFFFF',
    borderRadius: 8,
    marginVertical: 6,
    overflow: 'hidden',
  },
  dropdownHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    // backgroundColor: '#F8F9FA',
  },
  dropdownTitle: {
    fontSize: 16,
    fontWeight: '600',
    // color: '#000000',
  },
  dropdownIcon: {
    fontSize: 20,
    // color: '#666666',
    fontWeight: 'bold',
  },
  dropdownContent: {
    padding: 16,
    paddingTop: 8,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 8,
    alignItems: 'flex-start',
  },
  infoLabel: {
    fontSize: 14,
    // color: '#666666',
    fontWeight: '500',
    minWidth: 120,
    marginRight: 8,
  },
  infoValue: {
    fontSize: 14,
    // color: '#000000',
    flex: 1,
    lineHeight: 20,
  },
  statusRow: {
    flexDirection: 'row',
    marginBottom: 8,
    alignItems: 'center',
  },
  statusLabel: {
    fontSize: 14,
    // color: '#666666',
    fontWeight: '500',
    minWidth: 120,
    marginRight: 8,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  dataItem: {
    marginBottom: 12,
  },
  separator: {
    height: 1,
    // backgroundColor: '#E5E5E5',
    marginVertical: 8,
  },
});

export default ShareDetailMetadata;
