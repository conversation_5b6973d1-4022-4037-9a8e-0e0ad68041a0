import React from 'react';
import {Text, View, StyleSheet, ScrollView} from 'react-native';
import {RouteProp, useRoute} from '@react-navigation/native';
import {HomeStackParamList} from '../../navigation/HomeNavigator';
import {formatDateTime} from '../../utils/fn';
import {CCAppBar} from '../../components/QNH_CongDanComponents/HeaderComponent/CCAppBar';
import EmptyList from '../../components/EmptyList';
import {useTheme} from 'react-native-paper';

type ShareDetailProcessHistoryRouteProp = RouteProp<
  HomeStackParamList,
  'ShareDetailProcessHistory'
>;

interface ProcessItemProps {
  process: any;
  isLast: boolean;
  isActive: boolean;
}

const ProcessItem: React.FC<ProcessItemProps> = ({
  process,
  isLast,
  isActive,
}) => {
  const theme = useTheme();
  const dotColor = isActive ? theme.colors.error : theme.colors.primary;
  return (
    <View style={styles.processItemContainer}>
      <View style={styles.leftTimelineContainer}>
        <View style={[styles.statusDot, {backgroundColor: dotColor}]} />
        {!isLast && (
          <View
            style={[
              styles.timelineLine,
              {backgroundColor: theme.colors.outline},
            ]}
          />
        )}
      </View>
      <View style={styles.processContent}>
        <Text style={[styles.statusTitle, {color: theme.colors.onSurface}]}>
          {process.tenTrangThai || process.noiDungXuLy || 'Xử lý hồ sơ'}
        </Text>
        <View style={styles.processDetails}>
          {process.nguoiXuLy && (
            <Text
              style={[
                styles.handlerText,
                {color: theme.colors.onSurfaceVariant},
              ]}>
              Người xử lý:{' '}
              <Text
                style={[styles.handlerName, {color: theme.colors.onSurface}]}>
                {process.nguoiXuLy}
              </Text>
            </Text>
          )}
          {process.thoiDiemXuLy && (
            <Text
              style={[styles.dateText, {color: theme.colors.onSurfaceVariant}]}>
              {formatDateTime(process.thoiDiemXuLy)}
            </Text>
          )}
          {process.donViXuLy && (
            <Text style={[styles.unitText, {color: theme.colors.primary}]}>
              Đơn vị: {process.donViXuLy}
            </Text>
          )}
        </View>
      </View>
    </View>
  );
};

const ShareDetailProcessHistory = () => {
  const theme = useTheme();
  const route = useRoute<ShareDetailProcessHistoryRouteProp>();
  const {detail} = route.params;

  // Extract process history data - will be from detail.data.process when available
  const processHistory = detail?.data?.process ? detail.data.process : [];

  // Reverse the array to show newest first (like in the image)
  const reversedHistory = [...processHistory].reverse();

  return (
    <View
      style={[styles.container, {backgroundColor: theme.colors.background}]}>
      {/* Header */}
      <CCAppBar label="Quá trình xử lý" isBack />
      <ScrollView
        style={[styles.scrollView, {backgroundColor: theme.colors.background}]}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={
          processHistory.length === 0 ? styles.emptyContainer : undefined
        }>
        {processHistory.length === 0 ? (
          <EmptyList
            title="Chưa có quá trình xử lý"
            subtitle="Hồ sơ chưa được xử lý hoặc thông tin chưa được cập nhật"
          />
        ) : (
          <View style={styles.timelineContainer}>
            {reversedHistory.map((process, index) => (
              <ProcessItem
                key={`${process.maHoSo}-${index}`}
                process={process}
                isLast={index === reversedHistory.length - 1}
                isActive={index === 0}
              />
            ))}
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: '#F5F5F5',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  timelineContainer: {
    paddingVertical: 16,
  },
  processItemContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  leftTimelineContainer: {
    alignItems: 'center',
    width: 20,
  },
  statusDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginTop: 4,
  },
  timelineLine: {
    width: 2,
    // backgroundColor: '#E5E7EB',
    flex: 1,
    marginTop: 8,
  },
  processContent: {
    flex: 1,
    marginLeft: 16,
    paddingBottom: 20,
  },
  statusTitle: {
    fontSize: 16,
    fontWeight: '600',
    // color: '#000000',
    marginBottom: 8,
  },
  processDetails: {
    gap: 4,
  },
  handlerText: {
    fontSize: 14,
    // color: '#666666',
    lineHeight: 20,
  },
  handlerName: {
    fontWeight: '500',
    // color: '#333333',
  },
  dateText: {
    fontSize: 14,
    // color: '#666666',
    lineHeight: 20,
  },
  unitText: {
    fontSize: 14,
    // color: '#3B82F6',
    fontWeight: '500',
    marginTop: 4,
  },
});

export default ShareDetailProcessHistory;
