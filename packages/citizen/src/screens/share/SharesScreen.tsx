import {FlatList, StyleSheet, Text, View} from 'react-native';
import React, {useEffect, useMemo} from 'react';
import EmptyList from '../../components/EmptyList';
import {useTheme} from 'react-native-paper';

import ErrorBoundary from '../../components/QNH_CongDanComponents/ErrorBoundary';
import {useShareProfiles} from '../../hooks/useShareProfiles';
import {CCAppBar} from '../../components/QNH_CongDanComponents/HeaderComponent/CCAppBar';
import {ShareItem} from '../../components/ShareItem';
import {ShareItem as ShareItemType} from '../../utils/shareItemUtils';

const SharesScreen = (prop: any) => {
  const theme = useTheme();
  const {navigation} = prop;
  const navigate = prop.route?.params?.navigate || null;

  const {
    data,
    isLoading,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
    refetch,
    isRefetching,
  } = useShareProfiles();

  const items = useMemo(() => {
    if (!data?.pages) {
      return [];
    }

    const flattenedItems = data.pages.flatMap(page => {
      // Handle different possible response structures
      if (page?.data?.items) {
        // Expected structure: { data: { items: [], totalItems: number } }
        return page.data.items;
      } else if (page?.data && Array.isArray(page.data)) {
        // Alternative structure: { data: [] }
        return page.data;
      } else if (Array.isArray(page)) {
        // Direct array structure: []
        return page;
      } else if (page && typeof page === 'object') {
        // Single item structure - wrap in array
        return [page];
      } else {
        return [];
      }
    });

    return flattenedItems;
  }, [data]);

  useEffect(() => {
    if (navigate) {
      navigation.navigate(navigate.screen, {...navigate.params});
    }
  }, [navigate, navigation]);

  const renderItem = ({item}: {item: ShareItemType}) => {
    return <ShareItem item={item} />;
  };

  const onEndReached = () => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  };

  const onRefresh = () => {
    refetch();
  };

  const emptyList = () => (
    <>
      {!isLoading && (
        <EmptyList title={'Không có hồ sơ'} subtitle="Kéo xuống để tải lại" />
      )}
    </>
  );

  const footer = () => (
    <View style={styles.footerStyle}>
      {isFetchingNextPage ? (
        <Text
          style={[
            styles.footerTextStyle,
            {color: theme.colors.onSurfaceVariant},
          ]}>
          Đang tải
        </Text>
      ) : (
        <Text
          style={[
            styles.footerTextStyle,
            {color: theme.colors.onSurfaceVariant},
          ]}>
          {hasNextPage ? 'Kéo để tải thêm' : 'Cuối trang'}
        </Text>
      )}
    </View>
  );

  return (
    <ErrorBoundary name="ShareScreens">
      <View
        style={[styles.container, {backgroundColor: theme.colors.background}]}>
        <CCAppBar label="Hồ sơ đã nộp" isBack={true} />
        <FlatList
          style={styles.flatListStyle}
          contentContainerStyle={styles.flatListContainer}
          refreshing={isRefetching}
          onRefresh={onRefresh}
          extraData={items}
          data={items}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          onEndReachedThreshold={0.01}
          onEndReached={onEndReached}
          ListEmptyComponent={emptyList}
          ListFooterComponent={footer}
        />
      </View>
    </ErrorBoundary>
  );
};

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
  },
  flatListStyle: {
    flex: 1,
  },
  flatListContainer: {
    flexGrow: 1,
    paddingHorizontal: 16,
    paddingTop: 8,
  },
  itemContainer: {
    marginHorizontal: 16,
  },
  footerStyle: {
    margin: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  footerTextStyle: {
    textAlign: 'center',
    fontSize: 14,
  },
});
export default SharesScreen;
