import React, {useState, useCallback} from 'react';
import {
  View,
  StyleSheet,
  RefreshControl,
  Alert,
  BackHandler,
} from 'react-native';
import Animated, {
  useAnimatedScrollHandler,
  useSharedValue,
} from 'react-native-reanimated';
import {
  useRoute,
  useNavigation,
  RouteProp,
  useFocusEffect,
} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {drawNumber} from '../../../../requester-biz-service/apis/draw-number-api';
import {
  getTicketStatusInfo,
  useTicketStore,
} from '../../../../stores/ticket.store';
import {useUserStore, getCitizenCode} from '../../../../stores/user.store';
import {MainStackParamList} from '../../../../navigation/MainNavigator';
import {useTicketDetail} from '../../../../hooks/useTicketDetail';
import {useAnimatedHeaderStyles} from '../../../../hooks';
import TicketNumberCard from './components/TicketNumberCard';
import ServingStatusCard from './components/ServingStatusCard';
import ServiceDetailsCard from './components/ServiceDetailsCard';
import LoadingState from './components/LoadingState';
import ErrorState from './components/ErrorState';
import ActionButtons from './components/ActionButtons';
import BranchInfo from '../DrawNumber/components/BranchInfo';
import {CCAppBar} from '../../../../components/QNH_CongDanComponents/HeaderComponent/CCAppBar';
import {useTheme} from 'react-native-paper';

type TicketDetailScreenRouteProp = RouteProp<
  MainStackParamList,
  'TicketDetail'
>;

type NavigationProp = NativeStackNavigationProp<MainStackParamList>;

const TicketDetailScreen: React.FC = () => {
  const route = useRoute<TicketDetailScreenRouteProp>();
  const navigation = useNavigation<NavigationProp>();
  const {ticketId} = route.params;
  const [isCancelling, setIsCancelling] = useState(false);

  const scrollY = useSharedValue(0);
  const {headerStyle} = useAnimatedHeaderStyles(scrollY);
  const scrollHandler = useAnimatedScrollHandler(event => {
    scrollY.value = event.contentOffset.y;
  });

  const {refreshTickets} = useTicketStore();
  const {userProfile} = useUserStore();
  const theme = useTheme();

  const {
    data: details,
    isLoading,
    error,
    refetch,
    isRefetching,
  } = useTicketDetail({ticketId});

  const navigateToHome = useCallback(() => {
    navigation.reset({
      index: 0,
      routes: [{name: 'Dashboard'}],
    });
  }, [navigation]);

  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        navigateToHome();
        return true;
      };

      const subscription = BackHandler.addEventListener(
        'hardwareBackPress',
        onBackPress,
      );

      return () => subscription.remove();
    }, [navigateToHome]),
  );

  const handleCancelSchedule = useCallback(async () => {
    const identificationId = getCitizenCode(userProfile);
    if (!identificationId || !details) {
      Alert.alert('Lỗi', 'Không tìm thấy thông tin người dùng');
      return;
    }

    setIsCancelling(true);
    try {
      await drawNumber.cancelTicket({
        citizenCode: identificationId,
        ticketId: details.id,
      });

      Alert.alert('Thành công', 'Huỷ lịch hẹn thành công', [
        {
          text: 'OK',
          onPress: () => {
            refreshTickets(identificationId);
            navigateToHome();
          },
        },
      ]);
    } catch (err: any) {
      console.error('Error cancelling ticket:', err);
      Alert.alert('Lỗi', 'Không thể huỷ lịch hẹn. Vui lòng thử lại.');
    } finally {
      setIsCancelling(false);
    }
  }, [details, userProfile, refreshTickets, navigateToHome]);

  const showCancelConfirmation = useCallback(() => {
    Alert.alert(
      'Xác nhận huỷ lịch hẹn',
      'Quý khách có thực sự muốn huỷ lịch hẹn này không?',
      [
        {
          text: 'Quay lại',
          style: 'cancel',
        },
        {
          text: 'Đồng ý',
          style: 'destructive',
          onPress: handleCancelSchedule,
        },
      ],
    );
  }, [handleCancelSchedule]);

  const formatDateTime = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      // Add 7 hours for timezone adjustment (like in Flutter)
      date.setHours(date.getHours() + 0);
      return date.toLocaleDateString('vi-VN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch (err) {
      return dateString;
    }
  };

  const renderContent = () => {
    if (isLoading) {
      return <LoadingState />;
    }

    if (error || !details) {
      return (
        <ErrorState
          message={error?.message || 'Không tìm thấy thông tin số'}
          onRetry={() => refetch()}
        />
      );
    }

    const statusInfo = getTicketStatusInfo(details.status);

    // Helper function to get the branch name from details
    const getBranchName = () => {
      return (
        details.services?.qms_branch?.name ||
        details.services?.qmsBranch?.name ||
        details.services?.name ||
        'Không có thông tin chi nhánh'
      );
    };

    // Helper function to get the branch address from details
    const getBranchAddress = () => {
      return (
        details.services?.qms_branch?.address ||
        details.services?.qmsBranch?.address ||
        details.services?.description ||
        'Không có địa chỉ'
      );
    };

    // Helper function to get ticket number
    const getTicketNumber = () => {
      return details.ticketNumber || (details as any).ticket_number || 'N/A';
    };

    // Helper function to get counter names
    const getCounterNames = () => {
      return (
        details.counterServing?.map(counter => counter.name).join(', ') || ''
      );
    };

    return (
      <Animated.ScrollView
        onScroll={scrollHandler}
        scrollEventThrottle={16}
        style={[styles.content]}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefetching}
            onRefresh={() => {
              console.log('🔄 [TicketDetail] Pull-to-refresh triggered');
              refetch();
            }}
            tintColor={theme.colors.primary}
          />
        }>
        <View style={[styles.contentContainer]}>
          {/* Branch Info Card - Now shown first */}
          <BranchInfo
            branch={{
              name: getBranchName(),
              address: getBranchAddress(),
              id: details.services?.qms_branch?.id || '',
            }}
          />

          {/* Ticket Number Card */}
          <TicketNumberCard
            ticketNumber={getTicketNumber()}
            status={details.status}
            statusText={statusInfo.text}
            statusColor={statusInfo.color}
            updatedAt={details.updatedAt}
            formatDateTime={formatDateTime}
          />

          {/* Currently Serving Card */}
          {details.servingTicket && (
            <ServingStatusCard
              ticketNumber={details.servingTicket.ticket_number || ''}
            />
          )}

          {/* Service Details Card */}
          <ServiceDetailsCard
            serviceName={details.services?.name || ''}
            counterName={getCounterNames()}
            createdAt={details.createdAt}
            isOnline={details.isOnline}
            formatDateTime={formatDateTime}
          />
        </View>
      </Animated.ScrollView>
    );
  };

  return (
    <View
      style={[styles.container, {backgroundColor: theme.colors.background}]}>
      <Animated.View style={[{zIndex: 999}, headerStyle]}>
        <CCAppBar label="Chi tiết lịch hẹn" isBack />
      </Animated.View>

      <View style={[styles.wrapper]}>
        {renderContent()}

        {/* Action Buttons */}
        {!isLoading && !error && details && (
          <View style={[styles.buttonContainer]}>
            <ActionButtons
              status={details.status}
              isCancelling={isCancelling}
              onCancel={showCancelConfirmation}
              onClose={navigateToHome}
            />
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: theme.colors.background will be set inline
  },
  wrapper: {
    flex: 1,
  },
  content: {
    flex: 1,
    // backgroundColor: theme.colors.background will be set inline
  },
  contentContainer: {
    padding: 16,
    gap: 16,
    paddingBottom: 80,
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
    padding: 8,
    paddingHorizontal: 5,
    paddingBottom: 5,
  },
});

export default TicketDetailScreen;
