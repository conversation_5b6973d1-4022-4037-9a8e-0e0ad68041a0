import React, {ReactNode} from 'react';
import {View, Text, StyleSheet} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useTheme} from 'react-native-paper';

interface CardContainerProps {
  title: string;
  icon?: string;
  iconColor?: string;
  children: ReactNode;
  style?: object;
  leftBorderColor?: string;
}

let CardContainer: React.FC<CardContainerProps> = ({
  title,
  icon,
  iconColor,
  children,
  style,
  leftBorderColor,
}) => {
  const theme = useTheme();
  return (
    <View
      style={[
        styles.card,
        {
          backgroundColor: theme.colors.surface,
          shadowColor: theme.colors.shadow,
        },
        leftBorderColor
          ? [styles.leftBorder, {borderLeftColor: leftBorderColor}]
          : undefined,
        style,
      ]}>
      <View style={styles.header}>
        {icon && (
          <Icon
            name={icon}
            size={24}
            color={iconColor || theme.colors.primary}
          />
        )}
        <Text style={[styles.headerText, {color: theme.colors.onSurface}]}>
          {title}
        </Text>
      </View>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 12,
    padding: 16,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 3,
    marginBottom: 12,
  },
  leftBorder: {
    borderLeftWidth: 4,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 14,
    gap: 8,
  },
  headerText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
export default CardContainer = React.memo(CardContainer);
