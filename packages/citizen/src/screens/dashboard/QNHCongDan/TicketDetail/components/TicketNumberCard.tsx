import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import CardContainer from './CardContainer';
import StatusBadge from './StatusBadge';
import {useTheme} from 'react-native-paper';

interface TicketNumberCardProps {
  ticketNumber: string;
  status: string;
  statusText: string;
  statusColor: string;
  updatedAt?: string;
  formatDateTime?: (dateString: string) => string;
}

const TicketNumberCard: React.FC<TicketNumberCardProps> = ({
  ticketNumber,
  status,
  statusText,
  statusColor,
  updatedAt,
  formatDateTime,
}) => {
  const theme = useTheme();
  return (
    <CardContainer
      title="Số của quý khách"
      icon="confirmation-number"
      iconColor={theme.colors.primary}
      style={[styles.container]}>
      <View
        style={[
          styles.ticketNumberContainer,
          {
            backgroundColor: theme.colors.primaryContainer,
            borderColor: theme.colors.primary,
          },
        ]}>
        <Text style={[styles.ticketNumber, {color: theme.colors.primary}]}>
          {ticketNumber || 'N/A'}
        </Text>
      </View>

      {/* Status Badge */}
      <StatusBadge
        status={status}
        text={statusText}
        color={statusColor}
        updatedAt={status === 'CANCELLED' && updatedAt ? updatedAt : undefined}
        formatDateTime={formatDateTime}
      />
    </CardContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    padding: 20,
  },
  ticketNumberContainer: {
    alignItems: 'center',
    marginBottom: 20,
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 20,
    borderWidth: 2,
    minWidth: 200,
  },
  ticketNumber: {
    fontSize: 32,
    fontWeight: 'bold',
    textAlign: 'center',
    letterSpacing: 4,
  },
});

export default TicketNumberCard;
