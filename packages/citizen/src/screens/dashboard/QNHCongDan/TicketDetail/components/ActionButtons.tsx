import React from 'react';
import SharedActionButtons from '../../../../../components/shared/ActionButtons';

interface ActionButtonsProps {
  status: string;
  isCancelling: boolean;
  onCancel: () => void;
  onClose: () => void;
}

const ActionButtons: React.FC<ActionButtonsProps> = ({
  status,
  isCancelling,
  onCancel,
  onClose,
}) => {
  const isComplete = ['CALLED', 'FINISHED', 'CANCELLED'].includes(status);

  if (isComplete) {
    return (
      <SharedActionButtons
        primaryButton={{
          label: 'Đóng',
          onPress: onClose,
          variant: 'contained',
          flex: true,
        }}
        singleButton={true}
      />
    );
  }

  return (
    <SharedActionButtons
      primaryButton={{
        label: 'Đóng',
        onPress: onClose,
        variant: 'contained',
        flex: true,
      }}
      secondaryButton={{
        label: 'Huỷ lịch hẹn',
        onPress: onCancel,
        variant: 'elevated',
        loading: isCancelling,
        disabled: isCancelling,
        flex: true,
      }}
    />
  );
};

export default ActionButtons;
