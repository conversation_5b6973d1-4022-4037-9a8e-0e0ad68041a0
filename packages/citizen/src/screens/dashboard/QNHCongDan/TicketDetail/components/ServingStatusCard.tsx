import React from 'react';
import {Text, StyleSheet} from 'react-native';
import CardContainer from './CardContainer';
import {useTheme} from 'react-native-paper';

interface ServingStatusCardProps {
  ticketNumber: string;
}

const ServingStatusCard: React.FC<ServingStatusCardProps> = ({
  ticketNumber,
}) => {
  const theme = useTheme();
  return (
    <CardContainer
      title="Đang phục vụ"
      icon="play-arrow"
      iconColor={theme.colors.error}
      leftBorderColor={theme.colors.error}>
      <Text style={[styles.servingNumber, {color: theme.colors.error}]}>
        {ticketNumber || ''}
      </Text>
    </CardContainer>
  );
};

const styles = StyleSheet.create({
  servingNumber: {
    fontSize: 32,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default ServingStatusCard;
