import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface StatusBadgeProps {
  status: string;
  text: string;
  color: string;
  updatedAt?: string;
  formatDateTime?: (dateString: string) => string;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  text,
  color,
  updatedAt,
  formatDateTime,
}) => {
  return (
    <View style={[styles.statusBadge, {backgroundColor: color + '20'}]}>
      <Icon name="circle" size={8} color={color} />
      <Text style={[styles.statusText, {color}]}>
        {text}
        {status === 'CANCELLED' && updatedAt && formatDateTime
          ? ` - ${formatDateTime(updatedAt)}`
          : ''}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 8,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default StatusBadge;
