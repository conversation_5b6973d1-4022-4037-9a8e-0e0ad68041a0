import React from 'react';
import {View, Text, StyleSheet, useWindowDimensions} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import CardContainer from './CardContainer';
import {useTheme} from 'react-native-paper';

interface ServiceDetailsCardProps {
  serviceName: string;
  counterName: string;
  createdAt: string;
  isOnline: number;
  formatDateTime: (dateString: string) => string;
}

const ServiceDetailsCard: React.FC<ServiceDetailsCardProps> = ({
  serviceName,
  counterName,
  createdAt,
  isOnline,
  formatDateTime,
}) => {
  const {width} = useWindowDimensions();
  const theme = useTheme();

  // If screen width is less than 360px, use full width
  const itemStyle =
    width < 360
      ? [styles.detailItem, styles.fullWidthDetailItem]
      : styles.detailItem;

  return (
    <CardContainer title="Chi tiết dịch vụ">
      <View style={styles.detailsGrid}>
        <View style={itemStyle}>
          <Icon name="category" size={20} color={theme.colors.onSurface} />
          <View style={styles.detailContent}>
            <Text style={[styles.detailLabel, {color: theme.colors.onSurface}]}>
              Lĩnh vực
            </Text>
            <Text style={[styles.detailValue, {color: theme.colors.onSurface}]}>
              {serviceName || ''}
            </Text>
          </View>
        </View>

        <View style={itemStyle}>
          <Icon
            name="desktop-windows"
            size={20}
            color={theme.colors.onSurface}
          />
          <View style={styles.detailContent}>
            <Text style={[styles.detailLabel, {color: theme.colors.onSurface}]}>
              Khu vực quầy
            </Text>
            <Text style={[styles.detailValue, {color: theme.colors.onSurface}]}>
              {counterName || ''}
            </Text>
          </View>
        </View>

        <View style={itemStyle}>
          <Icon name="schedule" size={20} color={theme.colors.onSurface} />
          <View style={styles.detailContent}>
            <Text style={[styles.detailLabel, {color: theme.colors.onSurface}]}>
              Thời gian lấy số
            </Text>
            <Text style={[styles.detailValue, {color: theme.colors.onSurface}]}>
              {formatDateTime(createdAt)}
            </Text>
          </View>
        </View>

        <View style={itemStyle}>
          <Icon
            name={isOnline === 1 ? 'wifi' : 'store'}
            size={20}
            color={theme.colors.onSurface}
          />
          <View style={styles.detailContent}>
            <Text style={[styles.detailLabel, {color: theme.colors.onSurface}]}>
              Phương thức
            </Text>
            <Text style={[styles.detailValue, {color: theme.colors.onSurface}]}>
              {isOnline === 1 ? 'Lấy số online' : 'Lấy số tại quầy'}
            </Text>
          </View>
        </View>
      </View>
    </CardContainer>
  );
};

const styles = StyleSheet.create({
  detailsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    justifyContent: 'space-between',
    width: '100%',
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
    width: '48%', // Default: half width for 2 columns with gap
    minWidth: 150, // Reduced minimum width to better fit small screens
    flexGrow: 1, // Allow growing to fill available space
  },
  fullWidthDetailItem: {
    width: '100%', // Full width for small screens
    marginBottom: 8, // Add some space between items in single column mode
  },
  detailContent: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 13,
    color: '#666',
    marginBottom: 4,
    fontWeight: '500',
  },
  detailValue: {
    fontSize: 15,
    color: '#333',
    fontWeight: '500',
    lineHeight: 20,
  },
});

export default ServiceDetailsCard;
