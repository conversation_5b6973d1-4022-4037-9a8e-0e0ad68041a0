import React from 'react';
import {Text, StyleSheet} from 'react-native';
import CardContainer from './CardContainer';
import {useTheme} from 'react-native-paper';

interface BranchInfoCardProps {
  name: string;
  address: string;
}

const BranchInfoCard: React.FC<BranchInfoCardProps> = ({name, address}) => {
  const theme = useTheme();
  return (
    <CardContainer
      title="Thông tin chi nhánh"
      icon="location-on"
      iconColor={theme.colors.primary}>
      <Text style={[styles.branchName, {color: theme.colors.onSurface}]}>
        {name || 'Không có thông tin chi nhánh'}
      </Text>
      <Text style={[styles.branchAddress, {color: theme.colors.onSurface}]}>
        {address || 'Không có địa chỉ'}
      </Text>
    </CardContainer>
  );
};

const styles = StyleSheet.create({
  branchName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 6,
  },
  branchAddress: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
});

export default BranchInfoCard;
