import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useTheme} from 'react-native-paper';

interface ErrorStateProps {
  message?: string;
  onRetry: () => void;
}

const ErrorState: React.FC<ErrorStateProps> = ({
  message = 'Không tìm thấy thông tin số',
  onRetry,
}) => {
  const theme = useTheme();
  return (
    <View style={styles.errorContainer}>
      <Icon name="error-outline" size={48} color={theme.colors.error} />
      <Text style={[styles.errorText, {color: theme.colors.onSurface}]}>
        {message}
      </Text>
      <TouchableOpacity
        style={[styles.retryButton, {backgroundColor: theme.colors.primary}]}
        onPress={onRetry}>
        <Text style={[styles.retryButtonText, {color: theme.colors.onPrimary}]}>
          Thử lại
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  errorText: {
    color: '#666',
    textAlign: 'center',
    marginVertical: 16,
    fontSize: 16,
    lineHeight: 24,
  },
  retryButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
});

export default ErrorState;
