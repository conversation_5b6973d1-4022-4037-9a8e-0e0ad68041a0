import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {Card, useTheme} from 'react-native-paper';
import {NavigationProp, useNavigation} from '@react-navigation/native';
import {MainStackParamList} from '../../../../navigation/MainNavigator';
import {
  ICommonProcedureLoading,
  UseRequesterBizTemplates,
} from '../../../../useQueryState';
import {generateDataLoading} from '../../../../utils/fn';
import {AnyObj} from '@ac-mobile/common';
import {IChoseCommonProcedure, useCommonProcedure} from '../../../../stores';
import {
  CommonProcedureItem,
  ColorPickerIcon,
  LoadingComponent,
  LayoutComponent,
} from '../../../../components';

export const CommonProcedureList = React.memo(() => {
  // COMMENT: Store
  const {getProcedureItem} = useCommonProcedure();

  console.log('CommonProcedureList');
  // COMMENT: hook
  const nav = useNavigation<NavigationProp<MainStackParamList>>();
  const theme = useTheme();

  const [data, setData] = React.useState<ICommonProcedureLoading[]>([]);

  const params = {
    page: 1,
    pageSize: 21,
    orderBy: 'createdAt',
    orderDir: 'desc',
    status: 'ACTIVE',
    // tags: 'DVCQNMOBILE',
    tags: 'IHANOIMOBILE',
  };

  const {data: templateSelected, isLoading: isLoadingTemplate} =
    UseRequesterBizTemplates({params});

  React.useEffect(() => {
    if (isLoadingTemplate) {
      const payload = {
        id: '0',
        name: '',
        statusLoading: true,
      } as ICommonProcedureLoading;
      const responseLoading = generateDataLoading({count: 6, payload});
      setData(responseLoading);
    } else if (!isLoadingTemplate && templateSelected?.data?.data?.items) {
      const responseTemplate = templateSelected?.data?.data?.items.map(
        (item: AnyObj) => ({
          id: item?.id || 0,
          name: item?.name || '',
          statusLoading: false,
        }),
      ) as ICommonProcedureLoading[];

      setData(responseTemplate);
    }
  }, [isLoadingTemplate, templateSelected?.data?.data?.items]);

  const handleGo = async ({id, name}: {id: string; name: string}) => {
    const procedureItem: IChoseCommonProcedure = {
      procedureId: id,
      procedureName: name,
    };
    await getProcedureItem(procedureItem);
    nav.navigate('CommonProcedureDetail');
  };

  return (
    <View
      style={[styles.container, isLoadingTemplate && styles.containerLoading]}>
      {isLoadingTemplate ? (
        <Card style={[styles.card, isLoadingTemplate && styles.cardFullHeight]}>
          <Card.Content style={styles.cardContentLoading}>
            <LoadingComponent isVisible={isLoadingTemplate} />
          </Card.Content>
        </Card>
      ) : (
        <View style={styles.content}>
          <View>
            <Text
              style={[styles.cardTitleText, {color: theme.colors.onSurface}]}>
              Thủ tục phổ biến
            </Text>
          </View>

          <LayoutComponent type="grid" columns={3}>
            {data.map((item: ICommonProcedureLoading, index: number) => (
              <CommonProcedureItem
                key={index}
                title={item.name}
                id={item.id}
                isLoading={item.statusLoading}
                onPress={(id: string, name: string) => handleGo({id, name})}
                icon={<ColorPickerIcon paint={['#919EAB', '#919EAB']} />}
              />
            ))}
          </LayoutComponent>
        </View>
      )}
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB', // Màu nền tổng thể
    padding: 16,
  },
  cardTitleText: {
    fontSize: 20,
    fontWeight: '600',
  },
  containerLoading: {
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  card: {
    borderRadius: 8,
    backgroundColor: '#fff',
    width: '100%',
  },
  cardFullHeight: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cardContentLoading: {
    flex: 1,
    justifyContent: 'center', // căn giữa theo chiều dọc
    alignItems: 'center', // căn giữa theo chiều ngang
    padding: 16,
  },
  content: {
    display: 'flex',
    flexDirection: 'column',
    gap: 16,
  },
});
