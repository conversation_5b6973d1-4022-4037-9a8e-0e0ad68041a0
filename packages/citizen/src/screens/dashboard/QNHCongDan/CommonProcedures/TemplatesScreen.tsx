import React, {useEffect, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import Animated, {
  useAnimatedScrollHandler,
  useSharedValue,
} from 'react-native-reanimated';
import {useAnimatedHeaderStyles} from '../../../../hooks';
import {CommonProcedureList} from './CommonProcedureList';
import {useCommonProcedure} from '../../../../stores';
import {CommonProcedureListSearch} from './CommonProcedureListSearch';

import {CCAppBar} from '../../../../components/QNH_CongDanComponents/HeaderComponent/CCAppBar';

export const TemplatesScreen = React.memo(() => {
  const scrollY = useSharedValue(0);
  const {headerStyle} = useAnimatedHeaderStyles(scrollY);

  const scrollHandler = useAnimatedScrollHandler(event => {
    scrollY.value = event.contentOffset.y;
  });

  // COMMENT: store
  const {textSearchProcedureItem} = useCommonProcedure();

  // COMMENT: state
  const [showSearchProcedure, setShowSearchProcedure] =
    useState<boolean>(false);

  console.log({textSearchProcedureItem, showSearchProcedure});

  // COMMENT: useEffect
  useEffect(() => {
    if (textSearchProcedureItem && !showSearchProcedure) {
      setShowSearchProcedure(true);
    } else if (!textSearchProcedureItem && showSearchProcedure) {
      setShowSearchProcedure(false);
    }
  }, [textSearchProcedureItem, showSearchProcedure]);

  // COMMENT: function

  return (
    <View className="flex-1">
      <Animated.View style={[{zIndex: 999}, headerStyle]}>
        <CCAppBar isBack label={'Thủ tục phổ biến'} />
      </Animated.View>
      <Animated.ScrollView
        onScroll={scrollHandler}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainer}>
        {(showSearchProcedure && <CommonProcedureListSearch />) || (
          <CommonProcedureList />
        )}
      </Animated.ScrollView>
    </View>
  );
});

const styles = StyleSheet.create({
  contentContainer: {
    flexGrow: 1,
    paddingBottom: 24,
    zIndex: 999,
  },
});
