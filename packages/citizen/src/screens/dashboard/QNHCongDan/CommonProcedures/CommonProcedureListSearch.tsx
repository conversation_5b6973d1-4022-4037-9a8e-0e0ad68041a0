import React from 'react';
import {StyleSheet, View} from 'react-native';
import {NavigationProp, useNavigation} from '@react-navigation/native';
import {MainStackParamList} from '../../../../navigation/MainNavigator';
import {
  ICommonProcedureLoading,
  UseCongDangApiTemplates,
} from '../../../../useQueryState';
import {generateDataLoading} from '../../../../utils/fn';
import {AnyObj} from '@ac-mobile/common';
import {IChoseCommonProcedure, useCommonProcedure} from '../../../../stores';
import {
  CommonProcedureItem,
  ColorPickerIcon,
  LayoutComponent,
} from '../../../../components';

export const CommonProcedureListSearch = React.memo(() => {
  const {getProcedureItem, textSearchProcedureItem} = useCommonProcedure();
  const nav = useNavigation<NavigationProp<MainStackParamList>>();

  const [data, setData] = React.useState<ICommonProcedureLoading[]>([]);

  // ✅ Tạo params cố định khi textSearchProcedureItem thay đổi
  const params = React.useMemo(
    () => ({
      page: 1,
      pageSize: 21,
      orderBy: 'createdAt',
      orderDir: 'desc',
      status: 'ACTIVE',
      tags: 'IHANOIMOBILE',
      keyword: textSearchProcedureItem,
    }),
    [textSearchProcedureItem], // <-- chỉ tạo mới khi thay đổi keyword
  );

  const {data: templateSelected, isLoading: isLoadingTemplate} =
    UseCongDangApiTemplates({params, textSearchProcedureItem});

  React.useEffect(() => {
    if (isLoadingTemplate) {
      const payload = {
        id: '0',
        name: '',
        statusLoading: true,
      } as ICommonProcedureLoading;
      const responseLoading = generateDataLoading({count: 6, payload});
      setData(responseLoading);
    } else if (templateSelected?.data?.data?.items) {
      const responseTemplate = templateSelected.data.data.items.map(
        (item: AnyObj) => ({
          id: item?.id || 0,
          name: item?.name || '',
          statusLoading: false,
        }),
      ) as ICommonProcedureLoading[];

      setData(responseTemplate);
    }
  }, [isLoadingTemplate, templateSelected?.data?.data?.items]);

  const handleGo = async ({id, name}: {id: string; name: string}) => {
    const procedureItem: IChoseCommonProcedure = {
      procedureId: id,
      procedureName: name,
    };
    await getProcedureItem(procedureItem);
    nav.navigate('CommonProcedureDetail');
  };

  return (
    <View
      style={[styles.container, isLoadingTemplate && styles.containerLoading]}>
      <LayoutComponent type="vertical" columns={1}>
        {data.map((item, index) => (
          <CommonProcedureItem
            key={index}
            title={item.name}
            id={item.id}
            isLoading={item.statusLoading}
            onPress={(id, name) => handleGo({id, name})}
            icon={<ColorPickerIcon paint={['#919EAB', '#919EAB']} />}
            direction="horizontal"
          />
        ))}
      </LayoutComponent>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    padding: 16,
  },
  containerLoading: {
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
});
