import React from 'react';
import {Pressable, StyleSheet, Text, View} from 'react-native';
import {Card} from 'react-native-paper';
import {textStyles} from '../../../../../styles';
import {E_DOCUMENT_SHARE_PROGRESS_NAME} from '../../../../../utils/fn';
import {SVGTimer} from '../../../../../icons/cong-dan_icons/Timer';
import {SVGCheckmarkCircle2Fill} from '../../../../../icons';
import Icon from 'react-native-vector-icons/MaterialIcons';

type props = {
  id: string;
  status: string;
  titleStatus: string;
  colorBg: string;
  submitAddress: string;
  createdAt: string;
  name: string;
  code: string;
  onPressItem: (id: string) => void;
};

export const HistoryItem = React.memo(
  ({
    id,
    status,
    titleStatus,
    colorBg,
    submitAddress,
    createdAt,
    name,
    code,
    onPressItem,
  }: props) => {
    return (
      <Pressable onPress={() => onPressItem(id)}>
        <Card style={styles.card}>
          <View style={[styles.title, {backgroundColor: colorBg}]}>
            <View style={[styles.titleContent]}>
              {(status === E_DOCUMENT_SHARE_PROGRESS_NAME.ACCEPTED && (
                <SVGCheckmarkCircle2Fill />
              )) || <SVGTimer />}
              <Text className={`${textStyles.label}`}>{titleStatus}</Text>
            </View>
            <View>
              <Icon name="arrow-forward-ios" size={18} />
            </View>
          </View>
          <Card.Content>
            <View style={styles.content}>
              <View>
                <Text className={`${textStyles.subTitle2}`}>{name}</Text>
              </View>
              <View style={styles.infoItem}>
                <Text>Mã hồ sơ</Text>
                <Text>{code}</Text>
              </View>
              <View style={styles.infoItem}>
                <Text>Mã hồ sơ</Text>
                <Text>{submitAddress}</Text>
              </View>
              <View style={styles.infoItem}>
                <Text>Mã hồ sơ</Text>
                <Text>{createdAt}</Text>
              </View>
            </View>
          </Card.Content>
        </Card>
      </Pressable>
    );
  },
);

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#FFF',
  },
  title: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 8,
  },
  titleContent: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  content: {
    paddingTop: 16,
    paddingBottom: 16,
    display: 'flex',
    flexDirection: 'column',
    gap: 16,
  },
  infoItem: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
});
