import React from 'react';
import {View} from 'react-native';
import {useCommonProcedure} from '../../../../../stores/QNHCongDan';
import {getStatusText, handleFormatDate} from '../../../../../utils/fn';
import {ICommonProcedureHistory} from './HistoryType';
import {UseCongDangApiShares} from '../../../../../useQueryState';
import {LayoutComponent} from '../../../../../components';
import {HistoryItem} from './HistoryItem';

export const CommonProcedureHistory = React.memo(() => {
  // COMMENT: Store
  const {choseCommonProcedureItem} = useCommonProcedure();

  const [historyItems, setHistoryItems] = React.useState<
    ICommonProcedureHistory[]
  >([]);

  const params = {
    page: 1,
    pageSize: 6,
    orderBy: 'createdAt',
    orderDir: 'desc',
    status: 'ACTIVE',
    // tags: 'DVCQNMOBILE',
    tags: 'IHANOIMOBILE',
    templateIds: choseCommonProcedureItem?.procedureId,
  };

  const {
    data: historyData,
    isLoading: isLoadingTemplate,
    isError: errorTemplates,
  } = UseCongDangApiShares({params});

  React.useEffect(() => {
    if (!isLoadingTemplate && historyData?.data?.data?.items) {
      const dataFormat: ICommonProcedureHistory[] =
        historyData?.data?.data?.items.map((item: any) => {
          const status = getStatusText(item.progress);

          const historyItem: ICommonProcedureHistory = {
            id: item.id,
            title: item?.data[0]?.name || '',
            code: item?.partnerData?.pDocumentSetId || '',
            addressSubmit:
              item?.data[0]?.metadata?.donViThucHien?.DiaChiNopHoSo || '',
            date: item.createdAt ? handleFormatDate(item.createdAt) : '',
            status: item.progress,
            statusTitle: status.label,
            statusColor: status.background,
          };
          return historyItem;
        });

      setHistoryItems(dataFormat);
    }
  }, [isLoadingTemplate, historyData?.data?.data?.items]);

  const handleOnPress = (id: string) => {
    // COMMENT: Di chuyển đến mà thông tin chi tiết hồ sơ đã nộp
    console.log({id});
  };

  return (
    <View>
      <LayoutComponent type="vertical">
        {historyItems.map((item: ICommonProcedureHistory, index: number) => (
          <View key={index}>
            <HistoryItem
              id={item.id}
              status={item.status}
              titleStatus={item.statusTitle}
              colorBg={item.statusColor}
              submitAddress={item.addressSubmit}
              createdAt={item.date}
              name={item.title}
              code={item.code}
              onPressItem={(id: string) => handleOnPress(id)}
            />
          </View>
        ))}
      </LayoutComponent>
    </View>
  );
});
