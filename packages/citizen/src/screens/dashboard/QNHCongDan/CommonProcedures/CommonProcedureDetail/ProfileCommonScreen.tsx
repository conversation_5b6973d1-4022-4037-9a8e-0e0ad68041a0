import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import Animated, {
  useAnimatedScrollHandler,
  useSharedValue,
} from 'react-native-reanimated';
import {useAnimatedHeaderStyles} from '../../../../../hooks';
import {useCommonProcedure} from '../../../../../stores';
import {AnyObj} from '@ac-mobile/common';
import {
  LoadingComponent,
  LayoutComponent,
  HeaderCommonProcedure,
} from '../../../../../components';
import {textStyles} from '../../../../../styles/QNH_textStyle';
import {EIsLoading} from '../../../../../types';

const ProfileCommonScreen = React.memo(() => {
  // COMMENT: Store
  const {isLoading, templateSelected} = useCommonProcedure();

  const scrollY = useSharedValue(0);
  const {headerStyle} = useAnimatedHeaderStyles(scrollY);
  const scrollHandler = useAnimatedScrollHandler(event => {
    scrollY.value = event.contentOffset.y;
  });

  // COMMENT: State
  const [profile, setProfile] = React.useState<AnyObj[]>([]);

  // COMMENT: useEffect
  React.useEffect(() => {
    if (!templateSelected || !templateSelected?.partnerData) {
      return;
    }

    const {hoSoKemTheo} = templateSelected?.partnerData;
    const profileData: AnyObj[] = hoSoKemTheo.map(
      (item: AnyObj, index: number) => ({
        label: `Giấy tờ ${index + 1}`,
        value: item.tenHoSoKemTheo || '',
      }),
    );
    setProfile(profileData);
  }, [templateSelected]);

  // COMMENT: Function

  return (
    <View className="flex-1">
      <Animated.View style={[{zIndex: 999}, headerStyle]}>
        <HeaderCommonProcedure title={'Thành phần giấy tờ'} />
      </Animated.View>
      <Animated.ScrollView
        onScroll={scrollHandler}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.content}>
        <View style={styles.container}>
          {/* {(isLoading === EIsLoading.LOADING && (
            <LoadingComponent
              isVisible={isLoading === 'LOADING' ? true : false}
            />
          )) || (
            <LayoutComponent type="vertical">
              {profile.map((item: AnyObj, index: number) => (
                <View key={index} style={styles.item}>
                  <Text className={`${textStyles.subTitle1}`}>
                    {item?.label}
                  </Text>
                  <Text className={`${textStyles.body2}`}>{item?.value}</Text>
                </View>
              ))}
            </LayoutComponent>
          )} */}
        </View>
      </Animated.ScrollView>
    </View>
  );
});

export default ProfileCommonScreen;

const styles = StyleSheet.create({
  content: {
    flexGrow: 1,
    zIndex: 999,
  },
  container: {
    paddingHorizontal: 16,
    paddingVertical: 24,
    display: 'flex',
    flexDirection: 'column',
    gap: 24,
  },
  item: {
    display: 'flex',
    flexDirection: 'column',
    gap: 8,
  },
});
