import React from 'react';
import {Safe<PERSON>reaView, StyleSheet, View} from 'react-native';
import Animated, {
  useAnimatedScrollHandler,
  useSharedValue,
} from 'react-native-reanimated';
import {useAnimatedHeaderStyles} from '../../../../../hooks';
import {
  HeaderCommonProcedure,
  LoadingComponent,
} from '../../../../../components';
import {EIsLoading, useCommonProcedure} from '../../../../../stores';

const StepCommonScreen = React.memo(() => {
  // COMMENT: Store
  // const {isLoading, templateSelected} = useCommonProcedure();

  const scrollY = useSharedValue(0);
  const {headerStyle} = useAnimatedHeaderStyles(scrollY);
  const scrollHandler = useAnimatedScrollHandler(event => {
    scrollY.value = event.contentOffset.y;
  });

  // COMMENT: State
  const [stepCommon, setStepCommon] = React.useState<string>('');

  // COMMENT: useEffect
  // React.useEffect(() => {
  //   if (!templateSelected || !templateSelected?.partnerData) {
  //     return;
  //   }
  //   const {trinhTuThucHien} = templateSelected?.partnerData;
  //   setStepCommon(trinhTuThucHien || '');
  // }, [templateSelected]);

  // COMMENT: Function

  return (
    <View className="flex-1">
      <Animated.View style={[{zIndex: 999}, headerStyle]}>
        <HeaderCommonProcedure title={'Trình tự thực hiện'} />
      </Animated.View>
      <Animated.ScrollView
        onScroll={scrollHandler}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.content}>
        {/* <View style={styles.container}>
          {(isLoading === EIsLoading.LOADING && (
            <LoadingComponent
              isVisible={isLoading === 'LOADING' ? true : false}
            />
          )) || (
            <SafeAreaView style={{flex: 1}}>
              <HTMLRenderer htmlContent={htmlString} />
            </SafeAreaView>
          )}
        </View> */}
      </Animated.ScrollView>
    </View>
  );
});

export default StepCommonScreen;

const styles = StyleSheet.create({
  content: {
    flexGrow: 1,
    zIndex: 999,
  },
  container: {
    paddingHorizontal: 16,
    paddingVertical: 24,
    display: 'flex',
    flexDirection: 'column',
    gap: 24,
  },
  item: {
    display: 'flex',
    flexDirection: 'column',
    gap: 8,
  },
});
