import React, {useEffect, useState} from 'react';
import {StyleSheet, Text, View} from 'react-native';
import Animated, {
  useAnimatedScrollHandler,
  useSharedValue,
} from 'react-native-reanimated';
import {useAnimatedHeaderStyles} from '../../../../../hooks';
import {
  HeaderCommonProcedure,
  LayoutComponent,
  LoadingComponent,
} from '../../../../../components';
import {useCommonProcedure} from '../../../../../stores';
import {AnyObj} from '@ac-mobile/common';
import {textStyles} from '../../../../../styles/QNH_textStyle';
import {EIsLoading} from '../../../../../types';

const InformationCommonScreen = React.memo(() => {
  // COMMENT: Store
  const {isLoading, templateSelected} = useCommonProcedure();

  const scrollY = useSharedValue(0);
  const {headerStyle} = useAnimatedHeaderStyles(scrollY);
  const scrollHandler = useAnimatedScrollHandler(event => {
    scrollY.value = event.contentOffset.y;
  });

  // COMMENT: State
  const [information, setInformation] = useState<AnyObj[]>([]);

  // COMMENT: useEffect
  useEffect(() => {
    if (!templateSelected || !templateSelected?.partnerData) {
      return;
    }

    const informationData: AnyObj[] = [];
    const {maTTHC, tenTTHC, tenCapThucHien} = templateSelected?.partnerData;
    if (maTTHC) {
      informationData.push({
        label: 'Mã thủ tục',
        value: maTTHC,
      });
    }
    if (tenTTHC) {
      informationData.push({
        label: 'Tên thủ tục',
        value: tenTTHC,
      });
    }
    if (tenCapThucHien) {
      informationData.push({
        label: 'Cấp thực hiện',
        value: tenCapThucHien,
      });
    }
    setInformation(informationData);
  }, [templateSelected]);

  // COMMENT: Function

  return (
    <View className="flex-1">
      <Animated.View style={[{zIndex: 999}, headerStyle]}>
        <HeaderCommonProcedure title={'Thông tin thủ tục'} />
      </Animated.View>
      <Animated.ScrollView
        onScroll={scrollHandler}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.content}>
        <View style={styles.container}>
          {(isLoading === EIsLoading.LOADING && (
            <LoadingComponent
              isVisible={isLoading === 'LOADING' ? true : false}
            />
          )) || (
            <LayoutComponent type="vertical">
              {information.map((item: AnyObj, index: number) => (
                <View key={index} style={styles.item}>
                  <Text className={`${textStyles.subTitle1}`}>
                    {item?.label}
                  </Text>
                  <Text className={`${textStyles.body2}`}>{item?.value}</Text>
                </View>
              ))}
            </LayoutComponent>
          )}
        </View>
      </Animated.ScrollView>
    </View>
  );
});

export default InformationCommonScreen;

const styles = StyleSheet.create({
  content: {
    flexGrow: 1,
    zIndex: 999,
  },
  container: {
    paddingHorizontal: 16,
    paddingVertical: 24,
    display: 'flex',
    flexDirection: 'column',
    gap: 24,
  },
  item: {
    display: 'flex',
    flexDirection: 'column',
    gap: 8,
  },
});
