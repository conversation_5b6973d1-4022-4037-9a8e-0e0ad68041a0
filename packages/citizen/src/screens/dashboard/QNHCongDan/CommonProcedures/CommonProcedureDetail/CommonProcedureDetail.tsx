import React from 'react';
import {Pressable, StyleSheet, View} from 'react-native';
import Animated, {
  useAnimatedScrollHandler,
  useSharedValue,
} from 'react-native-reanimated';
import {LayoutComponent, DSDonViOptions} from '../../../../../components';
import {
  IDsDonViOption,
  IForwardData,
  useCommonProcedure,
} from '../../../../../stores';
import {AnyObj} from '@ac-mobile/common';
import {Button, Card, Text, useTheme} from 'react-native-paper';
import {TransferList, Stepper} from '../../../../../icons';
import {useNavigation} from '@react-navigation/native';
import {BottomSheetMethods} from '@gorhom/bottom-sheet/lib/typescript/types';
import {
  FormProvider,
  RHFSelected,
  RHFTextField,
} from '../../../../../components/RHFCustomComponent';
import {useForm} from 'react-hook-form';
import * as yup from 'yup';
import {yupResolver} from '@hookform/resolvers/yup';
import {CCAppBar} from '../../../../../components/QNH_CongDanComponents/HeaderComponent/CCAppBar';
import {HomeStackParamList} from '../../../../../navigation/HomeNavigator';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useSubmitFlow} from '../../../../../hooks/useSubmitFlow';
import {Dialog, Portal} from 'react-native-paper';

const data = [
  {
    title: 'Thông tin thủ tục',
    subTitle:
      'Bao gồm các thông tin tổng quan về thủ tục như mã thủ tục, tên thủ tục, lĩnh vực, v.v.',
    icon: 'info',
    path: 'InformationCommonScreen',
  },
  // {
  //   title: 'Trình tự thực hiện',
  //   subTitle: 'Cung cấp các bước cụ thể để hoàn tất một thủ tục hành chính',
  //   icon: 'step',
  //   path: 'StepCommonScreen',
  // },
  // {
  //   title: 'Thành phần giấy tờ',
  //   subTitle:
  //     'Những loại giấy tờ và tài liệu cần thiết để hoàn tất một thủ tục hành chính',
  //   icon: 'documents',
  //   path: 'ProfileCommonScreen',
  // },
];

type FormValues = {
  capThucHien: string;
  options: {
    id: string;
    label: string;
    value: {};
  };
};

type NavigationProp = NativeStackNavigationProp<HomeStackParamList>;

export const CommonProcedureDetail = React.memo(() => {
  const nav = useNavigation<NavigationProp>();
  const theme = useTheme();
  const {
    choseCommonProcedureItem,
    templateSelected,
    handleGetDonViThucHien,
    updateForwardData,
    dsDonViThucHienOptions,
  } = useCommonProcedure();

  const [title, setTitle] = React.useState<string>('');
  const clearStepOneData = useSubmitFlow(state => state.clearStepOneData);
  const scrollY = useSharedValue(0);
  const scrollHandler = useAnimatedScrollHandler(event => {
    scrollY.value = event.contentOffset.y;
  });

  const dialogDsDonVi = React.useRef<BottomSheetMethods | null>(null);

  const [showDialogDonVi, setShowDialogDonVi] = React.useState<boolean>(false);
  const [showFormDialog, setShowFormDialog] = React.useState<boolean>(false);

  React.useEffect(() => {
    console.log('choseCommonProcedureItem', choseCommonProcedureItem);
    if (choseCommonProcedureItem) {
      setTitle(choseCommonProcedureItem?.procedureName || '');
    }
  }, [choseCommonProcedureItem]);

  // Open dialog
  const handleStart = React.useCallback(() => {
    setShowFormDialog(true);
    handleGetDonViThucHien();
  }, [handleGetDonViThucHien]);

  // Close dialog
  const handleCloseDialogStart = React.useCallback(() => {
    setShowFormDialog(false);
  }, []);

  const handleOnPress = (path: string) => {
    if (path) {
      nav.navigate(path as any);
    }
  };

  const onHandleCloseDialogDsDonVi = () => {
    if (showDialogDonVi) {
      dialogDsDonVi.current?.close();
      setShowDialogDonVi(false);
    }
  };

  // Comment: Form
  const optionSchema = yup.object().shape({
    capThucHien: yup.string().required('Vui lòng chọn cấp thực hiện'),
    options: yup
      .object()
      .shape({
        id: yup.string().required('Vui lòng chọn cơ quan thực hiện'),
        label: yup.string().required(),
        value: yup.object(),
      })
      .required('Vui lòng chọn cơ quan thực hiện'),
  });

  const defaultValues = React.useMemo<FormValues>(
    () => ({
      capThucHien:
        (templateSelected && templateSelected?.partnerData?.tenCapThucHien) ||
        '',
      options: {
        id: '',
        label: '',
        value: {},
      },
    }),
    [templateSelected],
  );

  const methods = useForm<FormValues>({
    mode: 'onChange',
    resolver: yupResolver(optionSchema),
    defaultValues,
  });

  React.useEffect(() => {
    methods.reset({
      capThucHien:
        (templateSelected && templateSelected?.partnerData?.tenCapThucHien) ||
        '',
      options: {
        id: '',
        label: '',
        value: {},
      },
    });
  }, [templateSelected, methods]);

  // Ensure form resets when defaultValues change
  React.useEffect(() => {
    methods.reset(defaultValues);
  }, [defaultValues, methods]);

  const {
    handleSubmit,
    setError,
    clearErrors,
    setValue,
    formState: {errors},
  } = methods;

  const handleSubmitFormInfo = handleSubmit(async (dataForm, e) => {
    e?.preventDefault && e.preventDefault();
    try {
      if (dataForm.options.id !== '' && dataForm.capThucHien) {
        const {donViID, donViCapChaID, tenDonVi} = dataForm.options
          .value as IDsDonViOption;
        const forwardData = {
          CapThucHien: dataForm.capThucHien || '',
          DoiTuongThucHien: 'null',
          DonViThucHien: donViID || 0,
          DonViCapCha: String(donViCapChaID || 0),
          CapDonViID: donViID || 0,
          TenDonViThucHien: tenDonVi || '',
        } as IForwardData;

        await updateForwardData(forwardData);

        errors.options && clearErrors('options');
        errors.capThucHien && clearErrors('capThucHien');
        await handleCloseDialogStart();
        nav.navigate('StepOne');
        return;
      }
      if (!dataForm.options.id) {
        setError('options', {
          type: 'manual',
          message: 'Vui lòng chọn cơ quan thực hiện',
        });
      }
    } catch (error) {}
  });

  const handleChangeDSDonViOptions = (value: IDsDonViOption) => {
    const option = {
      id: value.donViID.toString(),
      label: value.tenDonVi,
      value: value,
    };
    setValue('options', option);
    if (errors.options) {
      clearErrors('options');
    }
    onHandleCloseDialogDsDonVi();
  };

  return (
    <View
      style={[styles.container, {backgroundColor: theme.colors.background}]}>
      <CCAppBar isBack label={title || 'Chi tiết thủ tục hành chính'} />
      <Animated.ScrollView
        onScroll={scrollHandler}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.content}>
        <View style={styles.container}>
          <LayoutComponent type="vertical">
            {data.map((item: AnyObj, index: number) => (
              <Pressable onPress={() => handleOnPress(item?.path)} key={index}>
                <Card style={styles.card}>
                  <View style={styles.row}>
                    <View style={styles.textContainer}>
                      <Text variant="titleMedium">{item?.title || ''}</Text>
                      <Text variant="bodyMedium">{item?.subTitle || ''}</Text>
                    </View>
                    <View style={styles.iconContainer}>
                      {(item?.icon === 'info' && <TransferList />) || (
                        <Stepper />
                      )}
                    </View>
                  </View>
                </Card>
              </Pressable>
            ))}
          </LayoutComponent>
          {/* <View>
          <BoxCollapsibleCustom
            startIcon={<Icon name="query-builder" size={24} />}
            title="Lịch sử yêu cầu"
            content={<CommonProcedureHistory />}
          />
        </View> */}
        </View>
      </Animated.ScrollView>
      <View style={styles.footer}>
        <Button mode="contained" style={styles.button} onPress={handleStart}>
          Tạo mới yêu cầu
        </Button>
      </View>

      {/* Dialog for form selection */}
      <Portal>
        <Dialog
          visible={showFormDialog}
          onDismiss={handleCloseDialogStart}
          style={{backgroundColor: theme.colors.surface}}>
          <Dialog.Title style={{textAlign: 'center'}}>
            Chọn cơ quan thực hiện
          </Dialog.Title>
          <Dialog.Content>
            <FormProvider methods={methods}>
              <View style={styles.info}>
                <RHFTextField
                  name="capThucHien"
                  required={true}
                  label="Cấp thực hiện"
                  disabled={true}
                />
                <RHFSelected
                  name="options"
                  required={true}
                  label="Cơ quan thực hiện"
                  options={(dsDonViThucHienOptions || []).map((item: any) => ({
                    id: item.donViID?.toString() || '',
                    label: item.tenDonVi || '',
                    value: item,
                  }))}
                />
              </View>
            </FormProvider>
          </Dialog.Content>
          <Dialog.Actions>
            <Button
              style={[styles.button, {marginTop: 0, marginLeft: 8}]}
              onPress={handleCloseDialogStart}>
              Hủy
            </Button>
            <Button
              mode="contained"
              style={[styles.button, {marginTop: 0, minWidth: 100}]}
              onPress={() => {
                handleSubmitFormInfo();
                clearStepOneData();
              }}>
              Xác nhận
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
      <DSDonViOptions
        refProp={dialogDsDonVi}
        selectedData={methods.getValues('options')?.id?.toString() || ''}
        onHandleSelect={(item: IDsDonViOption) =>
          handleChangeDSDonViOptions(item)
        }
      />
    </View>
  );
});

const FOOTER_HEIGHT = 80;

const styles = StyleSheet.create({
  content: {
    flexGrow: 1,
    paddingBottom: FOOTER_HEIGHT,
    zIndex: 999,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  container: {
    flex: 1,
  },
  card: {
    borderRadius: 12,
    padding: 16,
    elevation: 2, // Tạo hiệu ứng bóng
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  textContainer: {
    flex: 1,
    marginRight: 16,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    shadowOffset: {width: 0, height: -2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  button: {
    borderRadius: 8,
  },
  contentBottomSheet: {
    flex: 1,
    padding: 16,
  },
  overlayDialogStart: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  overlayDialogSelect: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  bottomSheet: {},
  title: {
    textAlign: 'center',
  },
  info: {
    display: 'flex',
    flexDirection: 'column',
    gap: 16,
  },
});
