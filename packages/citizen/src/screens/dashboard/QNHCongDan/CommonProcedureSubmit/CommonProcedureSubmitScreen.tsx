import React, {memo, useCallback, useEffect, useRef, useState} from 'react';
import {StyleSheet, Text, TouchableWithoutFeedback, View} from 'react-native';
import Animated, {
  useAnimatedScrollHand<PERSON>,
  useSharedValue,
} from 'react-native-reanimated'; // Sửa import Animated ở đây!
import {
  AcButtonActionFooter,
  BtsScrollView,
  HeaderCommonProcedure,
} from '../../../../components';
import {useAnimatedHeaderStyles} from '../../../../hooks';
import {useCommonProcedure} from '../../../../stores';
import {
  IOptionMenuItem,
  StepMenuVertical,
} from '../../../../components/AcStepMenu';
import {
  InformationUserScreen,
  InformationUserScreenRef,
} from './InformationUserScreen';
import {FOOTER_HEIGHT} from '../../../../styles';
import {BottomSheetMethods} from '@gorhom/bottom-sheet/lib/typescript/types';
import {AnyObj} from '@ac-mobile/common';
import {
  BtsSelect,
  EBtsInformationUserOptions,
  EDocumentType,
  EDTOptions,
  ENationalityOptions,
  ENcOptions,
  ETGOptions,
  submitScreenRef,
  IDoiTuongThucHienOptions,
  IOption,
} from '../../../../types';
import {BtsSexComponent} from './BtsInformationUser/BtsSexComponent';
import {Button} from 'react-native-paper';
import {BtsOptionsComponent} from './BtsInformationUser/BtsOptionsComponent';
import {BtsOptionInputComponent} from './BtsInformationUser/BtsOptionInputComponent';
import {BtsMainDT, IDoiTuongThucHienRef} from './BtsInformationUser/BtsMainDT';
import {BtsDoiTuongThucHienOptions} from './BtsInformationUser/BtsDoiTuongThucHienOptions';
import {textStyles} from '../../../../styles/QNH_textStyle';
import {EFormScreen} from './EFormScreen';
import {UploadFileScreen} from './UploadFileScreen';

const optionSteps: IOptionMenuItem[] = [
  {id: 1, name: 'Khai báo thông tin người yêu cầu'},
  {id: 2, name: 'Kê khai biểu mẫu điện tử'},
  {id: 3, name: 'Đính kèm giấy tờ'},
  {id: 4, name: 'Nhận kết quả & thanh toán'},
];

export const CommonProcedureSubmitScreen = memo(() => {
  const {choseCommonProcedureItem, eFormData} = useCommonProcedure();

  const [title, setTitle] = useState<string>('');
  const scrollY = useSharedValue(0);
  const {headerStyle} = useAnimatedHeaderStyles(scrollY);

  const scrollHandler = useAnimatedScrollHandler(event => {
    scrollY.value = event.contentOffset.y;
  });

  const [currentStep, setCurrentStep] = useState<number>(1);

  const btsMainRef = useRef<BottomSheetMethods | null>(null);
  const infoUserRef = useRef<InformationUserScreenRef>(null);
  const doiTuongThucHienRef = useRef<IDoiTuongThucHienRef>(null);

  const [showDialogOptions, setShowDialogOptions] = useState<boolean>(false);
  const [currentKeyBts, setCurrentKeyBts] = useState<
    EBtsInformationUserOptions | undefined
  >(undefined);
  const btsRef = useRef<submitScreenRef | null>(null);
  const [currentOptionsBtsValue, setCurrentOptionsBtsValue] = useState<
    Record<EBtsInformationUserOptions, AnyObj>
  >({} as Record<EBtsInformationUserOptions, AnyObj>);

  const [currentDoiTuongThucHien, setCurrentDoiTuongThucHien] =
    useState<IOption>(IDoiTuongThucHienOptions[0]);

  useEffect(() => {
    if (choseCommonProcedureItem) {
      setTitle(choseCommonProcedureItem?.procedureName || '');
    }
  }, [choseCommonProcedureItem]);

  useEffect(() => {
    if (!currentKeyBts) {
      return;
    }
    if (
      BtsSelect.includes(currentKeyBts) &&
      btsMainRef.current &&
      !showDialogOptions
    ) {
      btsMainRef.current?.expand();
      setShowDialogOptions(true);
    }
  }, [currentKeyBts, btsMainRef, showDialogOptions]);

  const handleNext = () => {
    if (currentStep >= optionSteps.length) {
      return;
    }
    if (currentStep === 1 && !eFormData) {
      setCurrentStep(currentStep + 2);
      return;
    }
    setCurrentStep(currentStep + 1);
  };
  const handlePrev = () => {
    if (currentStep <= 1) {
      return;
    }
    if (currentStep === 3 && !eFormData) {
      setCurrentStep(currentStep - 2);
      return;
    }
    setCurrentStep(currentStep - 1);
  };

  const onHandleChangeDialogStart = (index: number) => {
    if (index === -1) {
      return setShowDialogOptions(false);
    }
  };

  const handleOptionBts = (key: EBtsInformationUserOptions) => {
    setCurrentKeyBts(key);
  };

  const handleChangeValue = ({
    key,
    value,
  }: {
    key: EBtsInformationUserOptions;
    value: AnyObj;
  }) => {
    infoUserRef.current?.setFormData({key, value});
    if (btsMainRef?.current && showDialogOptions) {
      btsMainRef.current?.close();
      setCurrentKeyBts(undefined);
      setShowDialogOptions(false);
    }
  };

  const handleCloseDialogStart = useCallback(() => {
    if (btsMainRef.current && showDialogOptions) {
      btsMainRef.current?.close();
      setCurrentKeyBts(undefined);
      setShowDialogOptions(false);
      return;
    }
  }, [btsMainRef, showDialogOptions]);

  const handleSubmitInformationUser = () => {
    if (infoUserRef?.current) {
      infoUserRef?.current?.submitForm();
    }
  };

  const handleNextStep = () => {
    if (currentStep === 1) {
      handleSubmitInformationUser();
    } else if (currentStep === 2) {
      handleViewPdf()
    } else {
      handleNext();
    }
  };

  const handleChangeDoiTuongThucHien = ({
    key,
    value,
  }: {
    key: EBtsInformationUserOptions;
    value: AnyObj;
  }) => {
    if (key === EBtsInformationUserOptions.DOITUONGTHUCHIEN) {
      setCurrentDoiTuongThucHien(value as IOption);
      setCurrentKeyBts(EBtsInformationUserOptions.DOITUONG);
      return;
    }
  };

  const snapPoint = React.useMemo(() => {
    if (
      currentKeyBts === EBtsInformationUserOptions.DOITUONG &&
      (currentDoiTuongThucHien?.value === 'canhan' ||
        currentDoiTuongThucHien?.value === 'doanhnghiep')
    ) {
      return '70%';
    }
    return '50%';
  }, [currentKeyBts, currentDoiTuongThucHien]);

  const handleSubmitFormDT = () => {
    handleCloseDialogStart();
    handleNext();
  };

  const handleViewPdf = () => {
    if (infoUserRef?.current) {
      infoUserRef?.current?.submitForm();
    }
  };

  return (
    <View style={{flex: 1}}>
      <Animated.View style={[styles.headerZIndex, headerStyle]}>
        <HeaderCommonProcedure title={title} />
      </Animated.View>
      <View style={styles.stepMenuVertical}>
        <StepMenuVertical currentStep={currentStep} options={optionSteps} />
      </View>
      <Animated.ScrollView
        onScroll={scrollHandler}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.content}>
        <View style={styles.container}>
          {(currentStep === 1 && (
            <InformationUserScreen
              ref={infoUserRef}
              onHandleOptionBts={handleOptionBts}
              onHandleChangeValueForm={(value: AnyObj) => {
                setCurrentOptionsBtsValue(value);
              }}
              onHandNextStep={() => {
                setCurrentKeyBts(EBtsInformationUserOptions.DOITUONG);
              }}
            />
          )) ||
            (currentStep === 2 && <EFormScreen />) ||
            (currentStep === 3 && <UploadFileScreen />)}
        </View>
      </Animated.ScrollView>
      <AcButtonActionFooter
        onHandleNext={handleNextStep}
        onHandlePrevious={handlePrev}
      />
      {showDialogOptions && (
        <TouchableWithoutFeedback onPress={handleCloseDialogStart}>
          <View style={styles.overlayDialogStart} />
        </TouchableWithoutFeedback>
      )}
      <BtsScrollView
        snapPoints={[snapPoint]}
        refProp={btsMainRef}
        onHandleChange={onHandleChangeDialogStart}
        title={
          (currentKeyBts === EBtsInformationUserOptions.DOITUONG && (
            <View style={styles.header}>
              <Text className={textStyles.h6}>Chọn vai trò thực hiện</Text>
              <Text className={textStyles.body2} style={styles.subHeader}>
                Bạn muốn thao tác với tư cách nào? Làm cho bản thân hay khai hộ
                cho người thân?
              </Text>
            </View>
          )) ||
          (currentKeyBts === EBtsInformationUserOptions.DOITUONGTHUCHIEN && (
            <View style={styles.header}>
              <Text className={textStyles.h6}>Vai trò thực hiện</Text>
            </View>
          )) ||
          null
        }
        footer={
          <Button
            mode="contained"
            style={styles.button}
            onPress={() => {
              // console.log({
              //   currentKeyBts,
              //   doiTuongThucHienRef: doiTuongThucHienRef.current,
              // });
              if (
                (currentKeyBts === EBtsInformationUserOptions.DOITUONG ||
                  currentKeyBts ===
                    EBtsInformationUserOptions.DOITUONGTHUCHIEN) &&
                doiTuongThucHienRef?.current
              ) {
                doiTuongThucHienRef.current?.submitForm();
                return;
              } else if (btsRef.current) {
                btsRef.current?.submit();
                return;
              }
            }}>
            {currentKeyBts === EBtsInformationUserOptions.DOITUONGTHUCHIEN
              ? 'Hoàn tất'
              : 'Xác nhận'}
          </Button>
        }>
        <View>
          {(currentKeyBts === EBtsInformationUserOptions.SEX && (
            <BtsSexComponent
              currentOptionsBtsValue={currentOptionsBtsValue}
              ref={btsRef}
              onHandleChange={handleChangeValue}
            />
          )) ||
            (currentKeyBts === EBtsInformationUserOptions.NATIONALITY && (
              <BtsOptionsComponent
                formName={EBtsInformationUserOptions.NATIONALITY}
                options={ENationalityOptions}
                currentOptionsBtsValue={currentOptionsBtsValue}
                ref={btsRef}
                onHandleChange={handleChangeValue}
              />
            )) ||
            (currentKeyBts === EBtsInformationUserOptions.DT && (
              <BtsOptionsComponent
                formName={EBtsInformationUserOptions.DT}
                options={EDTOptions}
                currentOptionsBtsValue={currentOptionsBtsValue}
                ref={btsRef}
                onHandleChange={handleChangeValue}
              />
            )) ||
            (currentKeyBts === EBtsInformationUserOptions.TG && (
              <BtsOptionsComponent
                formName={EBtsInformationUserOptions.TG}
                options={ETGOptions}
                currentOptionsBtsValue={currentOptionsBtsValue}
                ref={btsRef}
                onHandleChange={handleChangeValue}
              />
            )) ||
            (currentKeyBts === EBtsInformationUserOptions.DocumentType && (
              <BtsOptionsComponent
                formName={EBtsInformationUserOptions.DocumentType}
                options={EDocumentType}
                currentOptionsBtsValue={currentOptionsBtsValue}
                ref={btsRef}
                onHandleChange={handleChangeValue}
              />
            )) ||
            (currentKeyBts === EBtsInformationUserOptions.NC && (
              <BtsOptionInputComponent
                formName={EBtsInformationUserOptions.NC}
                options={ENcOptions}
                currentOptionsBtsValue={currentOptionsBtsValue}
                ref={btsRef}
                onHandleChange={handleChangeValue}
              />
            )) ||
            (currentKeyBts === EBtsInformationUserOptions.DOITUONG && (
              <BtsMainDT
                ref={doiTuongThucHienRef}
                currentDoiTuongThucHien={currentDoiTuongThucHien}
                onHandleOptionBts={(value: EBtsInformationUserOptions) =>
                  handleOptionBts(value)
                }
                onHandleSubmitForm={handleSubmitFormDT}
              />
            )) ||
            (currentKeyBts === EBtsInformationUserOptions.DOITUONGTHUCHIEN && (
              <BtsDoiTuongThucHienOptions
                ref={doiTuongThucHienRef}
                formName={EBtsInformationUserOptions.DOITUONGTHUCHIEN}
                options={IDoiTuongThucHienOptions}
                currentDoiTuongThucHien={currentDoiTuongThucHien}
                onHandleChange={({
                  key,
                  value,
                }: {
                  key: EBtsInformationUserOptions;
                  value: AnyObj;
                }) => handleChangeDoiTuongThucHien({key, value})}
              />
            )) ||
            null}
        </View>
      </BtsScrollView>
    </View>
  );
});

const styles = StyleSheet.create({
  headerZIndex: {
    zIndex: 999,
  },
  content: {
    flexGrow: 1,
    paddingBottom: FOOTER_HEIGHT,
    zIndex: 999,
  },
  stepMenuVertical: {
    marginTop: 4,
    marginBottom: 4,
  },
  container: {
    display: 'flex',
    flexDirection: 'column',
    gap: 4,
  },
  overlayDialogStart: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: FOOTER_HEIGHT,
    backgroundColor: '#ffffff',
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: -2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  button: {
    borderRadius: 8,
  },
  header: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  subHeader: {
    textAlign: 'center',
    marginTop: 4,
  },
});
