import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {useCommonProcedure} from '../../../../stores';
import {textStyles} from '../../../../styles/QNH_textStyle';
import {SelectedBottomSheet} from '../../../../components/AcCard/SelectedBottomSheet';
import {AnyObj} from '@ac-mobile/common';

export const UploadFileScreen = () => {
  const {inputsData} = useCommonProcedure();

  console.log({inputsData});

  const handleOpenBottomSheet = (code: string) => {
    console.log({code});
  };

  const handleRemoveFile = (code: string) => {
    console.log({code});
  };

  return (
    <View style={styles.content}>
      <View>
        <Text className={`${textStyles.h6}`}>Thành phần giấy tờ</Text>
      </View>
      <View style={styles.formContent}>
        {inputsData.map((item: AnyObj, index: number) => (
          <View key={index}>
            <SelectedBottomSheet
              name={item.tenHoSoKemTheo}
              fileName={item.fileName}
              code={item.maHoSoKemTheo}
              onOpenBottomSheet={(code: string) => handleOpenBottomSheet(code)}
              onRemoveFile={(code: string) => handleRemoveFile(code)}
            />
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  content: {
    flexGrow: 1,
    display: 'flex',
    flexDirection: 'column',
    gap: 24,
    padding: 16,
    backgroundColor: '#fff',
  },
  formContent: {
    display: 'flex',
    flexDirection: 'column',
    gap: 24,
  },
});
