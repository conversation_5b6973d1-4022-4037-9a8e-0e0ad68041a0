import React, {useRef} from 'react';
import {View, SafeAreaView, StyleSheet, Dimensions, Text} from 'react-native';
import WebView from 'react-native-webview';
import {WebViewPdfHelper} from '../../../../types/webViewPdfHelper';
import {useCommonProcedure} from '../../../../stores';

// 👇 Giả sử FOOTER_HEIGHT là 80
const FOOTER_HEIGHT = 80;

export const EFormScreen = () => {
  const {eFormUrl} = useCommonProcedure();
  const webViewRef = useRef<WebView>(null);

  const onMessage = (event: any) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      console.log('📥 Received from WebView:', data);

      if (data.type === 'html') {
        console.log('📄 HTML Content:', data.data);
      } else if (data.type === 'meta') {
        console.log('📑 Meta Data:', data.data);
      } else if (data.type === 'mode') {
        console.log('🎯 Current Mode:', data.data);
      }
    } catch (err) {
      console.error('❌ Error parsing WebView data:', err);
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      {eFormUrl ? (
        <>
          <WebView
            ref={webViewRef}
            source={{uri: eFormUrl}}
            style={styles.webview}
            javaScriptEnabled
            domStorageEnabled
            onMessage={onMessage}
            injectedJavaScript={`
              (function() {
                window.addEventListener('message', function(event) {
                  const cmd = event.data;
                  if (cmd === 'get-html') {
                    const html = document.documentElement.outerHTML;
                    window.ReactNativeWebView.postMessage(JSON.stringify({type: 'html', data: html}));
                  } else if (cmd === 'get-meta-data') {
                    const meta = Array.from(document.getElementsByTagName('meta')).map(m => m.outerHTML);
                    window.ReactNativeWebView.postMessage(JSON.stringify({type: 'meta', data: meta}));
                  } else if (cmd === 'edit-mode' || cmd === 'sign-mode') {
                    document.body.setAttribute('data-mode', cmd);
                    window.ReactNativeWebView.postMessage(JSON.stringify({type: 'mode', data: cmd}));
                  }
                });
                console.log('✅ WebView JS injected');
              })();
              true;
            `}
          />
        </>
      ) : (
        <View style={styles.fallback}>
          <Text>Không có URL EForm</Text>
        </View>
      )}
    </SafeAreaView>
  );
};

const {height: SCREEN_HEIGHT} = Dimensions.get('window');

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  webview: {
    height: SCREEN_HEIGHT - FOOTER_HEIGHT, // 👈 Auto trừ chiều cao FOOTER
  },
  footer: {
    height: FOOTER_HEIGHT,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f1f1f1',
    borderTopWidth: 1,
    borderTopColor: '#ddd',
  },
  footerText: {
    fontSize: 16,
    color: '#333',
  },
  fallback: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
