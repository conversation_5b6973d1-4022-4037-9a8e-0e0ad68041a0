import {AnyObj} from '@ac-mobile/common';
import React, {forwardRef, useImperativeHandle} from 'react';
import {
  EBtsInformationUserOptions,
  IOption,
  submitScreenRef,
} from '../../../../../types';
import {SelectCustomOption} from '../../../../../components/RHFCustomComponent';
import {
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import {textStyles} from '../../../../../styles/QNH_textStyle';
import IoniconsIcon from 'react-native-vector-icons/Ionicons';

type props = {
  formName: EBtsInformationUserOptions;
  options: IOption[];
  currentOptionsBtsValue: Record<EBtsInformationUserOptions, AnyObj>;
  selectedItemOption?: SelectCustomOption;
  onHandleChange: ({
    key,
    value,
  }: {
    key: EBtsInformationUserOptions;
    value: AnyObj;
  }) => void;
};

export const BtsOptionInputComponent = React.memo(
  forwardRef<submitScreenRef, props>(
    (
      {
        formName,
        options,
        currentOptionsBtsValue,
        selectedItemOption,
        onHandleChange,
      },
      ref,
    ) => {
      const [selectedItem, setSelectedItem] = React.useState<
        IOption | undefined
      >(undefined);
      const [sexOptions, setSexOptions] = React.useState<IOption[]>([]);
      const [filteredOptions, setFilteredOptions] = React.useState<IOption[]>(
        [],
      );
      const [searchText, setSearchText] = React.useState<string>('');

      const handleInit = () => {
        if (options.length <= 0) {
          setSexOptions([]);
          return;
        }
        const formatOptionData = options.map(
          (item: IOption, index: number) => ({
            id: index,
            label: item.label,
            value: item.value,
          }),
        );

        const currentValueExisted = currentOptionsBtsValue[formName];
        if (!currentValueExisted) {
          setSexOptions(formatOptionData);
          setFilteredOptions(formatOptionData);
          return;
        }

        const optionMatched = formatOptionData.find(
          item => item.id === currentValueExisted.id,
        );

        if (!optionMatched) {
          const newOption: IOption = {
            id: currentValueExisted?.id || Date.now(), // Use timestamp as unique id
            label: currentValueExisted?.label || '',
            value: currentValueExisted?.value || '',
          };
          setSelectedItem(newOption);
          setSexOptions([newOption, ...formatOptionData]);
          setFilteredOptions([newOption, ...formatOptionData]);
          return;
        }
        setSelectedItem(optionMatched);
        if (optionMatched) {
          setSexOptions(formatOptionData);
          setFilteredOptions(formatOptionData);
          return;
        }
      };

      React.useEffect(() => {
        handleInit();
        // eslint-disable-next-line react-hooks/exhaustive-deps
      }, []);

      React.useEffect(() => {
        if (selectedItemOption) {
          setSelectedItem(selectedItemOption);
        }
      }, [selectedItemOption]);

      React.useEffect(() => {
        if (searchText.trim() === '') {
          setFilteredOptions(sexOptions);
        } else {
          const filtered = sexOptions.filter(option =>
            option.label.toLowerCase().includes(searchText.toLowerCase()),
          );
          setFilteredOptions(filtered);
        }
      }, [searchText, sexOptions]);

      const handleSelectItem = (item: IOption) => {
        setSelectedItem(item);
        setSearchText(''); // Reset search text after selecting
      };

      const handleAddNewItem = () => {
        if (!searchText.trim()) {
          return;
        }

        // Create new option
        const newOption: IOption = {
          id: Date.now(), // Use timestamp as unique id
          label: searchText.trim(),
          value: searchText.trim(),
        };

        // Append to sexOptions and set as selected
        const updatedOptions = [newOption, ...sexOptions];
        setSexOptions(updatedOptions);
        setFilteredOptions(updatedOptions);
        handleSelectItem(newOption);
      };

      useImperativeHandle(ref, () => ({
        submit: () =>
          onHandleChange({
            key: formName,
            value: selectedItem,
          }),
      }));

      return (
        <View>
          {/* Search Input */}
          <View style={styles.searchContainer}>
            <IoniconsIcon
              name="search"
              size={20}
              color="#888"
              style={styles.icon}
            />
            <TextInput
              style={styles.searchInput}
              placeholder="Tìm kiếm hoặc thêm mới..."
              value={searchText}
              onChangeText={setSearchText}
              onSubmitEditing={handleAddNewItem}
              returnKeyType="done"
            />
          </View>

          {/* Options List */}
          {filteredOptions.map((item: IOption, index: number) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.item,
                selectedItem?.id === item.id && styles.selectedItem,
              ]}
              onPress={() => handleSelectItem(item)}>
              <View style={styles.itemContent}>
                {selectedItem?.id === item.id && (
                  <IoniconsIcon
                    name="checkmark-circle"
                    size={24}
                    color="red"
                    style={styles.icon}
                  />
                )}
                <View>
                  <Text className={textStyles.subTitle2}>{item.label}</Text>
                </View>
              </View>
            </TouchableOpacity>
          ))}

          {/* If no result, show Add New Button */}
          {filteredOptions.length === 0 && !!searchText.trim() && (
            <TouchableOpacity
              style={[styles.item, styles.addNewItem]}
              onPress={handleAddNewItem}>
              <View style={styles.itemContent}>
                <IoniconsIcon
                  name="add-circle-outline"
                  size={24}
                  color="#007AFF"
                  style={styles.icon}
                />
                <Text className={textStyles.subTitle2}>
                  Thêm "{searchText.trim()}"
                </Text>
              </View>
            </TouchableOpacity>
          )}
        </View>
      );
    },
  ),
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    backgroundColor: '#fff',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#B0B0B0',
    borderRadius: 8,
    paddingHorizontal: 14,
    marginBottom: 12,
    backgroundColor: '#FFFFFF',
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
    color: '#333',
  },
  icon: {
    marginRight: 8,
  },
  item: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    backgroundColor: '#f9f9f9',
  },
  selectedItem: {
    backgroundColor: '#e0e0e0',
  },
  addNewItem: {
    backgroundColor: '#f0f8ff',
  },
  itemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});
