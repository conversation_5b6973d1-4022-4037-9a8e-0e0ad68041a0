import {AnyObj} from '@ac-mobile/common';
import React, {forwardRef, useImperativeHandle} from 'react';
import {
  EBtsInformationUserOptions,
  IOption,
  submitScreenRef,
} from '../../../../../types';
import {SelectCustomOption} from '../../../../../components/RHFCustomComponent';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {textStyles} from '../../../../../styles/QNH_textStyle';
import IoniconsIcon from 'react-native-vector-icons/Ionicons';

type props = {
  formName: EBtsInformationUserOptions;
  options: IOption[];
  currentOptionsBtsValue: Record<EBtsInformationUserOptions, AnyObj>;
  selectedItemOption?: SelectCustomOption;
  onHandleChange: ({
    key,
    value,
  }: {
    key: EBtsInformationUserOptions;
    value: AnyObj;
  }) => void;
};

export const BtsOptionsComponent = React.memo(
  forwardRef<submitScreenRef, props>(
    (
      {
        formName,
        options,
        currentOptionsBtsValue,
        selectedItemOption,
        onHandleChange,
      },
      ref,
    ) => {
      const [selectedItem, setSelectedItem] = React.useState<
        IOption | undefined
      >(undefined);
      const [sexOptions, setSexOptions] = React.useState<IOption[]>([]);

      const handleInit = async () => {
        if (options.length < 0) {
          setSexOptions([]);
          return;
        }
        const formatOptionData = await options.map(
          (item: IOption, index: number) => {
            return {
              id: index,
              label: item.label,
              value: item.value,
            };
          },
        );

        const currentValueExisted = currentOptionsBtsValue[formName];
        if (!currentValueExisted) {
          setSexOptions(formatOptionData);
          return;
        }
        const optionMatched = formatOptionData.find(
          item => item.id === currentValueExisted.id,
        );
        if (optionMatched) {
          setSelectedItem(optionMatched);
        }
        setSexOptions(formatOptionData);
      };

      React.useEffect(() => {
        handleInit();
        // eslint-disable-next-line react-hooks/exhaustive-deps
      }, []);

      React.useEffect(() => {
        if (selectedItemOption) {
          setSelectedItem(selectedItemOption);
        }
      }, [selectedItemOption]);

      useImperativeHandle(ref, () => ({
        submit: () =>
          onHandleChange({
            key: formName,
            value: selectedItem,
          }),
      }));

      return (
        <View>
          {sexOptions.map((item: IOption, index: number) => {
            return (
              <TouchableOpacity
                key={index}
                style={[
                  styles.item,
                  selectedItem?.id === item.id && styles.selectedItem,
                ]}
                onPress={
                  () => setSelectedItem(item)
                  // onHandleChange({key: EBtsInformationUserOptions.SEX, value: item})
                }>
                <View style={styles.itemContent}>
                  {selectedItem?.id === item.id && (
                    <IoniconsIcon
                      name="checkmark-circle"
                      size={24}
                      color="red"
                      style={styles.icon}
                    />
                  )}
                  <View>
                    <Text className={`${textStyles.subTitle2}`}>
                      {item.label}
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            );
          })}
        </View>
      );
    },
  ),
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    backgroundColor: '#fff',
  },
  list: {
    paddingVertical: 16,
  },
  contentContainerStyle: {paddingBottom: 16},
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#B0B0B0',
    borderRadius: 8,
    paddingHorizontal: 14,
    backgroundColor: '#FFFFFF',
  },
  icon: {
    marginRight: 8,
  },
  item: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  selectedItem: {
    backgroundColor: '#f0f0f0',
  },
  itemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingBottom: 24,
  },
  button: {
    borderRadius: 8,
  },
  contentBottomSheet: {
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
});
