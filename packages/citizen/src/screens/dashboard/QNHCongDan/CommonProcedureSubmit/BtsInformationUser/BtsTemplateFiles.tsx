import React, {forwardRef, useImperativeHandle} from 'react';
import {StyleSheet, TextInput, View} from 'react-native';
import {EBtsInformationUserOptions, IOption} from '../../../../../types';
import {textStyles} from '../../../../../styles/QNH_textStyle';
import {AnyObj} from '@ac-mobile/common';
import {BtsFlatList} from '../../../../../components';
import {BottomSheetMethods} from '@gorhom/bottom-sheet/lib/typescript/types';
import FeatherIcon from 'react-native-vector-icons/Feather';

type props = {
  refProp: React.RefObject<BottomSheetMethods>;
  formName: EBtsInformationUserOptions;
  options: IOption[];
  currentDoiTuongThucHien: IOption;
  onHandleChange: ({
    key,
    value,
  }: {
    key: EBtsInformationUserOptions;
    value: AnyObj;
  }) => void;
};

export type IFiles = {
  onHandleChangeValue: () => void;
};

export const BtsTemplateFiles = React.memo(
  forwardRef<IFiles, props>(
    (
      {refProp, formName, options, currentDoiTuongThucHien, onHandleChange},
      ref,
    ) => {
      useImperativeHandle(ref, () => ({
        onHandleChangeValue: () => {
          onHandleChange({
            key: formName,
            value: selectedItem,
          });
        },
      }));

      const [selectedItem, setSelectedItem] = React.useState<
        AnyObj | undefined
      >(undefined);

      const [data, setDate] = React.useState<AnyObj[]>([]);
      const [textSearch, setTextSearch] = React.useState('');

      return (
        <BtsFlatList
          refProp={refProp}
          snapPoints={['70%']}
          onHandleChange={(index: number) => {
            // onHandleChange && onHandleChange(index);
          }}>
          <View style={styles.searchContainer}>
            <FeatherIcon
              name="search"
              size={24}
              color="#B0B0B0"
              style={styles.icon}
            />
            <TextInput
              className={`${textStyles.body1}`}
              placeholder="Tìm kiếm giấy tờ"
              placeholderTextColor="#B0B0B0"
              value={textSearch}
              onChangeText={setTextSearch}
            />
          </View>
        </BtsFlatList>
      );
    },
  ),
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    backgroundColor: '#fff',
  },
  list: {
    paddingVertical: 16,
  },
  contentContainerStyle: {paddingBottom: 16},
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#B0B0B0',
    borderRadius: 8,
    paddingHorizontal: 14,
    backgroundColor: '#FFFFFF',
  },
  icon: {
    marginRight: 8,
  },
});
