import {AnyObj} from '@ac-mobile/common';
import React, {forwardRef, useImperativeHandle} from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {
  EBtsInformationUserOptions,
  ESexOptions,
  submitScreenRef,
} from '../../../../../types';
import {SelectCustomOption} from '../../../../../components/RHFCustomComponent';
import {textStyles} from '../../../../../styles/QNH_textStyle';
import IoniconsIcon from 'react-native-vector-icons/Ionicons';

type props = {
  currentOptionsBtsValue: Record<EBtsInformationUserOptions, AnyObj>;
  selectedItemOption?: SelectCustomOption;
  onHandleChange: ({
    key,
    value,
  }: {
    key: EBtsInformationUserOptions;
    value: AnyObj;
  }) => void;
};

export const BtsSexComponent = React.memo(
  forwardRef<submitScreenRef, props>(
    ({currentOptionsBtsValue, selectedItemOption, onHandleChange}, ref) => {
      const [selectedItem, setSelectedItem] = React.useState<
        SelectCustomOption | undefined
      >(undefined);
      const [sexOptions, setSexOptions] = React.useState<SelectCustomOption[]>(
        [],
      );

      const handleInit = async () => {
        if (ESexOptions.length < 0) {
          setSexOptions([]);
          return;
        }
        const options = await ESexOptions.map((item: AnyObj, index: number) => {
          return {
            id: index,
            label: item.label,
            value: item,
          };
        });

        const currentValueExisted =
          currentOptionsBtsValue[EBtsInformationUserOptions.SEX];
        if (!currentValueExisted) {
          setSexOptions(options);
          return;
        }
        const optionMatched = options.find(
          item => item.id === currentValueExisted.id,
        );
        if (optionMatched) {
          setSelectedItem(optionMatched);
        }
        setSexOptions(options);
      };

      React.useEffect(() => {
        handleInit();
        // eslint-disable-next-line react-hooks/exhaustive-deps
      }, []);

      React.useEffect(() => {
        if (selectedItemOption) {
          setSelectedItem(selectedItemOption);
        }
      }, [selectedItemOption]);

      useImperativeHandle(ref, () => ({
        submit: () =>
          onHandleChange({
            key: EBtsInformationUserOptions.SEX,
            value: selectedItem,
          }),
      }));

      return (
        <View>
          {sexOptions.map((item: SelectCustomOption, index: number) => {
            return (
              <TouchableOpacity
                key={index}
                style={[
                  styles.item,
                  selectedItem?.id === item.id && styles.selectedItem,
                ]}
                onPress={
                  () => setSelectedItem(item)
                  // onHandleChange({key: EBtsInformationUserOptions.SEX, value: item})
                }>
                <View style={styles.itemContent}>
                  {selectedItem?.id === item.id && (
                    <IoniconsIcon
                      name="checkmark-circle"
                      size={24}
                      color="red"
                      style={styles.icon}
                    />
                  )}
                  <View>
                    <Text className={`${textStyles.subTitle2}`}>
                      {item.label}
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            );
          })}
        </View>
      );
    },
  ),
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    backgroundColor: '#fff',
  },
  list: {
    paddingVertical: 16,
  },
  contentContainerStyle: {paddingBottom: 16},
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#B0B0B0',
    borderRadius: 8,
    paddingHorizontal: 14,
    backgroundColor: '#FFFFFF',
  },
  icon: {
    marginRight: 8,
  },
  item: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  selectedItem: {
    backgroundColor: '#f0f0f0',
  },
  itemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingBottom: 24,
  },
  button: {
    borderRadius: 8,
  },
  contentBottomSheet: {
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
});
