import React, {forwardRef, useCallback, useImperativeHandle} from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {EBtsInformationUserOptions, IOption} from '../../../../../types';
import {AnyObj} from '@ac-mobile/common';
import IoniconsIcon from 'react-native-vector-icons/Ionicons';
import {textStyles} from '../../../../../styles/QNH_textStyle';
import {IDoiTuongThucHienRef} from './BtsMainDT';

type props = {
  formName: EBtsInformationUserOptions;
  options: IOption[];
  currentDoiTuongThucHien: IOption;
  onHandleChange: ({
    key,
    value,
  }: {
    key: EBtsInformationUserOptions;
    value: AnyObj;
  }) => void;
};

export const BtsDoiTuongThucHienOptions = React.memo(
  forwardRef<IDoiTuongThucHienRef, props>(
    ({formName, options, currentDoiTuongThucHien, onHandleChange}, ref) => {
      useImperativeHandle(ref, () => ({
        submitForm: () => {
          console.log({formName, selectedItem});
          onHandleChange({
            key: formName,
            value: selectedItem,
          });
        },
      }));

      const [selectedItem, setSelectedItem] = React.useState<
        IOption | undefined
      >(undefined);
      const [optionData, setOptionData] = React.useState<IOption[]>([]);

      const handleInit = useCallback(() => {
        console.log({currentDoiTuongThucHien});
        if (options.length <= 0 && !currentDoiTuongThucHien) {
          setOptionData([]);
          return;
        }
        const formatOptionData = options.map(
          (item: IOption, index: number) => ({
            id: index,
            label: item.label,
            value: item.value,
          }),
        );
        if (!currentDoiTuongThucHien) {
          setOptionData(formatOptionData);
          return;
        }
        const currentDoiTuongThucHienExisted = formatOptionData.find(
          (item: IOption) =>
            item?.value?.value === currentDoiTuongThucHien?.value?.value,
        );

        setSelectedItem(currentDoiTuongThucHienExisted);
        setOptionData(formatOptionData);
      }, [currentDoiTuongThucHien, options]);

      React.useEffect(() => {
        handleInit();
        // eslint-disable-next-line react-hooks/exhaustive-deps
      }, []);

      return (
        <View>
          {optionData.map((item: IOption, index: number) => {
            return (
              <TouchableOpacity
                key={index}
                style={[
                  styles.item,
                  selectedItem?.id === item.id && styles.selectedItem,
                ]}
                onPress={
                  () => setSelectedItem(item)
                  // onHandleChange({key: EBtsInformationUserOptions.SEX, value: item})
                }>
                <View style={styles.itemContent}>
                  {selectedItem?.id === item.id && (
                    <IoniconsIcon
                      name="checkmark-circle"
                      size={24}
                      color="red"
                      style={styles.icon}
                    />
                  )}
                  <View>
                    <Text className={`${textStyles.subTitle2}`}>
                      {item.label}
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            );
          })}
        </View>
      );
    },
  ),
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    backgroundColor: '#fff',
  },
  icon: {
    marginRight: 8,
  },
  item: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    backgroundColor: '#f9f9f9',
  },
  selectedItem: {
    backgroundColor: '#e0e0e0',
  },
  addNewItem: {
    backgroundColor: '#f0f8ff',
  },
  itemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});
