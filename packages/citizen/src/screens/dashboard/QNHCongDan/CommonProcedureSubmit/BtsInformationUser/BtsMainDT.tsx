import React, {forwardRef, useImperativeHandle} from 'react';
import {EBtsInformationUserOptions, IOption} from '../../../../../types';
import {AnyObj} from '@ac-mobile/common';
import {StyleSheet, View} from 'react-native';
import {
  FormProvider,
  RHFSelected,
  RHFTextField,
} from '../../../../../components/RHFCustomComponent';
import * as Yup from 'yup';
import {useForm} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import moment from 'moment';

type props = {
  currentDoiTuongThucHien: IOption;
  onHandleOptionBts: (key: EBtsInformationUserOptions) => void;
  onHandleChange?: ({
    key,
    value,
  }: {
    key: EBtsInformationUserOptions;
    value: AnyObj;
  }) => void;
  onHandleSubmitForm: () => void;
};

export type IDoiTuongThucHienRef = {
  submitForm: () => void;
};

export const BtsMainDT = React.memo(
  forwardRef<IDoiTuongThucHienRef, props>(
    ({currentDoiTuongThucHien, onHandleOptionBts, onHandleSubmitForm}, ref) => {
      useImperativeHandle(ref, () => ({
        submitForm: () => handleSubmitFormData(),
      }));

      const {defaultValue, defaultSchema} = React.useMemo(() => {
        const value = {} as AnyObj;
        const schema = {} as AnyObj;

        // if (currentDoiTuongThucHien) {
        //   schema['DoiTuongThucHien'] = Yup.object()
        //     .shape({
        //       id: Yup.number(),
        //       label: Yup.string(),
        //       value: Yup.object(),
        //     })
        //     .required(`Đối tượng thực hiện không được để trống`);
        //   value['DoiTuongThucHien'] = {
        //     id: Date.now(),
        //     label: currentDoiTuongThucHien.label,
        //     value: currentDoiTuongThucHien.value,
        //   };
        // }
        schema['DoiTuongThucHien'] = Yup.object()
          .shape({
            id: Yup.number(),
            label: Yup.string(),
            value: Yup.string(),
          })
          .required(`Đối tượng thực hiện không được để trống`);
        value['DoiTuongThucHien'] = {
          id: Date.now(),
          label: currentDoiTuongThucHien.label,
          value: currentDoiTuongThucHien.value,
        };

        if (currentDoiTuongThucHien.value === 'canhan') {
          schema['tenNguoiUyQuyen'] = Yup.string().required(
            'Tên người ủy quyền không được để trống',
          );
          schema['ngaySinh'] = Yup.date()
            .max(
              moment(new Date()).add(1, 'minute').format(),
              'Ngày sinh phải nhỏ hơn thời gian hiện tại',
            )
            .required('Ngày sinh không được để trống');
          schema['soGiayTo'] = Yup.string().required(
            'Số giấy tờ không được để trống',
          );
          schema['quanHe'] = Yup.string().required(
            'Quan hệ với người ủy quyền không được để trống',
          );
          schema['taiLieuUyQuyen'] = Yup.object()
            .shape({
              name: Yup.string().required(
                'Tài liệu đính kèm không được để trống',
              ),
              uri: Yup.string(),
              isSigned: Yup.boolean(),
              ownerId: Yup.string(),
              issuerId: Yup.string(),
            })
            .nullable();
        }

        return {defaultValue: value, defaultSchema: schema};
      }, [currentDoiTuongThucHien]);

      const methods = useForm({
        mode: 'onChange',
        resolver: yupResolver(Yup.object().shape({...defaultSchema})),
        defaultValues: defaultValue,
      });

      const {
        handleSubmit,
        setError,
        setValue,
        clearErrors,
        formState: {errors},
      } = methods;

      const handleSubmitFormData = handleSubmit(async (data, e) => {
        e?.preventDefault?.();
        try {
          console.log({data});
          onHandleSubmitForm();
        } catch (error) {
          console.log(error);
        }
      });

      return (
        <View style={styles.content}>
          <FormProvider methods={methods}>
            <RHFSelected
              name="DoiTuongThucHien"
              required={true}
              label="Vai trò thực hiện"
              onHandleShowOptions={() =>
                onHandleOptionBts(EBtsInformationUserOptions.DOITUONGTHUCHIEN)
              }
              placeholder="Chọn vai trò"
            />
            {currentDoiTuongThucHien.value === 'canhan' && (
              <>
                <RHFTextField
                  name="tenNguoiUyQuyen"
                  required={true}
                  label="Họ và tên người uỷ quyền"
                  placeholder="Nhập họ và tên người uỷ quyền"
                />
                <RHFTextField
                  name="soGiayTo"
                  required={true}
                  label="Số giấy tờ của người ủy quyền"
                  placeholder="Nhập số giấy tờ"
                />
                <RHFTextField
                  name="quanHe"
                  required={true}
                  label="Mối quan hệ với người ủy quyền"
                  placeholder="Ví dụ: Vợ/chồng"
                />
              </>
            )}
          </FormProvider>
        </View>
      );
    },
  ),
);

const styles = StyleSheet.create({
  content: {
    flex: 1, // Full height for bottom sheet
    backgroundColor: '#fff',
  },
  formContainer: {
    paddingHorizontal: 16,
    paddingBottom: 32, // Add extra space for keyboard
  },
});
