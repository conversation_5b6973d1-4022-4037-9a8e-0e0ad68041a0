import * as React from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import Animated, {
  useAnimatedScrollHandler,
  useSharedValue,
  useAnimatedStyle,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {Menu} from 'react-native-paper';
import {MainContent} from '../../../components/QNH_CongDanComponents/HomeComponent';
import {useUserStore} from '../../../stores/user.store';
import {BottomSheetModalProvider} from '@gorhom/bottom-sheet';
import {useConfigStore} from '@ac-mobile/common';
import {useAuthStore} from '@ac-mobile/common';
import DeviceInfo from 'react-native-device-info';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {useTheme} from 'react-native-paper';
import {useQueryClient} from 'react-query';
import {USE_REQUESTER_BIZ_TEMPLATES_KEY} from '../../../hooks/UseRequesterBizTemplates';
import {useDrawNumberStore} from '../../../stores/drawNumber.store';
import {useNavigation} from '@react-navigation/native';
import {USE_REQUESTER_BIZ_SHARES_KEY} from '../../../hooks/useShareProfiles';
import {USE_REQUESTER_BIZ_DOCUMENTS_KEY} from '../../../hooks/useDocuments';

const HEADER_HEIGHT = 80;
const BG_FADE_DISTANCE = 60; // px to fade in background

const DashboardHomeScreen = (props: any) => {
  const theme = useTheme();
  const navigation = useNavigation();
  const scrollY = useSharedValue(0);
  const {fetchUserProfile, isDataStale, userProfile} = useUserStore();
  const queryClient = useQueryClient();
  const [refreshing, setRefreshing] = React.useState(false);
  const [menuVisible, setMenuVisible] = React.useState(false);
  const {setConfig} = useConfigStore();
  const {logout} = useAuthStore();
  const insets = useSafeAreaInsets();
  const {clearSelection} = useDrawNumberStore();
  // Get display name directly from userProfile as an alternative approach
  const displayNameDirect = userProfile?.fullName;
  const displayName = useUserStore(state => state.getDisplayName());
  const scrollHandler = useAnimatedScrollHandler(event => {
    scrollY.value = event.contentOffset.y;
  });
  const handleLogout = () => {
    Alert.alert('Đăng xuất', 'Bạn có chắc chắn muốn đăng xuất?', [
      {
        text: 'Huỷ',
        style: 'cancel',
      },
      {
        text: 'Đăng xuất',
        style: 'destructive',
        onPress: () => {
          setConfig('loginInfo', null);

          logout();

          const bundleId = DeviceInfo.getBundleId();
          if (bundleId === 'vn.gov.quangninh.dichvucong') {
            setConfig('loginMode', 'citizen');
          } else if (bundleId === 'vn.gov.quangninh.dichvucong.congchuc') {
            setConfig('loginMode', 'officer');
          }

          setMenuVisible(false);
          Alert.alert('Đã đăng xuất thành công');
        },
      },
    ]);
  };

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    console.log('Pull-to-refresh triggered, refreshing data...');

    try {
      await fetchUserProfile(true);
      await queryClient.invalidateQueries(USE_REQUESTER_BIZ_TEMPLATES_KEY);
      await queryClient.invalidateQueries(USE_REQUESTER_BIZ_SHARES_KEY);
      await queryClient.invalidateQueries(USE_REQUESTER_BIZ_DOCUMENTS_KEY);
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  }, [fetchUserProfile, queryClient]);

  React.useEffect(() => {
    clearSelection();
  }, [clearSelection]);

  // Fetch user profile when component mounts
  React.useEffect(() => {
    fetchUserProfile();
  }, [fetchUserProfile]);

  // Check if data is stale and refresh if needed
  React.useEffect(() => {
    if (isDataStale()) {
      console.log('User data is stale, refreshing...');
      fetchUserProfile(true);
    }
  }, [fetchUserProfile, isDataStale]);

  // Add fallback to displayName if empty
  const finalDisplayName = displayName || displayNameDirect || 'Người dùng';

  // Animated style for header background opacity
  const headerBgAnimatedStyle = useAnimatedStyle(() => {
    const bgOpacity = interpolate(
      scrollY.value,
      [0, BG_FADE_DISTANCE],
      [0, 1],
      Extrapolate.CLAMP,
    );
    return {opacity: bgOpacity};
  });

  return (
    <BottomSheetModalProvider>
      <View
        style={[
          styles.mainContainer,
          {backgroundColor: theme.colors.background},
        ]}>
        <LinearGradient
          colors={[
            theme.colors.secondaryContainer,
            theme.colors.surfaceVariant,
          ]}
          style={[StyleSheet.absoluteFill, {opacity: 0.95}]}
        />
        <View
          style={[
            styles.headerContainer,
            {
              paddingTop: insets.top > 0 ? insets.top + 8 : 20,
            },
          ]}>
          <Animated.View
            style={[
              styles.headerBg,
              {
                backgroundColor: theme.colors.secondaryContainer,
                shadowColor: theme.colors.secondary,
                shadowOpacity: 0.15,
                shadowRadius: 8,
                elevation: 8,
              },
              headerBgAnimatedStyle,
            ]}
          />
          <Menu
            visible={menuVisible}
            onDismiss={() => setMenuVisible(false)}
            anchor={
              <TouchableOpacity
                style={styles.headerLeft}
                onPress={() => setMenuVisible(true)}>
                <Text
                  style={[
                    styles.greetingTitle,
                    {color: theme.colors.onSecondaryContainer},
                  ]}>
                  Xin chào,
                </Text>
                <Text
                  style={[
                    styles.greetingName,
                    {color: theme.colors.onSecondaryContainer},
                  ]}>
                  {finalDisplayName}
                </Text>
              </TouchableOpacity>
            }
            contentStyle={[
              styles.menuContent,
              {backgroundColor: theme.colors.surface},
            ]}
            anchorPosition="bottom">
            <Menu.Item
              leadingIcon="logout"
              onPress={() => {
                setMenuVisible(false);
                handleLogout();
              }}
              title={'Đăng xuất'}
            />
          </Menu>
          <View style={styles.headerRight}>
            <TouchableOpacity
              style={styles.iconButton}
              onPress={() => {
                try {
                  navigation.navigate('DocumentsScreen');
                } catch (navigationError) {}
              }}>
              <Icon
                name="file-document-multiple-outline"
                size={24}
                color={theme.colors.onSecondaryContainer}
              />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.iconButton}
              onPress={() => {
                try {
                  navigation.navigate('SharesScreen');
                } catch (navigationError) {}
              }}>
              <Icon
                name="folder-outline"
                size={24}
                color={theme.colors.onSecondaryContainer}
              />
            </TouchableOpacity>
          </View>
        </View>

        <Animated.ScrollView
          onScroll={scrollHandler}
          scrollEventThrottle={16}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor={theme.colors.primary}
              title="Đang tải..."
              colors={[theme.colors.primary]}
              progressBackgroundColor={theme.colors.surface}
            />
          }
          contentContainerStyle={[
            styles.scrollViewContent,
            {paddingTop: HEADER_HEIGHT + (insets.top > 0 ? insets.top : 12)},
          ]}>
          <MainContent />
          {/* Demo Form at the bottom */}
          {/* <DemoForm /> */}
        </Animated.ScrollView>
      </View>
    </BottomSheetModalProvider>
  );
};

// Demo form schema - commented out to prevent unused variable warnings
/* const demoSchema = yup.object().shape({
  name: yup.string().required('Vui lòng nhập tên'),
  email: yup
    .string()
    .email('Email không hợp lệ')
    .required('Vui lòng nhập email'),
}); */

// Demo form for testing purposes - commented out to prevent unused variable warnings
/* const DemoForm = () => {
  // Define the form data type
  type FormData = {
    name: string;
    email: string;
  };

  const {
    control,
    handleSubmit,
    formState: {errors},
  } = useForm<FormData>({
    resolver: yupResolver(demoSchema),
    defaultValues: {name: '', email: ''},
  });

  const onSubmit = (data: FormData) => {
    Alert.alert('Thông tin đã nhập', `Tên: ${data.name}\nEmail: ${data.email}`);
  };

  return (
    <View style={styles.demoFormContainer}>
      <Text style={styles.demoFormTitle}>Demo Form</Text>
      <Controller
        control={control}
        name="name"
        render={({field: {onChange, onBlur, value}}) => (
          <TextInput
            style={[styles.demoInput, errors.name && styles.demoInputError]}
            placeholder="Tên"
            onBlur={onBlur}
            onChangeText={onChange}
            value={value}
          />
        )}
      />
      {errors.name && (
        <Text style={styles.demoErrorText}>{errors.name.message}</Text>
      )}
      <Controller
        control={control}
        name="email"
        render={({field: {onChange, onBlur, value}}) => (
          <TextInput
            style={[styles.demoInput, errors.email && styles.demoInputError]}
            placeholder="Email"
            keyboardType="email-address"
            autoCapitalize="none"
            onBlur={onBlur}
            onChangeText={onChange}
            value={value}
          />
        )}
      />
      {errors.email && (
        <Text style={styles.demoErrorText}>{errors.email.message}</Text>
      )}
      <Button title="Gửi" onPress={handleSubmit(onSubmit)} />
    </View>
  );
}; */

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  menuContent: {
    borderRadius: 8,
    marginTop: 8,
    width: 200,
    elevation: 5,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  headerContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    minHeight: HEADER_HEIGHT,
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 12,
    backgroundColor: 'transparent',
    zIndex: 10,
    overflow: 'hidden',
  },
  headerBg: {
    ...StyleSheet.absoluteFillObject,
    zIndex: -1,
  },
  headerLeft: {
    flexDirection: 'column',
  },
  greetingTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  greetingName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 2,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  iconButton: {
    marginLeft: 8,
    padding: 4,
  },
  badge: {
    position: 'absolute',
    top: 0,
    right: 0,
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 3,
  },
  badgeText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingBottom: 24,
    zIndex: 1,
  },
  demoFormContainer: {
    marginTop: 32,
    marginHorizontal: 20,
    padding: 16,
    borderRadius: 12,
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  demoFormTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  demoInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 10,
    marginBottom: 8,
  },
  demoInputError: {},
  demoErrorText: {
    fontSize: 12,
    marginBottom: 8,
  },
});

export default DashboardHomeScreen;
