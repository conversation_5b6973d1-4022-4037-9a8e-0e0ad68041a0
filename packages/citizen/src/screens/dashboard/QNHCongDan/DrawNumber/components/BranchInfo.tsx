import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import {useTheme} from 'react-native-paper';
import {textStyles} from '../../../../../styles/QNH_textStyle';

interface Branch {
  id: string;
  name: string;
  address?: string;
}

interface BranchInfoProps {
  branch: Branch;
  onPress?: () => void;
}

const BranchInfo: React.FC<BranchInfoProps> = ({branch, onPress}) => {
  const theme = useTheme();
  const styles = StyleSheet.create({
    branchInfo: {
      backgroundColor: theme.colors.surface,
      borderRadius: 8,
      padding: 12,
      marginVertical: 8,
      shadowColor: theme.colors.outlineVariant || '#000',
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    branchHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    branchTitleContainer: {
      flex: 1,
    },
    branchName: {
      color: theme.colors.onSurface,
      fontWeight: 'bold',
    },
    addressLabel: {
      color: theme.colors.onSurface,
      opacity: 0.7,
    },
    checkmark: {
      backgroundColor: theme.colors.primary,
      borderRadius: 12,
      padding: 4,
      marginLeft: 8,
    },
    checkmarkText: {
      color: theme.colors.onPrimary,
      fontWeight: 'bold',
    },
  });

  return (
    <TouchableOpacity
      style={styles.branchInfo}
      onPress={onPress ? () => onPress() : undefined}
      activeOpacity={0.7}>
      <View style={styles.branchHeader}>
        <View style={styles.branchTitleContainer}>
          <Text className={`${textStyles.h6}`} style={styles.branchName}>
            {branch.name}
          </Text>
          <Text className={`${textStyles.body2}`} style={styles.addressLabel}>
            {branch.address || ''}
          </Text>
        </View>
        {onPress && (
          <View style={styles.checkmark}>
            <Text style={styles.checkmarkText}>✓</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

export default BranchInfo;
