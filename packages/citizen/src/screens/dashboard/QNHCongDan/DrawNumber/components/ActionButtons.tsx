import React from 'react';
import SharedActionButtons from '../../../../../components/shared/ActionButtons';

interface ActionButtonsProps {
  isLoading: boolean;
  onCancel: () => void;
  onConfirm: () => void;
}

const ActionButtons: React.FC<ActionButtonsProps> = ({
  isLoading,
  onCancel,
  onConfirm,
}) => {
  return (
    <SharedActionButtons
      primaryButton={{
        label: 'Xác nhận',
        onPress: onConfirm,
        variant: 'contained',
        iconName: 'check',
        iconType: 'material-community',

        loading: isLoading,
        disabled: isLoading,

        flex: true,
      }}
      secondaryButton={{
        label: 'Huỷ bỏ',
        onPress: onCancel,
        variant: 'elevated',
        iconName: 'close',
        iconType: 'material-community',

        disabled: isLoading,
        flex: true,
      }}
      spacing={12}
    />
  );
};

export default ActionButtons;
