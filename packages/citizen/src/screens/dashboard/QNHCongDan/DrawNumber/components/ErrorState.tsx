import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import {useTheme} from 'react-native-paper';
import {textStyles} from '../../../../../styles/QNH_textStyle';

interface ErrorStateProps {
  error: string | null;
  onRetry?: () => void;
  buttonText?: string;
}

const ErrorState: React.FC<ErrorStateProps> = ({
  error,
  onRetry,
  buttonText = 'Thử lại',
}) => {
  const theme = useTheme();
  const styles = StyleSheet.create({
    errorContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: 24,
    },
    errorIcon: {
      marginBottom: 12,
    },
    errorIconText: {
      fontSize: 32,
      color: theme.colors.error,
    },
    errorText: {
      color: theme.colors.onSurface,
      textAlign: 'center',
      marginBottom: 16,
    },
    retryButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 8,
      paddingVertical: 8,
      paddingHorizontal: 24,
      marginTop: 8,
    },
    retryButtonText: {
      color: theme.colors.onPrimary,
      fontWeight: 'bold',
    },
  });

  return (
    <View style={styles.errorContainer}>
      <View style={styles.errorIcon}>
        <Text style={styles.errorIconText}>⚠️</Text>
      </View>
      <Text className={`${textStyles.body1}`} style={styles.errorText}>
        {error}
      </Text>
      {onRetry && (
        <TouchableOpacity style={styles.retryButton} onPress={onRetry}>
          <Text className={`${textStyles.h6}`} style={styles.retryButtonText}>
            {buttonText}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default ErrorState;
