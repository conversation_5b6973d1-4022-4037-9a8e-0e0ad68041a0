import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {useTheme} from 'react-native-paper';
import {textStyles} from '../../../../../styles/QNH_textStyle';

interface EmptyStateProps {
  searchQuery?: string;
  selectedTag?: string | null;
  customMessage?: string;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  searchQuery,
  selectedTag,
  customMessage,
}) => {
  const theme = useTheme();
  const styles = StyleSheet.create({
    emptyContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: 32,
    },
    emptyText: {
      color: theme.colors.onSurface,
      textAlign: 'center',
    },
  });

  const message =
    customMessage ||
    (searchQuery || selectedTag
      ? 'Không tìm thấy dịch vụ phù hợp'
      : 'Không có dịch vụ nào');

  return (
    <View style={styles.emptyContainer}>
      <Text className={`${textStyles.body1}`} style={styles.emptyText}>
        {message}
      </Text>
    </View>
  );
};

export default EmptyState;
