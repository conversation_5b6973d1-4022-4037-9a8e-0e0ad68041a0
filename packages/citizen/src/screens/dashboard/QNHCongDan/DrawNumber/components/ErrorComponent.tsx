import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import {textStyles} from '../../../../../styles/QNH_textStyle';

interface ErrorComponentProps {
  errorMessage: string;
  buttonText: string;
  onButtonPress: () => void;
}

const ErrorComponent: React.FC<ErrorComponentProps> = ({
  errorMessage,
  buttonText,
  onButtonPress,
}) => {
  return (
    <View style={styles.errorContainer}>
      <View style={styles.errorIcon}>
        <Text style={styles.errorIconText}>⚠️</Text>
      </View>
      <Text className={`${textStyles.body1}`} style={styles.errorText}>
        {errorMessage}
      </Text>
      <TouchableOpacity style={styles.retryButton} onPress={onButtonPress}>
        <Text className={`${textStyles.h6}`} style={styles.retryButtonText}>
          {buttonText}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 24,
  },
  errorIcon: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: 'rgba(255, 69, 58, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  errorIconText: {
    fontSize: 48,
  },
  errorText: {
    color: 'red',
    fontSize: 14,
    marginTop: 8,
    marginBottom: 8,
    textAlign: 'center',
  },
  retryButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    backgroundColor: '#007AFF',
    borderRadius: 8,
    marginTop: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
});

export default ErrorComponent;
