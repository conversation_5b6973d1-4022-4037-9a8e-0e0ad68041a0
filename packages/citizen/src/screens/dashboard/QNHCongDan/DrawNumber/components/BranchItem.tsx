import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import {Branch} from '../../../../../requester-biz-service/apis/draw-number-api';
import {useTheme} from 'react-native-paper';

interface BranchItemProps {
  branch: Branch;
  isSelected: boolean;
  hasReturned: boolean;
  onSelect: (branch: Branch) => void;
}

const BranchItem: React.FC<BranchItemProps> = ({
  branch,
  isSelected,
  hasReturned,
  onSelect,
}) => {
  const theme = useTheme();

  // Enhanced styles for selected branch when returning from next screen
  const enhancedStyles =
    isSelected && hasReturned
      ? {
          borderWidth: 2,
          borderColor: theme.colors.primary,
          backgroundColor: theme.colors.primary,
          shadowColor: '#000',
          shadowOffset: {width: 0, height: 2},
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 3,
        }
      : {};

  return (
    <TouchableOpacity
      style={[
        styles.branchItem,
        {
          backgroundColor: theme.colors.surface,
          borderColor: theme.colors.outline,
        },
        isSelected && [
          styles.selectedBranchItem,
          {
            borderColor: theme.colors.primary,
            backgroundColor:
              isSelected && hasReturned
                ? theme.colors.primary
                : theme.colors.surface,
          },
        ],
        enhancedStyles,
      ]}
      onPress={() => onSelect(branch)}
      activeOpacity={0.7}>
      <View style={styles.branchContent}>
        <Text
          style={[
            styles.branchName,
            {
              color:
                isSelected && hasReturned
                  ? theme.colors.onPrimary
                  : theme.colors.onSurface,
              fontWeight: isSelected && hasReturned ? '700' : '600',
            },
          ]}>
          {branch.name}
        </Text>
        {branch.address && (
          <Text
            style={[
              styles.branchAddress,
              {
                color:
                  isSelected && hasReturned
                    ? theme.colors.onPrimary
                    : theme.colors.onSurface,
              },
            ]}>
            {branch.address}
          </Text>
        )}
      </View>
      {isSelected && (
        <View
          style={[
            styles.checkmark,
            {
              backgroundColor:
                isSelected && hasReturned
                  ? theme.colors.onPrimary
                  : theme.colors.primary,
            },
          ]}>
          <Text
            style={[
              styles.checkmarkText,
              {
                color:
                  isSelected && hasReturned
                    ? theme.colors.primary
                    : theme.colors.onPrimary,
              },
            ]}>
            ✓
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  branchItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 12,
    marginVertical: 3,
    borderRadius: 8,
    borderWidth: 1,
  },
  selectedBranchItem: {},
  branchContent: {
    flex: 1,
  },
  branchName: {
    marginBottom: 3,
    fontSize: 14,
  },
  branchAddress: {
    lineHeight: 18,
    fontSize: 12,
  },
  checkmark: {
    width: 22,
    height: 22,
    borderRadius: 11,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 10,
  },
  checkmarkText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
});

export default BranchItem;
