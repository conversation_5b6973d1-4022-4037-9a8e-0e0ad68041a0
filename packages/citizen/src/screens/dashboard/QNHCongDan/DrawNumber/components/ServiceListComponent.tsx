import React, {useRef, useEffect} from 'react';
import {View, Text, StyleSheet, FlatList, TouchableOpacity} from 'react-native';
import {textStyles} from '../../../../../styles/QNH_textStyle';
import {Service} from '../../../../../requester-biz-service/apis/draw-number-api';

interface ServiceListComponentProps {
  services: Service[];
  selectedService: Service | null;
  onServiceSelect: (service: Service) => void;
  hasReturned: boolean;
  searchQuery?: string;
  selectedTag?: string | null;
}

const ServiceListComponent: React.FC<ServiceListComponentProps> = ({
  services,
  selectedService,
  onServiceSelect,
  hasReturned,
  searchQuery,
  selectedTag,
}) => {
  const serviceListRef = useRef<FlatList>(null);


  const renderServiceItem = ({item}: {item: Service}) => {
    const isSelected = selectedService?.id === item.id;

    const enhancedStyles =
      isSelected && hasReturned
        ? {
            borderWidth: 2,
            borderColor: '#007AFF',
            backgroundColor: '#E6F2FF',
            shadowColor: '#000',
            shadowOffset: {width: 0, height: 2},
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 3,
          }
        : {};

    return (
      <TouchableOpacity
        style={[
          styles.serviceItem,
          isSelected && styles.selectedServiceItem,
          enhancedStyles,
        ]}
        onPress={() => onServiceSelect(item)}
        activeOpacity={0.7}>
        <View style={styles.serviceContent}>
          <Text
            className={`${textStyles.h6}`}
            style={[
              styles.serviceName,
              isSelected && hasReturned && styles.selectedServiceName,
            ]}>
            {item.name}
          </Text>
        </View>
        {isSelected && (
          <View style={styles.checkmark}>
            <Text style={styles.checkmarkText}>✓</Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const renderEmptyComponent = () => (
    <View style={styles.emptyContainer}>
      <Text className={`${textStyles.body1}`} style={styles.emptyText}>
        {searchQuery || selectedTag
          ? 'Không tìm thấy dịch vụ phù hợp'
          : 'Không có dịch vụ nào'}
      </Text>
    </View>
  );

  return (
    <FlatList
      ref={serviceListRef}
      data={services}
      renderItem={renderServiceItem}
      keyExtractor={item => item.id}
      contentContainerStyle={styles.listContainer}
      showsVerticalScrollIndicator={false}
      ListEmptyComponent={renderEmptyComponent}
    />
  );
};

const styles = StyleSheet.create({
  listContainer: {
    paddingBottom: 12,
  },
  serviceItem: {
    backgroundColor: 'white',
    borderRadius: 8,
    marginBottom: 4,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    paddingHorizontal: 12,
    paddingVertical: 5,
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedServiceItem: {
    borderColor: '#007AFF',
    backgroundColor: '#f0f8ff',
  },
  selectedServiceName: {
    color: '#007AFF',
    fontWeight: '700',
  },
  serviceContent: {
    flex: 1,
    marginRight: 8,
  },
  serviceName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 3,
  },
  checkmark: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#007AFF',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  checkmarkText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  emptyText: {
    color: '#666',
    textAlign: 'center',
    fontSize: 14,
  },
});

export default ServiceListComponent;
