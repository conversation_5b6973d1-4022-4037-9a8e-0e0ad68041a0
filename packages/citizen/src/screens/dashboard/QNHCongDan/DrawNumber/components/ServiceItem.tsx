import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {useTheme} from 'react-native-paper';
import {Service} from '../../../../../requester-biz-service/apis/draw-number-api';

interface ServiceItemProps {
  item: Partial<Service>; // Allow partial properties for flexibility
  isSelected: boolean;
  hasReturned: boolean;
  onSelect?: (service: Service) => void;
}

const ServiceItem: React.FC<ServiceItemProps> = ({
  item,
  isSelected,
  hasReturned,
  onSelect,
}) => {
  const theme = useTheme();

  // Enhanced styles for selected service when returning from next screen
  const enhancedStyles =
    isSelected && hasReturned
      ? {
          borderWidth: 2,
          borderColor: theme.colors.primary,
          backgroundColor: theme.colors.primary,
          shadowColor: '#000',
          shadowOffset: {width: 0, height: 2},
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 3,
        }
      : {};

  return (
    <TouchableOpacity
      style={[
        styles.serviceItem,
        {
          backgroundColor: theme.colors.surface,
          borderColor: theme.colors.outlineVariant || theme.colors.outline,
        },
        isSelected && [
          styles.selectedServiceItem,
          {
            borderColor: theme.colors.primary,
            backgroundColor:
              isSelected && hasReturned
                ? theme.colors.primary
                : theme.colors.surface,
          },
        ],
        enhancedStyles,
      ]}
      onPress={() =>
        onSelect &&
        item.id !== undefined &&
        item.name !== undefined &&
        onSelect(item as Service)
      }
      activeOpacity={0.7}>
      <View style={styles.serviceContent}>
        <Text
          style={[
            styles.serviceName,
            {
              color:
                isSelected && hasReturned
                  ? theme.colors.onPrimary
                  : theme.colors.onSurface,
              fontWeight: isSelected && hasReturned ? '700' : '600',
            },
          ]}>
          {item.name}
        </Text>
      </View>
      {isSelected && (
        <View
          style={[
            styles.checkmark,
            {
              backgroundColor:
                isSelected && hasReturned
                  ? theme.colors.onPrimary
                  : theme.colors.primary,
            },
          ]}>
          <Text
            style={[
              styles.checkmarkText,
              {
                color:
                  isSelected && hasReturned
                    ? theme.colors.primary
                    : theme.colors.onPrimary,
              },
            ]}>
            ✓
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  serviceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 12,
    marginVertical: 3,
    borderRadius: 8,
    borderWidth: 1,
  },
  selectedServiceItem: {},
  serviceContent: {
    flex: 1,
    marginRight: 8,
  },
  serviceName: {
    marginBottom: 3,
    fontSize: 14,
  },
  checkmark: {
    width: 22,
    height: 22,
    borderRadius: 11,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 10,
  },
  checkmarkText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
});

export default ServiceItem;
