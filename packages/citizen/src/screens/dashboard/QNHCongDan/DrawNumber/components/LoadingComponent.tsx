import React from 'react';
import {View, Text, StyleSheet, ActivityIndicator} from 'react-native';
import {textStyles} from '../../../../../styles/QNH_textStyle';

interface LoadingComponentProps {
  message?: string;
}

const LoadingComponent: React.FC<LoadingComponentProps> = ({
  message = 'Đang tải dữ liệu...',
}) => {
  return (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color="#007AFF" />
      <Text className={`${textStyles.body1}`} style={styles.loadingText}>
        {message}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    marginTop: 12,
    color: '#666',
    textAlign: 'center',
    fontSize: 14,
  },
});

export default LoadingComponent;
