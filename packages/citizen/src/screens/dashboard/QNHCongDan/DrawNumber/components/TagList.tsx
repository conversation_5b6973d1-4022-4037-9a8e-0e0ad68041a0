import React from 'react';
import {View, Text, StyleSheet, FlatList, TouchableOpacity} from 'react-native';
import {useTheme} from 'react-native-paper';

import EmptyState from './EmptyState';

interface TagListProps {
  tags: string[];
  selectedTag: string | null;
  onTagSelect: (tag: string) => void;
  isKeyboardVisible?: boolean;
}

const TagList: React.FC<TagListProps> = ({
  tags,
  selectedTag,
  onTagSelect,
  isKeyboardVisible = false,
}) => {
  const theme = useTheme();
  const styles = StyleSheet.create({
    tagItem: {
      backgroundColor: theme.colors.surface,
      borderRadius: 8,
      paddingVertical: 10,
      paddingHorizontal: 16,
      marginVertical: 6,
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: theme.colors.outlineVariant || theme.colors.onSurface,
    },
    selectedTagItem: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    tagText: {
      color: theme.colors.onSurface,
      flex: 1,
    },
    tagTextSelected: {
      color: theme.colors.onPrimary,
      fontWeight: 'bold',
    },
    checkmark: {
      backgroundColor: theme.colors.primary,
      borderRadius: 12,
      padding: 4,
      marginLeft: 8,
    },
    checkmarkText: {
      color: theme.colors.onPrimary,
      fontWeight: 'bold',
    },
    sectionContainer: {
      flex: 1,
      paddingTop: 8,
    },
    keyboardOpenSectionContainer: {
      paddingTop: 0,
      marginTop: 0,
    },
    sectionTitle: {
      fontSize: 15,
      fontWeight: '600',
      marginVertical: 10,
      color: theme.colors.onSurface, // Use theme color for text
    },
    listContainer: {
      paddingBottom: 12,
    },
  });

  const renderTagItem = ({item}: {item: string}) => {
    const isSelected = item === selectedTag;

    return (
      <TouchableOpacity
        style={[styles.tagItem, isSelected && styles.selectedTagItem]}
        onPress={() => onTagSelect(item)}
        activeOpacity={0.7}>
        <Text style={[styles.tagText, isSelected && styles.tagTextSelected]}>
          {item}
        </Text>
        {isSelected && (
          <View style={styles.checkmark}>
            <Text style={styles.checkmarkText}>✓</Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View
      style={[
        styles.sectionContainer,
        isKeyboardVisible && styles.keyboardOpenSectionContainer,
      ]}>
      {!isKeyboardVisible && (
        <Text style={styles.sectionTitle}>Chọn mục đích</Text>
      )}
      <FlatList
        data={tags}
        renderItem={renderTagItem}
        keyExtractor={item => item}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={<EmptyState />}
      />
    </View>
  );
};

export default TagList;
