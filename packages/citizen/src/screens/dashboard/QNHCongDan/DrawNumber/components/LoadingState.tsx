import React from 'react';
import {View, Text, StyleSheet, ActivityIndicator} from 'react-native';
import {useTheme} from 'react-native-paper';
import {textStyles} from '../../../../../styles/QNH_textStyle';

interface LoadingStateProps {
  message?: string;
}

const LoadingState: React.FC<LoadingStateProps> = ({
  message = 'Đang tải dữ liệu...',
}) => {
  const theme = useTheme();
  const styles = StyleSheet.create({
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: 20,
    },
    loadingText: {
      marginTop: 12,
      color: theme.colors.onSurface,
      textAlign: 'center',
      fontSize: 14,
    },
  });
  return (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color={theme.colors.primary} />
      <Text className={`${textStyles.body1}`} style={styles.loadingText}>
        {message}
      </Text>
    </View>
  );
};

export default LoadingState;
