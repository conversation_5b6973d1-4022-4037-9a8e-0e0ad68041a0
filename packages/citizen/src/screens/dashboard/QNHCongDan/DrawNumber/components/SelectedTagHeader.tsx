import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {Button} from 'react-native-paper';
import {textStyles} from '../../../../../styles/QNH_textStyle';

interface SelectedTagHeaderProps {
  selectedTag: string;
  onChangeTag: () => void;
}

const SelectedTagHeader: React.FC<SelectedTagHeaderProps> = ({
  selectedTag,
  onChangeTag,
}) => {
  return (
    <View style={styles.selectedTagHeader}>
      <View style={styles.selectedTagInfo}>
        <Text className={`${textStyles.body2}`} style={styles.tagLabel}>
          Mục đích: <Text style={styles.selectedTagName}>{selectedTag}</Text>
        </Text>
      </View>
      <Button mode="text" compact onPress={onChangeTag}>
        Thay đổi
      </Button>
    </View>
  );
};

const styles = StyleSheet.create({
  selectedTagHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  selectedTagInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tagLabel: {
    fontSize: 13,
    fontWeight: '500',
    marginBottom: 6,
    color: '#444',
  },
  selectedTagName: {
    color: '#333',
    fontWeight: '600',
    fontSize: 13,
  },
});

export default SelectedTagHeader;
