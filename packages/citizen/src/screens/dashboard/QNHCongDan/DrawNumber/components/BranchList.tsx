import React, {useRef, useEffect} from 'react';
import {FlatList, StyleSheet} from 'react-native';
import {Branch} from '../../../../../requester-biz-service/apis/draw-number-api';
import BranchItem from './BranchItem';
import EmptyState from './EmptyState';

interface BranchListProps {
  branches: Branch[];
  selectedBranch: Branch | null;
  hasReturned: boolean;
  onSelectBranch: (branch: Branch) => void;
  searchQuery: string;
}

const BranchList: React.FC<BranchListProps> = ({
  branches,
  selectedBranch,
  hasReturned,
  onSelectBranch,
  searchQuery,
}) => {
  // Reference for FlatList to enable scrolling to selected item
  const branchListRef = useRef<FlatList>(null);

  const renderEmptyComponent = () => (
    <EmptyState
      searchQuery={searchQuery}
      customMessage={
        searchQuery
          ? 'Không tìm thấy chi nhánh phù hợp'
          : 'Không có chi nhánh nào'
      }
    />
  );

  return (
    <FlatList
      ref={branchListRef}
      data={branches}
      renderItem={({item}) => (
        <BranchItem
          branch={item}
          isSelected={selectedBranch?.id === item.id}
          hasReturned={hasReturned}
          onSelect={onSelectBranch}
        />
      )}
      keyExtractor={item => item.id}
      contentContainerStyle={styles.listContainer}
      showsVerticalScrollIndicator={false}
      ListEmptyComponent={renderEmptyComponent()}

    />
  );
};

const styles = StyleSheet.create({
  listContainer: {
    paddingBottom: 12, // Reduced from 16
    paddingHorizontal: 1, // Added to avoid items touching the edge
  },
});

export default BranchList;
