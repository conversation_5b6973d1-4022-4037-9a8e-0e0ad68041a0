import React, {useRef, useEffect} from 'react';
import {View, Text, StyleSheet, FlatList} from 'react-native';
import {Button, useTheme} from 'react-native-paper';

import {Service} from '../../../../../requester-biz-service/apis/draw-number-api';
import ServiceItem from './ServiceItem';
import EmptyState from './EmptyState';

interface ServiceListProps {
  services: Service[];
  selectedService: Service | null;
  selectedTag: string | null;
  searchQuery: string;
  onServiceSelect: (service: Service) => void;
  onChangeTag: () => void;
  isKeyboardVisible: boolean;
  hasReturned: boolean;
}

const ServiceList: React.FC<ServiceListProps> = ({
  services,
  selectedService,
  selectedTag,
  searchQuery,
  onServiceSelect,
  onChangeTag,
  isKeyboardVisible,
  hasReturned,
}) => {
  const theme = useTheme();
  const serviceListRef = useRef<FlatList>(null);

  const styles = StyleSheet.create({
    listContainer: {
      flex: 1,
      // backgroundColor: theme.colors.background,
    },
    sectionContainer: {
      flex: 1,
      paddingTop: 8,
    },
    keyboardOpenSectionContainer: {
      paddingTop: 0,
      marginTop: 0,
    },
    selectedTagHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 12,
    },
    selectedTagInfo: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    tagLabel: {
      fontSize: 13,
      fontWeight: '500',
      marginBottom: 6,
      color: theme.colors.onSurface, // Use theme color for text
    },
    selectedTagName: {
      color: theme.colors.onSurface, // Use theme color for text
      fontWeight: '600',
      fontSize: 13,
    },
    spacer: {
      height: 8,
    },
    listContainerKeyboardOpen: {
      paddingTop: 10,
      paddingBottom: 80,
    },
  });

  return (
    <View
      style={[
        styles.sectionContainer,
        isKeyboardVisible && styles.keyboardOpenSectionContainer,
      ]}>
      {!isKeyboardVisible && (
        <>
          <View style={styles.selectedTagHeader}>
            <View style={styles.selectedTagInfo}>
              <Text style={styles.tagLabel}>
                Mục đích:{' '}
                <Text style={styles.selectedTagName}>{selectedTag}</Text>
              </Text>
            </View>
            <Button mode="text" compact onPress={onChangeTag}>
              Thay đổi
            </Button>
          </View>

          {/* Small spacer since we moved search to AppBar */}
          <View style={styles.spacer} />
        </>
      )}

      <FlatList
        ref={serviceListRef}
        data={services}
        renderItem={({item}) => (
          <ServiceItem
            item={item}
            isSelected={selectedService?.id === item.id}
            hasReturned={hasReturned}
            onSelect={onServiceSelect}
          />
        )}
        keyExtractor={item => item.id}
        contentContainerStyle={[
          styles.listContainer,
          isKeyboardVisible && styles.listContainerKeyboardOpen,
        ]}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <EmptyState searchQuery={searchQuery} selectedTag={selectedTag} />
        }
      />
    </View>
  );
};

export default ServiceList;
