import React from 'react';
import {View, Text, StyleSheet, FlatList, TouchableOpacity} from 'react-native';
import {Modal, Portal, Button, IconButton} from 'react-native-paper';
import {textStyles} from '../../../../../styles/QNH_textStyle';

interface TagSelectionModalProps {
  visible: boolean;
  onDismiss: () => void;
  tags: string[];
  selectedTag: string | null;
  onTagSelect: (tag: string) => void;
}

const TagSelectionModal: React.FC<TagSelectionModalProps> = ({
  visible,
  onDismiss,
  tags,
  selectedTag,
  onTagSelect,
}) => {
  // Use useEffect to properly handle the case when tags change from non-empty to empty
  React.useEffect(() => {
    if (visible && (!tags || tags.length === 0)) {
      onDismiss();
    }
  }, [visible, tags, onDismiss]);

  // If there are no tags, don't render the modal
  if (!tags || tags.length === 0) {
    return null;
  }

  const renderTagItem = ({item}: {item: string}) => {
    const isSelected = item === selectedTag;

    return (
      <TouchableOpacity
        style={[styles.tagItem, isSelected && styles.selectedTagItem]}
        onPress={() => onTagSelect(item)}
        activeOpacity={0.7}>
        <Text
          className={`${textStyles.body1}`}
          style={[styles.tagText, isSelected && styles.tagTextSelected]}>
          {item}
        </Text>
        {isSelected && (
          <View style={styles.checkmark}>
            <Text style={styles.checkmarkText}>✓</Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const renderEmptyComponent = () => (
    <View style={styles.emptyContainer}>
      <Text className={`${textStyles.body1}`} style={styles.emptyText}>
        Không có mục đích nào
      </Text>
    </View>
  );

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <Text className={`${textStyles.h5}`} style={styles.modalTitle}>
            Chọn mục đích
          </Text>
          <IconButton
            icon="close"
            size={24}
            onPress={onDismiss}
            style={styles.closeButton}
          />
        </View>

        <View style={styles.modalContent}>
          <FlatList
            data={tags}
            renderItem={renderTagItem}
            keyExtractor={item => item}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={renderEmptyComponent}
          />
        </View>

        <View style={styles.modalActions}>
          <Button
            mode="outlined"
            onPress={onDismiss}
            style={styles.cancelButton}>
            Hủy
          </Button>
        </View>
      </Modal>
    </Portal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    backgroundColor: 'white',
    margin: 20,
    borderRadius: 16,
    maxHeight: '80%',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  closeButton: {
    margin: 0,
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  listContainer: {
    paddingBottom: 8,
  },
  tagItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 16,
    marginBottom: 8,
    backgroundColor: '#F8F9FA',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  selectedTagItem: {
    backgroundColor: '#E3F2FD',
    borderColor: '#007AFF',
    borderWidth: 2,
  },
  tagText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  tagTextSelected: {
    color: '#007AFF',
    fontWeight: '600',
  },
  checkmark: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#007AFF',
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkmarkText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    color: '#666',
    textAlign: 'center',
    fontSize: 16,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  cancelButton: {
    minWidth: 100,
  },
});

export default TagSelectionModal;
