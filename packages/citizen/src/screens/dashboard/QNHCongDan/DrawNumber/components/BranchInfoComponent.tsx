import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import {textStyles} from '../../../../../styles/QNH_textStyle';
import {Branch} from '../../../../../requester-biz-service/apis/draw-number-api';

interface BranchInfoProps {
  branch: Branch;
  onPress: () => void;
}

const BranchInfoComponent: React.FC<BranchInfoProps> = ({branch, onPress}) => {
  return (
    <TouchableOpacity
      style={styles.branchInfo}
      onPress={onPress}
      activeOpacity={0.7}>
      <View style={styles.branchHeader}>
        <View style={styles.branchTitleContainer}>
          <Text className={`${textStyles.h6}`} style={styles.branchName}>
            {branch.name}
          </Text>
          <Text className={`${textStyles.body2}`} style={styles.addressLabel}>
            {branch.address}
          </Text>
        </View>
        <View style={styles.checkmark}>
          <Text style={styles.checkmarkText}>✓</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  branchInfo: {
    paddingVertical: 8,
    paddingHorizontal: 8,
    marginBottom: 8,
    borderRadius: 8,
    borderWidth: 0,
    backgroundColor: '#E6F2FF',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 2,
  },
  branchHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  branchTitleContainer: {
    flex: 1,
  },
  branchName: {
    fontWeight: '700',
    color: '#007AFF',
    marginBottom: 4,
    fontSize: 15,
  },
  addressLabel: {
    color: '#666',
    marginBottom: 1,
    fontSize: 13,
  },
  checkmark: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#007AFF',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  checkmarkText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
});

export default BranchInfoComponent;
