import React, {useState, useEffect, useMemo, useCallback} from 'react';
import {View, StyleSheet, BackHandler, Keyboard} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useNavigation, useFocusEffect} from '@react-navigation/native';
import {NavigationProp} from '@react-navigation/native';
import {MainStackParamList} from '../../../../navigation/MainNavigator';
import {Button} from 'react-native-paper';
import {CCAppBar} from '../../../../components/QNH_CongDanComponents/HeaderComponent/CCAppBar';
import {useTheme} from 'react-native-paper';

// API and Store
import {
  drawNumber,
  Service,
} from '../../../../requester-biz-service/apis/draw-number-api';
import {useDrawNumberStore} from '../../../../stores/drawNumber.store';

// Components
import BranchInfo from './components/BranchInfo';
import LoadingState from './components/LoadingState';
import ErrorState from './components/ErrorState';
import EmptyState from './components/EmptyState';
import TagList from './components/TagList';
import ServiceList from './components/ServiceList';
import {normalizeVietnameseText} from '../../../../utils/textUtils';

const DrawNumberFieldSelectScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp<MainStackParamList>>();
  const {selectedBranch, selectedService, setSelectedService} =
    useDrawNumberStore();
  const theme = useTheme();

  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [selectedTag, setSelectedTag] = useState<string | null>(null);
  const [availableTags, setAvailableTags] = useState<string[]>([]);
  const [showTagSelection, setShowTagSelection] = useState(true);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const [hasReturned, setHasReturned] = useState(false);

  const filteredServices = useMemo(() => {
    if (!searchQuery.trim() && !selectedTag) {
      return services;
    }

    let tagFiltered = services;
    if (selectedTag) {
      tagFiltered = services.filter(service => {
        if (!service.tags) {
          return false;
        }
        const serviceTags = service.tags
          .split(',')
          .map(t => t.trim())
          .filter(t => t.length > 0);
        return serviceTags.includes(selectedTag);
      });
    }

    if (!searchQuery.trim()) {
      return tagFiltered;
    }

    const normalizedQuery = normalizeVietnameseText(searchQuery.toLowerCase());

    return tagFiltered.filter(service => {
      const normalizedName = normalizeVietnameseText(
        service.name?.toLowerCase() || '',
      );
      const normalizedDescription = normalizeVietnameseText(
        service.description?.toLowerCase() || '',
      );

      return (
        normalizedName.includes(normalizedQuery) ||
        normalizedDescription.includes(normalizedQuery)
      );
    });
  }, [services, searchQuery, selectedTag]);

  const handleBackPress = useCallback(() => {
    setSelectedService(null);
    navigation.goBack();
  }, [setSelectedService, navigation]);

  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        handleBackPress();
        return true;
      };

      const subscription = BackHandler.addEventListener(
        'hardwareBackPress',
        onBackPress,
      );

      return () => subscription.remove();
    }, [handleBackPress]),
  );

  const fetchServices = useCallback(async () => {
    if (!selectedBranch) {
      setError('Chưa chọn chi nhánh');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await drawNumber.drawNumberListService(
        selectedBranch.id,
      );

      if (response.data?.items) {
        const uniqueServices: {[key: string]: Service} = {};
        const tags = new Set<string>();

        for (const item of response.data.items) {
          const service = (item as any).services || item;
          if (service && !uniqueServices[service.id.toString()]) {
            const tagString =
              service.tag?.toString() || service.tags?.toString() || '';
            let processedTags = '';
            if (tagString) {
              const splitTags = tagString
                .split(',')
                .map((t: string) => t.trim())
                .filter((t: string) => t.length > 0);
              splitTags.forEach((tag: string) => tags.add(tag));
              processedTags = tagString;
            }

            uniqueServices[service.id.toString()] = {
              id: service.id.toString(),
              name: service.name,
              description: service.description,
              tags: processedTags || undefined,
              createdAt: service.createdAt || new Date().toISOString(),
              updatedAt: service.updatedAt || new Date().toISOString(),
              branchId: selectedBranch.id,
              keyword: service.keyword,
              tenantId: service.tenantId,
              metadata: service.metadata,
              procedures: service.procedures,
            };
          }
        }

        const servicesList = Object.values(uniqueServices);
        const tagsList = Array.from(tags)
          .filter(tag => tag.length > 0)
          .sort();

        setServices(servicesList);
        setAvailableTags(tagsList);
      }
    } catch (err: any) {
      setError('Đã xảy ra lỗi khi tải dữ liệu');
      console.error('Error fetching services:', err);
    } finally {
      setLoading(false);
    }
  }, [selectedBranch]);

  useEffect(() => {
    fetchServices();
  }, [fetchServices]);

  useEffect(() => {
    if (selectedService) {
      setHasReturned(true);
    }
  }, [selectedService]);

  const handleTagSelect = (tag: string) => {
    setSelectedTag(tag);
    setShowTagSelection(false);
    setSearchQuery('');
    Keyboard.dismiss();
  };

  const handleChangeTag = () => {
    setShowTagSelection(true);
    setSelectedTag(null);
    setSearchQuery('');
    setSelectedService(null);
  };

  const handleServiceSelect = (service: Service) => {
    setSelectedService(service);
    Keyboard.dismiss();
  };

  const handleContinue = () => {
    if (selectedService) {
      console.log('Selected service:', selectedService);
      navigation.navigate('DrawNumberConfirmation');
    }
  };

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setIsKeyboardVisible(true);
      },
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setIsKeyboardVisible(false);
      },
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  // Update styles to use theme
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      paddingHorizontal: 8,
    },
    bottomContainer: {
      padding: 8,
    },
  });

  if (!selectedBranch) {
    return (
      <SafeAreaView style={styles.container} edges={['bottom']}>
        <CCAppBar isBack={true} label="Chọn lĩnh vực" />
        <ErrorState
          error="Chưa chọn chi nhánh. Vui lòng quay lại và chọn chi nhánh."
          onRetry={handleBackPress}
          buttonText="Quay lại"
        />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <CCAppBar
        isBack={true}
        label={!selectedTag ? 'Chọn lĩnh vực' : undefined}
        showSearch={!!selectedTag}
        searchPlaceholder="Tìm kiếm dịch vụ..."
        searchValue={searchQuery}
        onSearchChange={setSearchQuery}
      />

      <View
        style={[styles.content, {backgroundColor: theme.colors.background}]}>
        {!isKeyboardVisible && (
          <BranchInfo branch={selectedBranch} onPress={handleBackPress} />
        )}

        {loading ? (
          <LoadingState />
        ) : error ? (
          <ErrorState error={error} onRetry={fetchServices} />
        ) : availableTags.length > 0 && showTagSelection ? (
          <TagList
            tags={availableTags}
            selectedTag={selectedTag}
            onTagSelect={handleTagSelect}
            isKeyboardVisible={isKeyboardVisible}
          />
        ) : selectedTag ? (
          <ServiceList
            services={filteredServices}
            selectedService={selectedService}
            selectedTag={selectedTag}
            searchQuery={searchQuery}
            onServiceSelect={handleServiceSelect}
            onChangeTag={handleChangeTag}
            isKeyboardVisible={isKeyboardVisible}
            hasReturned={hasReturned}
          />
        ) : (
          <EmptyState customMessage="Không có dữ liệu để hiển thị" />
        )}
      </View>

      {selectedService && !isKeyboardVisible && (
        <View style={[styles.bottomContainer]}>
          <Button mode="contained" onPress={handleContinue}>
            Đặt lịch
          </Button>
        </View>
      )}
    </SafeAreaView>
  );
};

export default DrawNumberFieldSelectScreen;
