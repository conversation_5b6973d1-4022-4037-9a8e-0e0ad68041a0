import React, {useState, useEffect, useMemo, useCallback} from 'react';
import {View, StyleSheet, Alert, BackHandler} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useNavigation, useFocusEffect} from '@react-navigation/native';
import {NavigationProp} from '@react-navigation/native';
import {MainStackParamList} from '../../../../navigation/MainNavigator';
import {CCAppBar} from '../../../../components/QNH_CongDanComponents/HeaderComponent/CCAppBar';
import {
  drawNumber,
  Branch,
} from '../../../../requester-biz-service/apis/draw-number-api';
import {useDrawNumberStore} from '../../../../stores/drawNumber.store';
import {normalizeVietnameseText} from '../../../../utils/textUtils';
import {useTheme} from 'react-native-paper';

// Import components
import LoadingState from './components/LoadingState';
import ErrorState from './components/ErrorState';
import BranchList from './components/BranchList';

const DrawNumberBranchSelectScreen: React.FC = () => {
  const theme = useTheme();
  const navigation = useNavigation<NavigationProp<MainStackParamList>>();
  const {setSelectedBranch, selectedBranch, clearSelection} =
    useDrawNumberStore();

  const [branches, setBranches] = useState<Branch[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [hasReturned, setHasReturned] = useState(false);

  const handleBackPress = useCallback(() => {
    // Don't clear selection when going back, preserve state
    navigation.goBack();
  }, [navigation]);

  // Handle hardware back button (Android)
  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        handleBackPress();
        return true; // Prevent default behavior
      };

      const subscription = BackHandler.addEventListener(
        'hardwareBackPress',
        onBackPress,
      );

      return () => subscription.remove();
    }, [handleBackPress]),
  );

  const filteredBranches = useMemo(() => {
    if (!searchQuery.trim()) {
      return branches;
    }

    const normalizedQuery = normalizeVietnameseText(searchQuery.toLowerCase());
    return branches.filter(branch => {
      const normalizedName = normalizeVietnameseText(
        branch.name?.toLowerCase() || '',
      );
      const normalizedAddress = normalizeVietnameseText(
        branch.address?.toLowerCase() || '',
      );

      return (
        normalizedName.includes(normalizedQuery) ||
        normalizedAddress.includes(normalizedQuery)
      );
    });
  }, [branches, searchQuery]);

  const fetchBranches = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await drawNumber.drawNumberListBranch();

      if (response.data?.items) {
        setBranches(response.data.items);
      } else {
        if (response.data) {
          console.log('Response.data structure:', Object.keys(response.data));
        }
      }
    } catch (err: any) {
      console.error('Error fetching branches:', err);
      setError(err.message || 'Đã có lỗi xảy ra khi tải danh sách chi nhánh');
      Alert.alert(
        'Lỗi',
        err.message || 'Đã có lỗi xảy ra khi tải danh sách chi nhánh',
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBranches();
  }, []); // Check if user is returning to this screen with an existing selection
  useEffect(() => {
    // If there's a selected branch when the component mounts,
    // that means we're returning from the next screen
    if (selectedBranch) {
      setHasReturned(true);
    }
  }, [selectedBranch]);

  useEffect(() => {
    clearSelection();
  }, [searchQuery, clearSelection]);

  const handleBranchSelect = (branch: Branch) => {
    clearSelection();
    setSelectedBranch(branch);
    navigation.navigate('DrawNumberFieldSelect');
  };

  // No longer needed as we're using the BranchItem component

  return (
    <SafeAreaView
      style={[styles.container, {backgroundColor: theme.colors.background}]}
      edges={['bottom']}>
      <CCAppBar
        isBack={true}
        showSearch={true}
        searchPlaceholder="Tìm kiếm chi nhánh..."
        searchValue={searchQuery}
        onSearchChange={setSearchQuery}
      />
      <View
        style={[styles.content, {backgroundColor: theme.colors.background}]}>
        {loading ? (
          <LoadingState message="Đang tải danh sách chi nhánh..." />
        ) : error ? (
          <ErrorState error={error} onRetry={fetchBranches} />
        ) : (
          <BranchList
            branches={filteredBranches}
            selectedBranch={selectedBranch}
            hasReturned={hasReturned}
            onSelectBranch={handleBranchSelect}
            searchQuery={searchQuery}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    paddingHorizontal: 12, // Reduced from 16 for better space usage
    paddingTop: 12, // Reduced from 16
  },
});

export default DrawNumberBranchSelectScreen;
