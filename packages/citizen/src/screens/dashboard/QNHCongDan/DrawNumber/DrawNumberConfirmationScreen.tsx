import React, {useState, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  BackHandler,
  ScrollView,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useNavigation, useFocusEffect} from '@react-navigation/native';
import {NavigationProp} from '@react-navigation/native';
import {MainStackParamList} from '../../../../navigation/MainNavigator';
import {CCAppBar} from '../../../../components/QNH_CongDanComponents/HeaderComponent/CCAppBar';
import {textStyles} from '../../../../styles/QNH_textStyle';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  drawNumber,
  DrawNumberRequest,
} from '../../../../requester-biz-service/apis/draw-number-api';
import {useDrawNumberStore} from '../../../../stores/drawNumber.store';
import {
  useUserStore,
  getCitizenCode,
  formatDateOfBirth,
} from '../../../../stores/user.store';
import BranchInfo from './components/BranchInfo';
import ServiceItem from './components/ServiceItem';
import ActionButtons from './components/ActionButtons';
import {useTheme} from 'react-native-paper';

const DrawNumberConfirmationScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp<MainStackParamList>>();
  const {selectedBranch, selectedService, clearSelection} =
    useDrawNumberStore();
  const {userProfile, getDisplayName} = useUserStore();
  const theme = useTheme();

  const [isLoading, setIsLoading] = useState(false);

  // Debug logging for user profile
  // React.useEffect(() => {
  //   console.log('DrawNumberConfirmation - userProfile:', userProfile);
  //   console.log('DrawNumberConfirmation - getDisplayName():', getDisplayName());
  //   console.log(
  //     'DrawNumberConfirmation - getCitizenCode():',
  //     getCitizenCode(userProfile),
  //   );
  // }, [userProfile, getDisplayName]);

  // Helper function to safely get display name with multiple fallbacks
  const getSafeDisplayName = () => {
    // Try the store method first
    const storeDisplayName = getDisplayName();
    if (storeDisplayName) {
      return storeDisplayName;
    }

    // Try direct access if userProfile exists
    if (userProfile) {
      // Handle nested structure
      if ('data' in userProfile && (userProfile as any).data?.fullName) {
        return (userProfile as any).data.fullName;
      }
      // Handle direct structure
      if (userProfile.fullName) {
        return userProfile.fullName;
      }
    }

    return ''; // Return empty string, let the UI handle fallback
  };

  const handleBackPress = useCallback(() => {
    // Keep branch selection but clear service selection when going back
    navigation.goBack();
  }, [navigation]);

  // Handle hardware back button (Android)
  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        handleBackPress();
        return true; // Prevent default behavior
      };

      const subscription = BackHandler.addEventListener(
        'hardwareBackPress',
        onBackPress,
      );

      return () => subscription.remove();
    }, [handleBackPress]),
  );

  const handleCancel = () => {
    navigation.goBack();
  };

  const handleConfirm = async () => {
    if (!selectedBranch || !selectedService) {
      Alert.alert('Lỗi', 'Thiếu thông tin chi nhánh hoặc dịch vụ');
      return;
    }

    try {
      setIsLoading(true);

      // Use actual user data from the store, with fallbacks
      const drawNumberRequest: DrawNumberRequest = {
        branchId: selectedBranch.id,
        serviceId: selectedService.id,
        citizenCode: getCitizenCode(userProfile) || '123456789', // Use actual citizen code or fallback
        fullName: getSafeDisplayName() || 'Nguyễn Văn A', // Use actual name or fallback
        dateOfBirth: formatDateOfBirth(userProfile) || '1990-01-01', // Use actual DOB or fallback
        type: 'NORMAL',
      };

      const response = await drawNumber.createDrawNumber(drawNumberRequest);

      // Navigate directly to TicketDetailScreen with the created ticket ID
      if (response.id) {
        navigation.navigate('TicketDetail', {ticketId: response.id});
        setTimeout(() => {
          clearSelection();
        }, 1000);
      } else {
        // Fallback to DrawNumber screen if no ticket ID
        navigation.navigate('DrawNumber');
      }
    } catch (error: any) {
      console.error('Error creating draw number:', error);
      Alert.alert(
        'Thông báo',
        error.message || 'Đã có lỗi xảy ra khi tạo số chờ. Vui lòng thử lại.',
      );
    } finally {
      setIsLoading(false);
    }
  };

  if (!selectedBranch || !selectedService) {
    return (
      <SafeAreaView
        style={[styles.container, {backgroundColor: theme.colors.background}]}
        edges={['bottom']}>
        <CCAppBar isBack={true} label="Xác nhận" />
        <View style={styles.errorContainer}>
          <View
            style={[
              styles.errorIcon,
              {
                backgroundColor:
                  theme.colors.errorContainer || 'rgba(255, 69, 58, 0.1)',
              },
            ]}>
            <Icon
              name="alert-circle-outline"
              size={48}
              color={theme.colors.error}
            />
          </View>
          <Text
            className={`${textStyles.body1}`}
            style={[styles.errorText, {color: theme.colors.onSurface}]}>
            Thiếu thông tin chi nhánh hoặc dịch vụ. Vui lòng quay lại và chọn
            lại.
          </Text>
          <TouchableOpacity
            style={[
              styles.retryButton,
              {backgroundColor: theme.colors.primary},
            ]}
            onPress={() => navigation.goBack()}>
            <Text
              style={[styles.retryButtonText, {color: theme.colors.onPrimary}]}>
              Quay lại
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      style={[styles.container, {backgroundColor: theme.colors.background}]}
      edges={['bottom']}>
      <CCAppBar isBack={true} label="Xác nhận lấy số chờ?" />
      <View style={styles.content}>
        <ScrollView
          style={[
            styles.scrollView,
            {backgroundColor: theme.colors.background},
          ]}
          showsVerticalScrollIndicator={false}>
          <View
            style={[
              styles.infoCard,
              {
                backgroundColor: theme.colors.background,
                shadowColor: theme.colors.shadow || '#000',
              },
            ]}>
            <View style={styles.cardHeader}>
              <Icon
                name="map-marker-outline"
                size={24}
                color={theme.colors.primary}
              />
              <Text
                className={`${textStyles.h6}`}
                style={[
                  styles.cardHeaderText,
                  {color: theme.colors.onSurface},
                ]}>
                Địa điểm
              </Text>
            </View>
            <BranchInfo
              branch={{
                name: selectedBranch.name,
                address: selectedBranch.address,
                id: selectedBranch.id,
              }}
            />
          </View>

          {/* Service Info Card */}

          <View
            style={[
              styles.infoCard,
              {
                backgroundColor: theme.colors.surface,
                shadowColor: theme.colors.shadow || '#000',
              },
            ]}>
            <View style={styles.cardHeader}>
              <Icon
                name="clipboard-text-outline"
                size={24}
                color={theme.colors.primary}
              />
              <Text
                className={`${textStyles.h6}`}
                style={[
                  styles.cardHeaderText,
                  {color: theme.colors.onSurface},
                ]}>
                Dịch vụ
              </Text>
            </View>
            <ServiceItem
              item={{
                name: selectedService.name,
                description: selectedService.description,
                id: selectedService.id,
              }}
              isSelected={true}
              hasReturned={false}
            />
          </View>

          {/* User Info Card */}
          {userProfile && (
            <View
              style={[
                styles.infoCard,
                {
                  backgroundColor: theme.colors.surface,
                  shadowColor: theme.colors.shadow || '#000',
                },
              ]}>
              <View style={styles.detailsGrid}>
                <View style={styles.detailItem}>
                  <Icon
                    name="account"
                    size={20}
                    color={theme.colors.onSurface}
                  />
                  <View style={styles.detailContent}>
                    <Text
                      className={`${textStyles.body2}`}
                      style={[
                        styles.detailLabel,
                        {color: theme.colors.onSurface},
                      ]}>
                      Họ và tên
                    </Text>
                    <Text
                      className={`${textStyles.body2}`}
                      style={[
                        styles.detailValue,
                        {color: theme.colors.onSurface},
                      ]}>
                      {getSafeDisplayName() || 'Chưa có thông tin'}
                    </Text>
                  </View>
                </View>

                <View style={styles.detailItem}>
                  <Icon
                    name="card-account-details-outline"
                    size={20}
                    color={theme.colors.onSurface}
                  />
                  <View style={styles.detailContent}>
                    <Text
                      className={`${textStyles.body2}`}
                      style={[
                        styles.detailLabel,
                        {color: theme.colors.onSurface},
                      ]}>
                      CCCD/CMND
                    </Text>
                    <Text
                      className={`${textStyles.body2}`}
                      style={[
                        styles.detailValue,
                        {color: theme.colors.onSurface},
                      ]}>
                      {getCitizenCode(userProfile) || 'Chưa có thông tin'}
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          )}
        </ScrollView>

        <View style={[styles.buttonContainer]}>
          <ActionButtons
            isLoading={isLoading}
            onCancel={handleCancel}
            onConfirm={handleConfirm}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  content: {
    flex: 1,
    // paddingHorizontal: 16,
  },
  scrollView: {
    flex: 1,
  },
  headerSection: {
    alignItems: 'center',
    paddingTop: 5,
    paddingBottom: 5,
  },
  iconContainer: {
    marginBottom: 16,
  },
  iconWrapper: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconText: {
    fontSize: 32,
  },
  title: {
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  // New card-style components
  infoCard: {
    marginHorizontal: 8,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 10,
    marginVertical: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  cardHeaderText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  branchName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#007AFF',
    marginBottom: 8,
  },
  serviceName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  serviceDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  detailsGrid: {
    gap: 4,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 4,
    marginBottom: 8,
  },
  detailContent: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 13,
    color: '#666',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 15,
    color: '#333',
    lineHeight: 20,
  },

  // Warning elements
  warningContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFF3CD',
    borderColor: '#FFE69C',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginVertical: 16,
    marginHorizontal: 4,
    alignItems: 'flex-start',
  },
  warningIcon: {
    marginRight: 8,
    marginTop: 1,
  },
  warningText: {
    flex: 1,
    color: '#856404',
    fontSize: 14,
    lineHeight: 20,
  },

  // Legacy styles for backwards compatibility
  scrollContainer: {
    flex: 1,
    paddingTop: 32,
    alignItems: 'center',
  },
  infoContainer: {
    width: '100%',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoRow: {
    marginBottom: 8,
  },
  separator: {
    height: 1,
    backgroundColor: '#e0e0e0',
    marginVertical: 12,
  },
  infoLabel: {
    color: '#333',
    fontWeight: '600',
    marginBottom: 2,
  },
  infoValue: {
    color: '#666',
    lineHeight: 20,
  },

  // Button styles
  buttonContainer: {
    paddingVertical: 4,
    paddingHorizontal: 12,
    // borderTopWidth: 1,
  },
  buttonRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  button: {
    flex: 1,
    flexDirection: 'row',
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 48,
  },
  buttonIcon: {
    marginRight: 6,
  },
  cancelButton: {
    borderWidth: 1,
  },
  buttonSpacing: {
    width: 12,
  },
  buttonText: {
    fontWeight: '600',
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  errorText: {
    color: '#666',
    textAlign: 'center',
    marginVertical: 16,
    fontSize: 16,
    lineHeight: 24,
  },
  errorIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 69, 58, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  retryButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
});

export default DrawNumberConfirmationScreen;
