import React from 'react';
import {ScrollView, StyleSheet, View, Text} from 'react-native';

const TermsScreen = () => {
  return (
    <ScrollView contentContainerStyle={styles.container}>
      <Text style={styles.title}>Điều Khoản Sử Dụng</Text>
      <View style={styles.section}>
        <Text>
          Chào mừng bạn đến với trang web của chúng tôi. Nếu bạn tiếp tục duyệt
          và sử dụng trang web này, bạn đồng ý tuân thủ và bị ràng buộc bởi các
          điều khoản và điều kiện sử dụng dưới đây, cùng với chính sách bảo mật
          của chúng tôi, điều này quản lý mối quan hệ giữa chúng tôi và bạn liên
          quan đến trang web này. Nếu bạn không đồng ý với bất kỳ phần nào của
          các điều khoản và điều kiện này, vui lòng không sử dụng trang web của
          chúng tôi.
        </Text>
      </View>
      <View style={styles.section}>
        <Text>
          Thuật ngữ 'AppTest' hoặc 'chúng tôi' hoặc 'chúng ta' đề cập đến chủ sở
          hữu của trang web có địa chỉ đăng ký là https://nanalingo.com. Thuật
          ngữ 'bạn' đề cập đến người dùng hoặc người xem trang web của chúng
          tôi.
        </Text>
      </View>
      <View style={styles.section}>
        <Text>
          Việc sử dụng trang web này phải tuân theo các điều khoản sử dụng sau
          đây:
        </Text>
        <Text style={styles.listItem}>
          1. Nội dung của các trang trên trang web này chỉ dành cho thông tin
          chung và sử dụng của bạn. Nó có thể thay đổi mà không cần thông báo
          trước.
        </Text>
        <Text style={styles.listItem}>
          2. Trang web này sử dụng cookie để theo dõi sở thích duyệt web. Nếu
          bạn cho phép sử dụng cookie, các thông tin cá nhân sau có thể được lưu
          trữ bởi chúng tôi để sử dụng bởi bên thứ ba.
        </Text>
        <Text style={styles.listItem}>
          3. Chúng tôi và bên thứ ba không cung cấp bất kỳ bảo hành hoặc cam kết
          nào về độ chính xác, tính kịp thời, hiệu suất, đầy đủ hoặc phù hợp của
          thông tin và tài liệu được tìm thấy hoặc cung cấp trên trang web này
          cho bất kỳ mục đích cụ thể nào. Bạn thừa nhận rằng các thông tin và
          tài liệu như vậy có thể chứa sai lệch hoặc lỗi và chúng tôi tuyên bố
          loại trừ trách nhiệm cho bất kỳ sai lệch hoặc lỗi đó đến mức tối đa
          cho phép theo luật pháp.
        </Text>
        <Text style={styles.listItem}>
          4. Việc sử dụng bất kỳ thông tin hoặc tài liệu nào trên trang web này
          là hoàn toàn do rủi ro của bạn, và chúng tôi sẽ không chịu trách
          nhiệm. Điều này sẽ là trách nhiệm của bạn để đảm bảo rằng bất kỳ sản
          phẩm, dịch vụ hoặc thông tin nào có sẵn thông qua trang web này đáp
          ứng đủ yêu cầu cụ thể của bạn.
        </Text>
        <Text style={styles.listItem}>
          5. Trang web này chứa tài liệu thuộc sở hữu của chúng tôi hoặc được
          cấp phép cho chúng tôi. Tài liệu này bao gồm, nhưng không giới hạn,
          thiết kế, bố cục, giao diện, hình ảnh và đồ họa. Sự sao chép là cấm
          chỉ dưới sự tuân thủ của thông báo bản quyền, mà là một phần của các
          điều khoản và điều kiện này.
        </Text>
      </View>
      <View style={styles.section}>
        <Text>
          Việc sử dụng trái phép trang web này có thể dẫn đến yêu cầu bồi thường
          và/hoặc bị coi là hành vi phạm tội.
        </Text>
      </View>
      <View style={styles.section}>
        <Text>
          Thỉnh thoảng, trang web này cũng có thể bao gồm các liên kết đến các
          trang web khác. Những liên kết này được cung cấp để thuận tiện cho bạn
          để cung cấp thêm thông tin. Chúng không ngụ ý rằng chúng tôi ủng hộ
          các trang web đó. Chúng tôi không chịu trách nhiệm về nội dung của các
          trang web liên kết đó.
        </Text>
      </View>
      <View style={styles.section}>
        <Text>
          Việc sử dụng của bạn đối với trang web này và bất kỳ tranh chấp nào
          phát sinh từ việc sử dụng trang web này đều tuân thủ các luật pháp của
          nước Cộng Hoà Xã Hội Chủ Nghĩa Việt Nam.
        </Text>
      </View>
    </ScrollView>
  );
};
const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
  },
  subTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 15,
    marginBottom: 5,
  },
  section: {
    marginBottom: 15,
  },
  listItem: {
    marginLeft: 10,
    marginBottom: 5,
  },
});

export default TermsScreen;
