import React, {useState} from 'react';
import {useMutation} from 'react-query';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
} from 'react-native';

import {Button, useTheme, Avatar, Portal, Dialog} from 'react-native-paper';
import {StepMenuVertical, IOptionMenuItem} from '../../components/AcStepMenu';
import {FOOTER_HEIGHT} from '../../styles';
import {CCAppBar} from '../../components/QNH_CongDanComponents/HeaderComponent/CCAppBar';
import {useNavigation, NavigationProp} from '@react-navigation/native';
import {MainStackParamList} from '../../navigation/MainNavigator';
import {useSubmitFlow} from '../../hooks/useSubmitFlow';
import {useCommonProcedure} from '../../stores';
import {template} from '../../requester-biz-service/apis/template-api';
import {getCitizenCode, useUserStore} from '../../stores/user.store';
import {useForm, FormProvider} from 'react-hook-form';
import {RHFTextField} from '../../components/RHFCustomComponent/RHFTextField';
import {RHFDatePicker} from '../../components/RHFCustomComponent';

const optionSteps: IOptionMenuItem[] = [
  {id: 1, name: 'Chọn vai trò thực hiện'},
  {id: 2, name: 'Kê khai biểu mẫu điện tử'},
  {id: 3, name: 'Đính kèm giấy tờ'},
  {id: 4, name: 'Nhận kết quả & thanh toán'},
];

const roleOptions = [
  {label: 'Bản Thân', value: 'Bản Thân', icon: 'account'},
  {label: 'Khai hộ', value: 'Khai hộ', icon: 'account-multiple'},
  {label: 'Doanh Nghiệp', value: 'Doanh Nghiệp', icon: 'domain'},
];

export const StepRoleChoose = () => {
  const methods = useForm({mode: 'onChange'});
  const {
    setValue,
    watch,
    formState: {isValid},
    reset,
  } = methods;
  const theme = useTheme();
  const [currentStep] = useState<number>(1);
  const [selectedRole, setSelectedRole] = useState<string>('');
  const [showKhaiHoDialog, setShowKhaiHoDialog] = useState<boolean>(false);
  const [showDoanhNghiepDialog, setShowDoanhNghiepDialog] =
    useState<boolean>(false);
  const nav = useNavigation<NavigationProp<MainStackParamList>>();
  const addItemToArray = useSubmitFlow(state => state.addItemToArray);
  const removeItemFromArray = useSubmitFlow(state => state.removeItemFromArray);
  const hasFormFieldInTemplateDetail = useCommonProcedure(
    state => state.hasFormFieldInTemplateDetail,
  );
  const {userProfile} = useUserStore();
  const forwardData = useCommonProcedure(state => state.forwardData);
  const formId = useCommonProcedure(state => state.formId);
  const formName = useCommonProcedure(state => state.formName);
  const templateDetail = useCommonProcedure(state => state.templateDetail);
  const displayName = useUserStore(state => state.getDisplayName());
  const stepOneFormData = useSubmitFlow(state => state.stepOneFormData);

  const initUserFormMutation = useMutation(
    async (payload: any) => {
      return await template.initUserForm({data: payload});
    },
    {
      onSuccess: data => {
        nav.navigate('StepTwo', {userFormId: data?.data.data.userFormId});
      },
    },
  );

  // Watch form fields for Khai hộ
  const uyQuyenHoTen = watch('uyQuyenHoTen') || '';
  const uyQuyenNgaySinh = watch('uyQuyenNgaySinh') || '';
  const uyQuyenSoGiayTo = watch('uyQuyenSoGiayTo') || '';
  const uyQuyenMoiQuanHe = watch('uyQuyenMoiQuanHe') || '';
  // Watch form fields for Doanh Nghiệp
  const doanhNghiepTen = watch('doanhNghiepTen') || '';
  const doanhNghiepSoDKKD = watch('doanhNghiepSoDKKD') || '';
  const doanhNghiepMaSoThue = watch('doanhNghiepMaSoThue') || '';
  const doanhNghiepDiaChi = watch('doanhNghiepDiaChi') || '';
  const doanhNghiepSoDienThoai = watch('doanhNghiepSoDienThoai') || '';

  const isKhaiHoFormValid =
    uyQuyenHoTen.trim() &&
    uyQuyenNgaySinh &&
    uyQuyenSoGiayTo.trim() &&
    uyQuyenMoiQuanHe.trim();
  const isDoanhNghiepFormValid =
    doanhNghiepTen.trim() &&
    doanhNghiepSoDKKD.trim() &&
    doanhNghiepMaSoThue.trim() &&
    doanhNghiepDiaChi.trim() &&
    doanhNghiepSoDienThoai.trim();

  const handleRoleSelect = (role: string) => {
    setSelectedRole(role);
    if (role === 'Khai hộ') {
      setShowKhaiHoDialog(true);
    } else if (role === 'Doanh Nghiệp') {
      setShowDoanhNghiepDialog(true);
    }
  };

  const handleKhaiHoDialogClose = () => {
    setShowKhaiHoDialog(false);
    // Reset form values for Khai hộ
    setValue('uyQuyenHoTen', '');
    setValue('uyQuyenNgaySinh', '');
    setValue('uyQuyenSoGiayTo', '');
    setValue('uyQuyenMoiQuanHe', '');
  };

  const handleDoanhNghiepDialogClose = () => {
    setShowDoanhNghiepDialog(false);
    // Reset form values for Doanh Nghiệp
    setValue('doanhNghiepTen', '');
    setValue('doanhNghiepSoDKKD', '');
    setValue('doanhNghiepMaSoThue', '');
    setValue('doanhNghiepDiaChi', '');
    setValue('doanhNghiepSoDienThoai', '');
  };

  const handleSubmit = () => {
    // add email and password to item at submit flow define
    const emailItem = templateDetail?.data.find(
      (item: {dataType: string}) => item.dataType === 'email',
    );
    if (emailItem) {
      addItemToArray({
        code: emailItem.code,
        name: emailItem.name,
        dataType: emailItem.dataType,
        required: emailItem.required,
        value: stepOneFormData?.email || '',
      });
    }
    const phoneItem = templateDetail?.data.find(
      (item: {dataType: string}) => item.dataType === 'phone',
    );
    if (phoneItem) {
      addItemToArray({
        code: phoneItem.code,
        name: phoneItem.name,
        dataType: phoneItem.dataType,
        required: phoneItem.required,
        value: stepOneFormData?.phoneNumber || '',
      });
    }
    // Next logic
    if (selectedRole === 'Khai hộ') {
      // Convert uyQuyenNgaySinh to YYYY-MM-DD if needed
      let formattedUyQuyenNgaySinh = uyQuyenNgaySinh;
      if (uyQuyenNgaySinh) {
        // If it's a Moment object
        if (uyQuyenNgaySinh._isAMomentObject) {
          formattedUyQuyenNgaySinh = uyQuyenNgaySinh.format('YYYY-MM-DD');
        } else if (typeof uyQuyenNgaySinh === 'string') {
          if (uyQuyenNgaySinh.includes('/')) {
            const parts = uyQuyenNgaySinh.split('/');
            if (parts.length === 3) {
              const yyyy = parts[2];
              const mm = parts[1].padStart(2, '0');
              const dd = parts[0].padStart(2, '0');
              formattedUyQuyenNgaySinh = `${yyyy}-${mm}-${dd}`;
            }
          } else {
            // Parse ISO or other string formats
            const date = new Date(uyQuyenNgaySinh);
            if (!isNaN(date.getTime())) {
              const yyyy = date.getFullYear();
              const mm = String(date.getMonth() + 1).padStart(2, '0');
              const dd = String(date.getDate()).padStart(2, '0');
              formattedUyQuyenNgaySinh = `${yyyy}-${mm}-${dd}`;
            } else if (
              uyQuyenNgaySinh.includes('-') &&
              uyQuyenNgaySinh.length >= 10
            ) {
              // Fallback: take first 10 chars
              formattedUyQuyenNgaySinh = uyQuyenNgaySinh.substring(0, 10);
            }
          }
        } else if (uyQuyenNgaySinh instanceof Date) {
          // If it's a Date object
          const yyyy = uyQuyenNgaySinh.getFullYear();
          const mm = String(uyQuyenNgaySinh.getMonth() + 1).padStart(2, '0');
          const dd = String(uyQuyenNgaySinh.getDate()).padStart(2, '0');
          formattedUyQuyenNgaySinh = `${yyyy}-${mm}-${dd}`;
        }
      }
      removeItemFromArray('_thongTinUyQuyenDoanhNghiep');
      removeItemFromArray('_taiLieuUyQuyenDoanhNghiep');
      addItemToArray({
        code: '_taiLieuUyQuyenCaNhan',
        name: 'Tài liệu uỷ quyền cá nhân',
        dataType: 'uri',
        required: true,
        isAttachmentUri: true,
      });

      addItemToArray({
        code: '_thongTinUyQuyenCaNhan',
        name: 'UyQuyen',
        dataType: 'json',
        isExternalData: true,
        required: true,
        value: {
          HoTen: uyQuyenHoTen,
          NgayThangNamSinh: formattedUyQuyenNgaySinh,
          QuanHe: uyQuyenMoiQuanHe,
          SoGiayTo: uyQuyenSoGiayTo,
        },
      });
    } else if (selectedRole === 'Doanh Nghiệp') {
      removeItemFromArray('_thongTinUyQuyenCaNhan');
      removeItemFromArray('_taiLieuUyQuyenCaNhan');
      addItemToArray({
        code: '_taiLieuUyQuyenDoanhNghiep',
        name: 'Tài liệu uỷ quyền doanh nghiệp',
        dataType: 'uri',
        required: true,
        isAttachmentUri: true,
      });
      addItemToArray({
        code: '_thongTinUyQuyenDoanhNghiep',
        name: 'UyQuyen',
        dataType: 'json',
        isExternalData: true,
        required: true,
        value: {
          TenDoanhNghiep: doanhNghiepTen,
          GiayChungNhan_DKKD: doanhNghiepSoDKKD,
          Email_SDT: doanhNghiepSoDienThoai,
          MaSoThue: doanhNghiepMaSoThue,
          ThongTinLienHe: doanhNghiepDiaChi,
        },
      });
    } else if (selectedRole === 'Bản Thân') {
      removeItemFromArray('_thongTinUyQuyenCaNhan');
      removeItemFromArray('_taiLieuUyQuyenCaNhan');
      removeItemFromArray('_thongTinUyQuyenDoanhNghiep');
      removeItemFromArray('_taiLieuUyQuyenDoanhNghiep');
    }
    if (hasFormFieldInTemplateDetail && templateDetail) {
      // Format DateOfProvision to yyyy-MM-dd
      let formattedDate = '';
      if (stepOneFormData?.DateOfProvision) {
        const date = new Date(stepOneFormData.DateOfProvision);
        if (!isNaN(date.getTime())) {
          const yyyy = date.getFullYear();
          const mm = String(date.getMonth() + 1).padStart(2, '0');
          const dd = String(date.getDate()).padStart(2, '0');
          formattedDate = `${yyyy}-${mm}-${dd}`;
        }
      }
      // Format DateOfBirth from DD/MM/YYYY to YYYY-MM-DD
      console.log(stepOneFormData?.DateOfProvision);
      let formattedBirthDate = '';
      if (stepOneFormData?.DateOfBirth) {
        const parts = stepOneFormData.DateOfBirth.split('/');
        if (parts.length === 3) {
          const yyyy = parts[2];
          const mm = parts[1].padStart(2, '0');
          const dd = parts[0].padStart(2, '0');
          formattedBirthDate = `${yyyy}-${mm}-${dd}`;
        }
      }
      // Get current date in YYYY-MM-DD format
      const now = new Date();
      const nowYYYY = now.getFullYear();
      const nowMM = String(now.getMonth() + 1).padStart(2, '0');
      const nowDD = String(now.getDate()).padStart(2, '0');
      const currentDate = `${nowYYYY}-${nowMM}-${nowDD}`;

      const payload = {
        formId: formId || '',
        name: formName || '',
        jsonData: {
          noiGui: forwardData?.TenDonViThucHien || '',
          nycHoTen: displayName || '',
          nycDiaChiCuTru: '',
          nycLoaiGiayToTuyThan: stepOneFormData?.DocumentType.value.value,
          nycSoGiayTo: stepOneFormData?.CitizenCode,
          nycNgayCapGiayTo: formattedDate,
          nycNoiCapGiayTo: stepOneFormData?.PlaceOfIssues.value.value,
          nycGioiTinh: stepOneFormData?.Sex.value.value,
          nycNgaySinh: formattedBirthDate,
          nycQuocTich: stepOneFormData?.Nationality.value.value || '',
          nycDanToc: stepOneFormData?.Nation.value.value,
          nycTonGiao: stepOneFormData?.Religion.value.value,
          nycEmail: stepOneFormData?.email || '',
          nycSoDienThoai: stepOneFormData?.phoneNumber || '',
          ngayTao: currentDate,
          quanHe: selectedRole,
          tenCapThucHien: forwardData?.CapThucHien,
        },

        ownerId: getCitizenCode(userProfile), // Fill with actual ownerId if available
      };
      initUserFormMutation.mutate(payload);
      // nav.navigate('StepTwo');
    } else {
      nav.navigate('StepThree');
    }
  };

  const canProceed = () => {
    if (!selectedRole) return false;
    if (selectedRole === 'Bản Thân') return true;
    if (selectedRole === 'Khai hộ') return isKhaiHoFormValid;
    if (selectedRole === 'Doanh Nghiệp') return isDoanhNghiepFormValid;
    return false;
  };

  return (
    <View style={[styles.container, {backgroundColor: theme.colors.surface}]}>
      <CCAppBar label="Nộp hồ sơ" isBack={true} />
      <StepMenuVertical currentStep={currentStep} options={optionSteps} />

      <ScrollView
        style={styles.scrollContent}
        contentContainerStyle={styles.scrollContentContainer}
        keyboardShouldPersistTaps="handled">
        <FormProvider {...methods}>
          <View style={styles.content}>
            <Text style={styles.title}>Chọn vai trò thực hiện</Text>
            <Text style={styles.subtitle}>
              Vui lòng chọn một vai trò phù hợp để tiếp tục
            </Text>
            <View style={styles.roleList}>
              {roleOptions.map(option => (
                <TouchableOpacity
                  key={option.value}
                  style={[
                    styles.roleOption,
                    selectedRole === option.value && {
                      borderColor: theme.colors.primary,
                      backgroundColor: theme.colors.primaryContainer,
                      shadowColor: theme.colors.primary,
                      shadowOpacity: 0.15,
                      shadowRadius: 8,
                      elevation: 2,
                    },
                  ]}
                  activeOpacity={0.7}
                  onPress={() => handleRoleSelect(option.value)}>
                  <Avatar.Icon
                    size={36}
                    icon={selectedRole === option.value ? 'check' : option.icon}
                    style={
                      selectedRole === option.value
                        ? [
                            styles.selectedIcon,
                            {backgroundColor: theme.colors.primary},
                          ]
                        : styles.icon
                    }
                    color={theme.colors.onPrimary}
                  />
                  <Text
                    style={[
                      styles.radioLabel,
                      selectedRole === option.value && {
                        color: theme.colors.onPrimaryContainer,
                        fontWeight: 'bold',
                      },
                    ]}>
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </FormProvider>
      </ScrollView>

      <View style={[styles.footer, {backgroundColor: theme.colors.surface}]}>
        <Button
          mode="outlined"
          style={styles.button}
          onPress={() => {
            nav.goBack();
          }}>
          Quay lại
        </Button>
        <Button
          mode="contained"
          style={styles.button}
          disabled={!canProceed()}
          onPress={handleSubmit}>
          Tiếp theo
        </Button>
      </View>

      {/* Khai hộ Dialog */}
      <Portal>
        <Dialog
          visible={showKhaiHoDialog}
          onDismiss={() => {
            setSelectedRole('');
            handleKhaiHoDialogClose();
          }}
          style={styles.dialog}>
          <FormProvider {...methods}>
            <Dialog.Title style={styles.dialogTitle}>
              Thông tin người uỷ quyền
            </Dialog.Title>
            <Dialog.ScrollArea style={styles.dialogScrollArea}>
              <ScrollView contentContainerStyle={styles.dialogContent}>
                <View style={styles.formRow}>
                  <RHFTextField
                    name="uyQuyenHoTen"
                    label="Họ Tên người Uỷ Quyền"
                    placeholder="Nhập họ tên"
                    required
                  />
                </View>
                <View style={styles.formRow}>
                  <RHFDatePicker
                    name="uyQuyenNgaySinh"
                    label="Ngày tháng năm sinh"
                    placeholder="YYYY-MM-DD"
                    required
                  />
                </View>
                <View style={styles.formRow}>
                  <RHFTextField
                    name="uyQuyenSoGiayTo"
                    label="Số giấy tờ"
                    placeholder="Nhập số giấy tờ"
                    required
                  />
                </View>
                <View style={styles.formRow}>
                  <RHFTextField
                    name="uyQuyenMoiQuanHe"
                    label="Mối quan hệ với người uỷ quyền"
                    placeholder="Nhập mối quan hệ"
                    required
                  />
                </View>
              </ScrollView>
            </Dialog.ScrollArea>
            <Dialog.Actions style={styles.dialogActions}>
              <Button
                mode="outlined"
                onPress={() => {
                  setSelectedRole('');
                  handleKhaiHoDialogClose();
                }}
                style={styles.dialogButton}>
                Hủy
              </Button>
              <Button
                mode="contained"
                disabled={!isKhaiHoFormValid}
                onPress={() => {
                  setShowKhaiHoDialog(false);
                  handleSubmit();
                }}
                style={styles.dialogButton}>
                Xác nhận
              </Button>
            </Dialog.Actions>
          </FormProvider>
        </Dialog>
      </Portal>

      {/* Doanh nghiệp Dialog */}
      <Portal>
        <Dialog
          visible={showDoanhNghiepDialog}
          onDismiss={() => {
            setSelectedRole('');
            handleDoanhNghiepDialogClose();
          }}
          style={styles.dialog}>
          <FormProvider {...methods}>
            <Dialog.Title style={styles.dialogTitle}>
              Thông tin doanh nghiệp uỷ quyền
            </Dialog.Title>
            <Dialog.ScrollArea style={styles.dialogScrollArea}>
              <ScrollView contentContainerStyle={styles.dialogContent}>
                <View style={styles.formRow}>
                  <RHFTextField
                    name="doanhNghiepTen"
                    label="Tên Doanh nghiệp uỷ quyền"
                    placeholder="Nhập tên doanh nghiệp"
                    required
                  />
                </View>
                <View style={styles.formRow}>
                  <RHFTextField
                    name="doanhNghiepSoDKKD"
                    label="Số giấy chứng nhận đăng ký kinh doanh"
                    placeholder="Nhập số giấy chứng nhận đăng ký kinh doanh"
                    required
                  />
                </View>
                <View style={styles.formRow}>
                  <RHFTextField
                    name="doanhNghiepMaSoThue"
                    label="Mã số Thuế"
                    placeholder="Nhập mã số thuế"
                    required
                  />
                </View>
                <View style={styles.formRow}>
                  <RHFTextField
                    name="doanhNghiepDiaChi"
                    label="Địa chỉ trụ sở"
                    placeholder="Nhập địa chỉ trụ sở"
                    required
                  />
                </View>
                <View style={styles.formRow}>
                  <RHFTextField
                    name="doanhNghiepSoDienThoai"
                    label="Số điện thoại"
                    placeholder="Nhập số điện thoại"
                    required
                  />
                </View>
              </ScrollView>
            </Dialog.ScrollArea>
            <Dialog.Actions style={styles.dialogActions}>
              <Button
                mode="outlined"
                onPress={() => {
                  setSelectedRole('');
                  handleDoanhNghiepDialogClose();
                }}
                style={styles.dialogButton}>
                Hủy
              </Button>
              <Button
                mode="contained"
                disabled={!isDoanhNghiepFormValid}
                onPress={() => setShowDoanhNghiepDialog(false)}
                style={styles.dialogButton}>
                Xác nhận
              </Button>
            </Dialog.Actions>
          </FormProvider>
        </Dialog>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  stepMenuVertical: {
    marginTop: 4,
    marginBottom: 4,
  },
  scrollContent: {
    flex: 1,
  },
  scrollContentContainer: {
    flexGrow: 1,
    paddingBottom: FOOTER_HEIGHT + 16, // Add padding to prevent overlap with footer
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  roleList: {
    width: '100%',
    maxWidth: 400,
    alignSelf: 'center',
    marginTop: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 24,
    textAlign: 'center',
  },
  roleOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingVertical: 12,
    paddingHorizontal: 12,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: 'transparent',
    backgroundColor: 'transparent',
  },
  icon: {
    marginRight: 8,
  },
  selectedIcon: {
    marginRight: 8,
  },
  radioLabel: {
    fontSize: 16,
    marginLeft: 8,
    fontWeight: '500',
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: FOOTER_HEIGHT,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowOffset: {width: 0, height: -2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
    gap: 4,
  },
  button: {
    flex: 1,
  },
  dialog: {
    maxHeight: '80%',
  },
  dialogTitle: {
    textAlign: 'center',
    fontSize: 18,
    fontWeight: 'bold',
  },
  dialogScrollArea: {
    maxHeight: 400,
    paddingHorizontal: 0,
  },
  dialogContent: {
    paddingHorizontal: 24,
    paddingVertical: 8,
  },
  dialogActions: {
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  dialogButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  formRow: {
    marginBottom: 16,
  },
});

export default StepRoleChoose;
