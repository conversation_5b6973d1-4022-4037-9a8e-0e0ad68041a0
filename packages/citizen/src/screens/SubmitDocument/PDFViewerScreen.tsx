import React, {useEffect} from 'react';
import {View, StyleSheet, Platform, BackHandler} from 'react-native';
import {CCAppBar} from '../../components/QNH_CongDanComponents/HeaderComponent/CCAppBar';
import {useTheme} from 'react-native-paper';
import {WebView} from 'react-native-webview';
import Pdf from 'react-native-pdf';
import {RouteProp, useRoute, useNavigation} from '@react-navigation/native';
import {MainStackParamList} from '../../navigation/MainNavigator';

type PDFViewerScreenRouteProp = RouteProp<
  MainStackParamList,
  'PDFViewerScreen'
>;

// type NavigationProp = NativeStackNavigationProp<MainStackParamList>;

const PDFViewerScreen = () => {
  const route = useRoute<PDFViewerScreenRouteProp>();
  const theme = useTheme();
  const {pdfPath} = route.params;
  const navigation = useNavigation();

  useEffect(() => {
    const onBackPress = () => {
      navigation.goBack();
      return true;
    };
    if (Platform.OS === 'android') {
      BackHandler.addEventListener('hardwareBackPress', onBackPress);
      return () => {
        BackHandler.removeEventListener('hardwareBackPress', onBackPress);
      };
    }
  }, [navigation]);

  return (
    <View
      style={[styles.container, {backgroundColor: theme.colors.background}]}>
      <CCAppBar label="Xem PDF" isBack={true} />
      {Platform.OS === 'ios' ? (
        <WebView
          source={{uri: `file://${pdfPath}`}}
          style={styles.webview}
          useWebKit={true}
          originWhitelist={['*']}
          allowFileAccess
          allowUniversalAccessFromFileURLs
        />
      ) : (
        <Pdf
          source={{uri: `file://${pdfPath}`}}
          style={styles.webview}
          trustAllCerts={true}
          onError={error => {
            console.log('PDF load error:', error);
          }}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  webview: {
    flex: 1,
    marginTop: 8,
  },
});

export default PDFViewerScreen;
