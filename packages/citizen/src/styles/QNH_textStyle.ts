export const textOverflow = 'truncate';

export const fontSize = {
  'text-12': 12,
  'text-13': 13,
  'text-14': 14,
  'text-17': 17,
};

export const colors = {
  textWhite: '#FFF',
  textPrimary: '#212B36',
  textSecondary: '#637381',
  outLine: 'bg-custom-gray bg-opacity-20',
};

export const textStyles = {
  h5: 'text-18px font-bold text-primary-text leading-[27px]',
  h6: 'text-17px font-bold text-primary-text leading-[26px]',
  body1: 'text-16px font-sans text-sm font-normal leading-[24px]',
  body2: 'font-sans text-sm font-normal leading-[22px]',
  textSmall: 'text-13px font-bold text-primary-text leading-[22px]',
  textLarge: 'text-15px font-bold text-primary-text leading-[26px]',
  chipLabel:
    'text-secondary text-center font-medium text-[13px] leading-[18px]',
  subTitle2: 'font-semibold text-[14px] leading-[22px] text-primary-text',
  subTitle1: 'font-semibold text-[16px] leading-[24px] text-primary-text',
  label: 'text-[12px] font-bold leading-[20px] text-primary-text',
};

export const card = {
  header: `${textStyles.h6} ${colors.textPrimary}`,
};

export const appBar = {
  bg: '#366AE2',
  title: `${textStyles.h6} text-white`,
  subTitle: `${textStyles.body2} ${colors.textWhite} ${textOverflow}`,
};
