import {StyleSheet, TextStyle} from 'react-native';

export const textStyles = {
  text_12px_bold: 'text-xs/[18px] font-bold text-primary-text',
  text_12px_regular: 'text-xs/[18px] font-normal text-primary-text',
  text_12px_medium: 'text-xs/[18px] font-semibold text-primary-text',
  text_13px_regular: 'text-13px font-normal text-primary-text',
  text_13px_medium: 'text-13px font-semibold text-primary-text',
  text_13px_bold: 'text-13px font-bold text-primary-text',
  text_14px_regular: 'text-sm font-normal text-primary-text leading-[22px]',
  text_14px_medium: 'text-sm font-semibold text-primary-text leading-[22px]',
  text_14px_bold: 'text-sm font-bold text-primary-text leading-[22px]',
  text_15px_regular: 'text-15px font-normal text-primary-text',
  text_15px_medium: 'text-15px font-semibold text-primary-text',
  text_15px_bold: 'text-15px font-bold text-primary-text',
  text_16px_regular: 'text-base font-normal text-primary-text',
  text_16px_medium: 'text-base font-semibold text-primary-text',
  text_16px_bold: 'text-base font-bold text-primary-text',
  text_17px_regular: 'text-17px font-normal text-primary-text',
  text_17px_medium: 'text-17px font-semibold text-primary-text',
  text_17px_bold: 'text-17px font-bold text-primary-text',
  text_20px_bold: 'text-xl font-bold text-primary-text',
};

interface TextStylesProps {
  text_14px_bold: TextStyle;
  text_15px_bold: TextStyle;
}

export const textInlineStyles: TextStylesProps = StyleSheet.create({
  text_14px_bold: {
    fontSize: 14,
    lineHeight: 24,
    color: '#212B36',
    fontWeight: 'bold',
  },

  text_15px_bold: {
    fontSize: 15,
    lineHeight: 26,
    color: '#212B36',
    fontWeight: 'bold',
  },
});
