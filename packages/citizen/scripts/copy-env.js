const fs = require('fs');
const path = require('path');

const ENV_MAP = {
  dev: 'dev',
  uat: 'uat',
  prod: 'prod',
};

const copyEnvConfig = env => {
  if (!ENV_MAP[env]) {
    console.error('Invalid environment. Please use: dev, uat, or prod');
    process.exit(1);
  }

  const sourceFile = path.join(
    __dirname,
    '..',
    'src',
    'api',
    `api-config.${env}.ts`,
  );
  const targetFile = path.join(__dirname, '..', 'src', 'api', 'api-config.ts');

  try {
    if (fs.existsSync(targetFile)) {
      fs.unlinkSync(targetFile);
    }
    fs.copyFileSync(sourceFile, targetFile);
    console.log(`Successfully copied ${env} configuration`);
  } catch (error) {
    console.error('Error copying configuration:', error);
    process.exit(1);
  }
};

const env = process.argv[2];
copyEnvConfig(env);
