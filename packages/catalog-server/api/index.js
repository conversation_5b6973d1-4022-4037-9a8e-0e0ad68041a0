const express = require('express');

// if running on vercel, use generated .prod configs
const env =
  process.env.VERCEL || process.env.NODE_ENV === 'production'
    ? '.prod'
    : '.dev';

const suffix = env + '.json';

const host = require('../data/host' + suffix);
const dashboard = require('../data/dashboard' + suffix);

const app = express();
const port = process.env.PORT ?? 3000;

app.get('/version-android', (_, res) => {
  res.send({
    version: '0.0.2',
    message:
      'Chúng tôi đã cải tiến và bổ sung một số tính năng mới để mang đến trải nghiệm tốt hơn cho bạn. Hãy cập nhật ngay bây giờ để khám phá',
  });
});

app.get('/version-ios', (_, res) => {
  res.send({
    version: '0.0.2',
    message:
      '<PERSON><PERSON>g tôi đã cải tiến và bổ sung một số tính năng mới để mang đến trải nghiệm tốt hơn cho bạn. Hãy cập nhật ngay bây giờ để khám phá',
  });
});

app.get('/host', (req, res) => {
  const platform = req.query.platform;
  const appVersion = req.query.appVersion;

  res.send(host[platform][appVersion]);
});

app.get('/dashboard', (req, res) => {
  const platform = req.query.platform;
  const appVersion = req.query.appVersion;

  res.send(dashboard[platform][appVersion]);
});

app.listen(port, () => {
  console.log(`[CatalogServer] Server listening at port ${port}`);
});

module.exports = app;
