const packageDashboard = require('../packages/host/package.json');
const dependencies = require('../packages/sdk/lib/dependencies.json');
const devDependencies = require('../packages/sdk/lib/devDependencies.json');
const fs = require('fs');

Object.keys(packageDashboard.devDependencies).map((i) => {
  devDependencies[i] = {
    name: i,
    version: packageDashboard.devDependencies[i],
    devOnly: true,
  };
});

Object.keys(packageDashboard.dependencies).map((i) => {
  dependencies[i] = {
    name: i,
    version: packageDashboard.dependencies[i],
  };
});

fs.writeFileSync(
  './packages/sdk/lib/dependencies.json',
  JSON.stringify(dependencies, null, 2)
);

fs.writeFileSync(
  './packages/sdk/lib/devDependencies.json',
  JSON.stringify(devDependencies, null, 2)
);
