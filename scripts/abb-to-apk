#! /bin/bash

export date=$(date '+%Y%m%d--%H%M%S')

java -jar ./bundletool-all-1.17.1.jar build-apks --bundle=./packages/host/android/app/build/outputs/bundle/release/app-release.aab --output="./tmp/app_test_$date.apks" --ks=./packages/host/android/app/my-upload-key.keystore --ks-pass=pass:"onthemoon@123!!" --ks-key-alias=my-key-alias --key-pass="pass:onthemoon@123!!" --mode=universal

unzip -p "./tmp/app_test_$date.apks" universal.apk > "./tmp/app_test_$date.apk"

