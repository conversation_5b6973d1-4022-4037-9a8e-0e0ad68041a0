#! /bin/bash

export date=$(date '+%Y%m%d--%H%M%S')
export FIREBASE_APP_ANDROID_ID="1:617462185423:android:c74bc19c6e64cc3c6ee6ba"

# Copy env production
pnpm copy:prod
# Bundle
pnpm bundle:qn:host:android
# Build Production
pnpm build:android:prod

java -jar ./bundletool-all-1.17.1.jar build-apks --bundle=./packages/host/android/app/build/outputs/bundle/release/app-release.aab --output="./tmp/app_test_$date.apks" --ks=./packages/host/android/app/my-upload-key.keystore --ks-pass=pass:"onthemoon@123!!" --ks-key-alias=my-key-alias --key-pass="pass:onthemoon@123!!" --mode=universal

unzip -p "./tmp/app_test_$date.apks" universal.apk > "./tmp/app_test_$date.apk"

firebase appdistribution:distribute "./tmp/app_test_$date.apk"  \
    --app "$FIREBASE_APP_ANDROID_ID"  \
    --release-notes "Something new android" --groups "fis" \
    --token "$FIREBASE_TOKEN"

