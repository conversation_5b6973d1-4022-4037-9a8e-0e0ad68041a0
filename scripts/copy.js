const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

const COPY_CONFIGS = {
  api: {
    operations: [
      {
        from: './packages/dashboard/src/api',
        to: ['./packages/host/src/api', './packages/auth/src/api']
      }
    ]
  },
  assets: {
    operations: [
      {
        from: './packages/dashboard/src/assets',
        to: ['./packages/host/src/assets', './packages/auth/src/assets']
      }
    ]
  },
  icons: {
    operations: [
      {
        from: './packages/dashboard/src/icons',
        to: ['./packages/host/src/icons', './packages/auth/src/icons']
      }
    ]
  },
  lotties: {
    operations: [
      {
        from: './packages/host/src/lotties',
        to: ['./packages/dashboard/src/lotties', './packages/auth/src/lotties']
      }
    ]
  },
  screens: {
    operations: [
      {
        from: './packages/dashboard/src/screens/dashboard',
        to: ['./packages/host/src/screens/dashboard']
      }
    ]
  },
  stores: {
    operations: [
      {
        from: './packages/dashboard/src/stores/dashboard',
        to: ['./packages/host/src/stores/dashboard']
      }
    ]
  },
  utils: {
    operations: [
      {
        from: './packages/host/src/utils',
        to: ['./packages/dashboard/src/utils']
      }
    ]
  },
  config: {
    operations: [
      {
        type: 'config',
        packages: ['auth', 'dashboard']
      }
    ]
  }
};

const copyFiles = (from, to) => {
  if (!fs.existsSync(from)) {
    console.error(`Source directory doesn't exist: ${from}`);
    return;
  }

  to.forEach(target => {
    if (fs.existsSync(target)) {
      fs.rmSync(target, { recursive: true, force: true });
    }
    fs.mkdirSync(path.dirname(target), { recursive: true });
    fs.cpSync(from, target, { recursive: true });
    console.log(`Copied from ${from} to ${target}`);
  });
};

const copyConfig = (env, packages) => {
  packages.forEach(pkg => {
    const targetFile = `./packages/${pkg}/src/api/api-config.ts`;
    const sourceFile = `./packages/${pkg}/src/api/api-config.${env}.ts`;

    if (fs.existsSync(targetFile)) {
      fs.unlinkSync(targetFile);
    }
    fs.copyFileSync(sourceFile, targetFile);
    console.log(`Copied ${env} config for ${pkg}`);
  });
};

const executeCopy = (type, env) => {
  const config = COPY_CONFIGS[type];
  if (!config) {
    console.error(`Unknown copy type: ${type}`);
    process.exit(1);
  }

  config.operations.forEach(op => {
    if (op.type === 'config' && env) {
      copyConfig(env, op.packages);
    } else if (op.from && op.to) {
      copyFiles(op.from, op.to);
    }
  });
};

const main = () => {
  const [type, env] = process.argv.slice(2);
  
  if (!type) {
    console.error('Please specify what to copy');
    console.log('Available options:', Object.keys(COPY_CONFIGS).join(', '));
    process.exit(1);
  }

  if (type === 'all') {
    Object.keys(COPY_CONFIGS).forEach(key => {
      if (key !== 'config') {
        executeCopy(key);
      }
    });
  } else {
    executeCopy(type, env);
  }
};

main();