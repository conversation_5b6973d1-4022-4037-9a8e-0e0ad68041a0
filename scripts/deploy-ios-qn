#! /bin/bash

export date=$(date '+%Y%m%d--%H%M%S')
export FIREBASE_APP_IOS_ID="1:617462185423:ios:25b4e7209de9392e6ee6ba"
export CURRENT_DIR=$(pwd)

pnpm copy:prod
# pnpm bundle:ios

cd $CURRENT_DIR/packages/host/ios/fastlane && fastlane ios build
# cd $CURRENT_DIR/packages/host/ios/fastlane && bundle exec fastlane ios build
cp $CURRENT_DIR/packages/host/ios/fastlane/dist/host.ipa $CURRENT_DIR/tmp/app_test_$date.ipa

firebase appdistribution:distribute $CURRENT_DIR/tmp/app_test_$date.ipa  \
    --app "$FIREBASE_APP_IOS_ID"  \
    --release-notes "Something new ios" --groups "fis" \
    --token "$FIREBASE_TOKEN"

