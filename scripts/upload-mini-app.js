const Minio = require('minio');
const fs = require('fs');
const path = require('path');

const env = {
  IHANOI_MINI_APPS_STAFF_BUCKET: process.env['IHANOI_MINI_APPS_STAFF_BUCKET'],
  IHANOI_MINI_APPS_STAFF_KEY: process.env['IHANOI_MINI_APPS_STAFF_KEY'],
  IHANOI_MINI_APPS_STAFF_SECRET_KEY:
    process.env['IHANOI_MINI_APPS_STAFF_SECRET_KEY'],
  IHANOI_MINI_APPS_STAFF_URL: process.env['IHANOI_MINI_APPS_STAFF_URL'],
};

const minioClient = new Minio.Client({
  endPoint: env.IHANOI_MINI_APPS_STAFF_URL,
  accessKey: env.IHANOI_MINI_APPS_STAFF_KEY,
  secretKey: env.<PERSON><PERSON><PERSON><PERSON><PERSON>_MINI_APPS_STAFF_SECRET_KEY,
  useSSL: true,
});

const miniAppFolder = process.argv[2];
const miniAppFolderPath = path.join(__dirname, '..', 'build', miniAppFolder);

if (!miniAppFolder || !fs.existsSync(miniAppFolderPath)) {
  throw new Error(`Không tìm thấy thư mục build/${miniAppFolder}`);
}
const miniAppBase = miniAppFolder.split('@')[0];

async function uploadFolder(bucketName, localFolder, destinationPath) {
  // Read all files and subfolders in the local folder
  const items = fs.readdirSync(localFolder);

  for (const item of items) {
    const itemPath = path.join(localFolder, item);
    const s3Path = path.join(destinationPath, item).replace(/\\/g, '/'); // Replace backslashes with forward slashes for S3

    if (itemPath.includes('.map')) {
      continue;
    }

    if (fs.lstatSync(itemPath).isDirectory()) {
      // Recursively upload subfolders
      await uploadFolder(bucketName, itemPath, s3Path);
    } else {
      // Upload file
      await minioClient.fPutObject(
        bucketName,
        s3Path,
        itemPath,
        {},
        (err, etag) => {
          if (err) {
            console.error(`Error uploading ${itemPath}:`, err);
          } else {
            console.log(`Uploaded ${itemPath} to ${bucketName}/${s3Path}`);
          }
        }
      );
    }
  }
}

uploadFolder(
  env.IHANOI_MINI_APPS_STAFF_BUCKET,
  miniAppFolderPath,
  `${miniAppBase}/${miniAppFolder}`
);
