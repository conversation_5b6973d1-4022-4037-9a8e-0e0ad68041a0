# Super App Pro - Mobile App

```sh
rm -fr node_modules
rm -fr packages/host/node_modules
rm -fr packages/citizen/node_modules
rm -fr packages/auth/node_modules
rm -fr packages/dashboard/node_modules
rm -fr packages/catalog-server/node_modules
rm -fr packages/host/android/app/build
rm -fr packages/host/ios/build
rm -fr packages/host/build
```

```sh
brew install ruby
# Sau đó add path như trong output khi vào vào ~/.zshrc
export GEM_HOME="$HOME/.gem"
export PATH="$GEM_HOME/bin:$PATH"
gem install cocoapods --user-install
cd host/ios
bundle exec pod install
```
