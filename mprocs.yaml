procs:
  Host:
    shell: pnpm start:hn:host
    stop: SIGKILL
    autostart: false
  Auth:
    shell: pnpm start:hn:auth:dev
    stop: SIGKILL
  Dashboard:
    shell: pnpm start:hn:dashboard:dev
    stop: <PERSON>IG<PERSON>ILL
  # Catalog Server:
  #   shell: pnpm start:catalog-server
  #   stop: SIG<PERSON>ILL
  # Watch icons:
  #   shell: pnpm watch:icons
  #   stop: SIGKILL
  # Watch assets:
  #   shell: pnpm watch:assets
  #   stop: SIGKILL
  # Watch lotties:
  #   shell: pnpm watch:lotties
  #   stop: SIGKILL
  # Watch screens:
  #   shell: pnpm watch:screens
  #   stop: SIGKILL
  # Watch stores:
  #   shell: pnpm watch:stores
  #   stop: SIG<PERSON><PERSON>L
  # Watch utils:
  #   shell: pnpm watch:utils
  #   stop: SIG<PERSON><PERSON><PERSON>
  # Watch api:
  #   shell: pnpm watch:api
  #   stop: SIGKILL
